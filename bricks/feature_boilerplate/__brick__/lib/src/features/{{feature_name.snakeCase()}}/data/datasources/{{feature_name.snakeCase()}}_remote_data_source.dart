import 'dart:convert' show json;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class {{feature_name.pascalCase()}}RemoteDataSource {
  final HttpManager httpManager;

  {{feature_name.pascalCase()}}RemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> fetch{{feature_name.pascalCase()}}() async {
    final response = await httpManager.request(
      path: Api().baseUrl,
      method: HttpMethods.get,
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final {{feature_name.camelCase()}}DatasourceProvider = Provider<{{feature_name.pascalCase()}}RemoteDataSource>((ref) {
  return {{feature_name.pascalCase()}}RemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
