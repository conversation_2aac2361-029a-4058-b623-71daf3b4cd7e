import 'package:kurdsat/src/features/{{feature_name.snakeCase()}}/data/models/{{feature_name.snakeCase()}}_model_data.dart' show {{feature_name.pascalCase()}}ModelData;
import 'package:kurdsat/src/features/{{feature_name.snakeCase()}}/domain/entities/{{feature_name.snakeCase()}}_entity.dart' show {{feature_name.pascalCase()}}Entity;


class {{feature_name.pascalCase()}}Mapper {

  final {{feature_name.pascalCase()}}ModelData {{feature_name.camelCase()}}ModelData;

  {{feature_name.pascalCase()}}Mapper({required this.{{feature_name.camelCase()}}ModelData});
    

  List<{{feature_name.pascalCase()}}Entity> to{{feature_name.pascalCase()}}Entity() {
    return [];
  }
}