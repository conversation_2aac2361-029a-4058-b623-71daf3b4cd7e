import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/{{feature_name.snakeCase()}}/domain/entities/{{feature_name.snakeCase()}}_entity.dart" show {{feature_name.pascalCase()}}Entity;
import "package:kurdsat/src/features/{{feature_name.snakeCase()}}/domain/repositories/{{feature_name.snakeCase()}}_repository.dart";


class {{feature_name.pascalCase()}}Notifier extends AsyncNotifier<List<{{feature_name.pascalCase()}}Entity>> {
  late {{feature_name.pascalCase()}}Repository _{{feature_name.camelCase()}}RepositoryProvider;

  @override
  FutureOr<List<{{feature_name.pascalCase()}}Entity>> build() async {
    _{{feature_name.camelCase()}}RepositoryProvider = ref.read({{feature_name.camelCase()}}RepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _{{feature_name.camelCase()}}RepositoryProvider.fetch{{feature_name.pascalCase()}}();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final {{feature_name.camelCase()}}NotifierProvider = AsyncNotifierProvider<{{feature_name.pascalCase()}}Notifier, List<{{feature_name.pascalCase()}}Entity>>({{feature_name.pascalCase()}}Notifier.new);
