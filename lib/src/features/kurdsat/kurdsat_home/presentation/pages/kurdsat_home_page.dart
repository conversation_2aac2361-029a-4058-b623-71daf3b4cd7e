import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart' show TextView;

class KurdsatHomePage extends ConsumerStatefulWidget {
  const KurdsatHomePage({super.key});

  @override
  ConsumerState<KurdsatHomePage> createState() => _KurdsatHomePageState();
}

class _KurdsatHomePageState extends ConsumerState<KurdsatHomePage> {
  @override
  Widget build(BuildContext context) {
    // final kurdsatHomeState = ref.watch(kurdsatHomeNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBarView(title: "Kurdsat"),

      body: DefaultTextStyle(
        style: TextStyle(
          color: Color(0xffE9F2FB),
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                24.verticalSpace,

                // // Continue
                // kurdsatHomeState.maybeWhen(
                //   orElse: () => Text("Loading..."),
                //   data: (data) => Text("Data loaded: ${data.length} items"),
                //   loading: () => CircularProgressIndicator(),
                //   error: (error, stack) => Text("Error: $error"),
                // ),
                Text(
                  "Kurdsat Home Page",
                  style: TextStyle(
                    color: Color(0xffE9F2FB),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                24.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
