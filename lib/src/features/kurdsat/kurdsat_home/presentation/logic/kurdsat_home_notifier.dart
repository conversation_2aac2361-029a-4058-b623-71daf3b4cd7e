import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/kurdsat/kurdsat_home/domain/entities/kurdsat_home_entity.dart" show KurdsatHomeEntity;
import "package:kurdsat/src/features/kurdsat/kurdsat_home/domain/repositories/kurdsat_home_repository.dart";

class KurdsatHomeNotifier extends AsyncNotifier<List<KurdsatHomeEntity>> {
  late KurdsatHomeRepository _kurdsatHomeRepositoryProvider;

  @override
  FutureOr<List<KurdsatHomeEntity>> build() async {
    _kurdsatHomeRepositoryProvider = ref.read(kurdsatHomeRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _kurdsatHomeRepositoryProvider.fetchKurdsatHome();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final kurdsatHomeNotifierProvider = AsyncNotifierProvider<KurdsatHomeNotifier, List<KurdsatHomeEntity>>(KurdsatHomeNotifier.new);
