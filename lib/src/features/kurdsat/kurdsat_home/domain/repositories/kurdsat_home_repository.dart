import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/data/datasources/kurdsat_home_remote_data_source.dart' show kurdsatHomeDatasourceProvider;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/data/repositories/kurdsat_home_reposetory_impl.dart' show KurdsatHomeRepositoryImpl;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/domain/entities/kurdsat_home_entity.dart' show KurdsatHomeEntity;

abstract class KurdsatHomeRepository {
  Future<Either<ErrorModel, List<KurdsatHomeEntity>>> fetchKurdsatHome();
}

final kurdsatHomeRepositoryProvider = Provider<KurdsatHomeRepository>((ref) {
  return KurdsatHomeRepositoryImpl(kurdsatHomeRemoteDataSource: ref.read(kurdsatHomeDatasourceProvider));
});
