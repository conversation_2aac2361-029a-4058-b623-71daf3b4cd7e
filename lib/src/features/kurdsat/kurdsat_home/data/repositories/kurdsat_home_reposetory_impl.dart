import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/data/datasources/kurdsat_home_remote_data_source.dart' show KurdsatHomeRemoteDataSource;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/data/mappers/kurdsat_home_mapper.dart' show KurdsatHomeMapper;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/data/models/kurdsat_home_model_data.dart' show KurdsatHomeModelData;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/domain/entities/kurdsat_home_entity.dart' show KurdsatHomeEntity;
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/domain/repositories/kurdsat_home_repository.dart' show KurdsatHomeRepository;

class KurdsatHomeRepositoryImpl implements KurdsatHomeRepository {
  final KurdsatHomeRemoteDataSource kurdsatHomeRemoteDataSource;

  KurdsatHomeRepositoryImpl({required this.kurdsatHomeRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<KurdsatHomeEntity>>> fetchKurdsatHome() async {
    try {
      final result = await kurdsatHomeRemoteDataSource.fetchKurdsatHome();

      KurdsatHomeModelData model = KurdsatHomeModelData.fromJson(result);

      KurdsatHomeMapper mapper = KurdsatHomeMapper(kurdsatHomeModelData: model);

      final List<KurdsatHomeEntity> finalResponse = mapper.toKurdsatHomeEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
