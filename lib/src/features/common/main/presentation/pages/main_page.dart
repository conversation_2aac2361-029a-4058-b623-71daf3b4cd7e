import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart';
import 'package:kurdsat/src/features/common/main/presentation/widgets/kurdsat_news_latest_news.dart' show KurdsatNewsLatestNews;
import 'package:kurdsat/src/features/common/main/presentation/widgets/kurdsat_pinned_article.dart';
import 'package:kurdsat/src/features/common/main/presentation/widgets/tv_and_radio_list.dart';

class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  ConsumerState<MainPage> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBarView(
        title: "Kurdsat",
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            32.verticalSpace,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TvAndRadioList(),
            ),
            32.verticalSpace,
            KurdsatNewsLatestNews(),
            16.verticalSpace,
            KurdsatPinnedArticle(),
            32.verticalSpace,
          ],
        ),
      ),
    );
  }
}
