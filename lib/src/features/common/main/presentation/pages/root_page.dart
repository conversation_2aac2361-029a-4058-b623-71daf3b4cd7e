import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' show BottomNavigationBar, BottomNavigationBarType, BuildContext, Color, Colors, FontWeight, Icon, Icons, InkRipple, Scaffold, TextStyle, Theme, Widget;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show ConsumerState, ConsumerStatefulWidget;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kurdsat/src/core/utils/services/notification_service.dart' show NotificationService;
import 'package:kurdsat/src/features/common/explore/presentation/pages/explore_page.dart' show ExplorePage;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/logic/live_broadcast_notifier.dart' show liveBroadcastNotifierProvider;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/pages/live_broadcasts_page.dart' show LiveBroadcastsPage;
import 'package:kurdsat/src/features/common/main/presentation/pages/main_page.dart' show MainPage;
import 'package:kurdsat/src/features/common/settings/presentation/pages/settings_page.dart' show SettingsPage;

class RootPage extends ConsumerStatefulWidget {
  const RootPage({super.key});

  @override
  ConsumerState<RootPage> createState() => _RootPageState();
}

class _RootPageState extends ConsumerState<RootPage> {
  int _selectedIndex = 0;

  final List<Widget> widgetOptions = const [
    MainPage(),
    LiveBroadcastsPage(),
    ExplorePage(),
    SettingsPage(),
  ];

  void _onItemTapped(int index) {
    if (_selectedIndex != index) HapticFeedback.lightImpact();

    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(liveBroadcastNotifierProvider.notifier).getLiveBroadcasts();
    });

    _getToken();
  }

  Future<void> _getToken() async {
    String? token = await NotificationService().getToken();
    log("FCM Token: $token");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widgetOptions.elementAt(_selectedIndex),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Color(0xFF192028),
          splashFactory: InkRipple.splashFactory,
          highlightColor: Colors.transparent,
        ),
        child: BottomNavigationBar(
          backgroundColor: Color(0xff14191F),
          enableFeedback: true,
          elevation: 0,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          type: BottomNavigationBarType.fixed,
          selectedItemColor: Color(0xff3693FF),
          unselectedItemColor: Color(0xff8396AF),
          items: <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: SvgPicture.asset(
                'assets/icons/Home.svg',
                colorFilter: ColorFilter.mode(
                  Color(0xff8396AF),
                  BlendMode.srcIn,
                ),
              ),
              activeIcon: SvgPicture.asset('assets/icons/Home.svg'),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/icons/live_icon.svg'),
              label: 'Live',
            ),
            BottomNavigationBarItem(
              activeIcon: SvgPicture.asset('assets/icons/Search.svg'),
              icon: Icon(CupertinoIcons.search),
              label: 'Explore',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.menu),
              label: 'Menu',
            ),
          ],
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
        ),
      ),
    );
  }
}
