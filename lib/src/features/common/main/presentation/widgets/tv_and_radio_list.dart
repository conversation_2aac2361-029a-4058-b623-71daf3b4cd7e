import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity, BroadcastIdentifierEnum;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/logic/live_broadcast_notifier.dart' show liveBroadcastNotifierProvider;
import 'package:kurdsat/src/features/common/main/presentation/widgets/image_card_view.dart';

class TvAndRadioList extends StatelessWidget {
  const TvAndRadioList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      clipBehavior: Clip.none,
      scrollDirection: Axis.horizontal,
      child: Row(
        spacing: 16.w,
        children: [
          TvList(),
          RadiList(),
        ],
      ),
    );
  }
}

class RadiList extends ConsumerStatefulWidget {
  const RadiList({
    super.key,
  });

  @override
  ConsumerState<RadiList> createState() => _RadiListState();
}

class _RadiListState extends ConsumerState<RadiList> {
  @override
  Widget build(BuildContext context) {
    final liveStreamState = ref.watch(liveBroadcastNotifierProvider);

    return liveStreamState.maybeWhen(
      orElse: () => SizedBox(),
      data: (List<StrapiItemEntity<BroadcastEntity>> broadcasts) {
        return Row(
          spacing: 16.w,
          children: [
            ImageCardView(
              onPressed: () {
                final BroadcastEntity broadcast = broadcasts.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.radio963).attributes;
                context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
              },
              imageUrl: "assets/tv_logos/radio-96.3.png",
              title: "Dangi Kurdsat",
            ),
            ImageCardView(
              onPressed: () {
                final BroadcastEntity broadcast = broadcasts.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.radio957).attributes;
                context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
              },
              imageUrl: "assets/tv_logos/radio-95.7.png",
              title: "Dangi Kurdsat",
            ),
          ],
        );
      },
    );
  }
}

class TvList extends StatelessWidget {
  const TvList({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 16.w,
      children: [
        ImageCardView(
          onPressed: () {
            context.pushNamed(RouteNameEnum.kurdsatNewsHome.name);
          },
          title: "Kurdsat News",
          imageUrl: "assets/tv_logos/kurdsat-news.png",
        ),
        ImageCardView(
          onPressed: () {
            context.pushNamed(RouteNameEnum.kurdsatHome.name);
          },
          imageUrl: "assets/tv_logos/kurdsat.png",
          title: "Kurdsat",
        ),
        ImageCardView(
          onPressed: () {
            context.pushNamed(RouteNameEnum.kurdbinHomePage.name);
          },
          title: "Kurdbin",
          imageUrl: "assets/tv_logos/kurdbin.png",
        ),
      ],
    );
  }
}
