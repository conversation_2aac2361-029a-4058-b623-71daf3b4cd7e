import 'package:flutter/cupertino.dart' show CupertinoButton;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart' show TextView;

class ImageCardView extends StatelessWidget {
  final Function()? onPressed;
  final String? imageUrl;
  final String? title;

  const ImageCardView({
    super.key,
    this.onPressed,
    this.imageUrl,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      minimumSize: const Size(0, 0),
      child: Container(
        decoration: ShapeDecoration(
          color: Color(0xff14191F),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(32),
            side: BorderSide(
              color: Color(0xff1F2A3A),
              width: 1,
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
          child: Column(
            children: [
              if (imageUrl != null)
                Image.asset(
                  imageUrl!,
                  scale: 1.8,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholder();
                  },
                )
              else
                _buildPlaceholder(),
              if (title != null) ...[
                10.verticalSpace,
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: TextView(
                    text: title!,
                    style: TextStyle(
                      color: Color(0xffEFF2F5),
                      fontSize: 12.sp,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: FittedBox(
              child: Icon(
                Icons.image_rounded,
                color: Colors.grey[500],
              ),
            ),
          ),
        ),
      ],
    );
  }
}



              // IntrinsicHeight(
              //   child: ListView(
              //     physics: NeverScrollableScrollPhysics(),
              //     shrinkWrap: true,

              //     scrollDirection: Axis.horizontal,
              //     // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, crossAxisSpacing: 10),
              //     children: [
              //       InkWell(
              //         onTap: () {
              //           context.push("/kurdsat-news");
              //         },
              //         child: Container(
              //           decoration: BoxDecoration(
              //             color: Color(0xff14191f),
              //             borderRadius: BorderRadius.circular(10.r),
              //           ),
              //           child: Center(
              //             child: TextView(
              //               text: "Kurdsat\nNews",
              //               textAlignment: TextAlign.center,
              //               style: TextStyle(
              //                 color: Color(0xffEFF2F5),
              //                 fontSize: 16.sp,
              //                 fontWeight: FontWeight.bold,
              //               ),
              //             ),
              //           ),
              //         ),
              //       ),
              //       InkWell(
              //         onTap: () {
              //           context.push("/kurdsat");
              //         },
              //         child: Container(
              //           decoration: BoxDecoration(
              //             color: Color(0xff14191f),
              //             borderRadius: BorderRadius.circular(10.r),
              //           ),
              //           child: Center(child: TextView(text: "Kurdsat")),
              //         ),
              //       ),
              //       InkWell(
              //         onTap: () {
              //           context.pushNamed(RouteNameEnum.kurdbinHomePage.name);
              //         },
              //         child: Container(
              //           decoration: BoxDecoration(
              //             color: Color(0xff14191f),
              //             borderRadius: BorderRadius.circular(10.r),
              //           ),
              //           child: Center(child: TextView(text: "Kurdbin")),
              //         ),
              //       ),
              //     ],
              //   ),
              // ),
              // InkWell(
              //   onTap: () {
              //     context.push("/kurdsat-news");
              //   },
              //   child: Container(
              //     decoration: BoxDecoration(
              //       color: Color(0xff14191f),
              //       borderRadius: BorderRadius.circular(10.r),
              //     ),
              //     child: Center(
              //       child: TextView(
              //         text: "Kurdsat\nNews",
              //         textAlignment: TextAlign.center,
              //         style: TextStyle(
              //           color: Color(0xffEFF2F5),
              //           fontSize: 16.sp,
              //           fontWeight: FontWeight.bold,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
              // InkWell(
              //   onTap: () {
              //     context.push("/kurdsat");
              //   },
              //   child: Container(
              //     decoration: BoxDecoration(
              //       color: Color(0xff14191f),
              //       borderRadius: BorderRadius.circular(10.r),
              //     ),
              //     child: Center(child: TextView(text: "Kurdsat")),
              //   ),
              // ),
              // InkWell(
              //   onTap: () {
              //     context.pushNamed(RouteNameEnum.kurdbinHomePage.name);
              //   },
              //   child: Container(
              //     decoration: BoxDecoration(
              //       color: Color(0xff14191f),
              //       borderRadius: BorderRadius.circular(10.r),
              //     ),
              //     child: Center(child: TextView(text: "Kurdbin")),
              //   ),
              // ),