import 'package:flutter/cupertino.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerView extends StatelessWidget {
  final Widget child;
  const ShimmerView({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF14191F),
      highlightColor: const Color(0xFF192028),
      period: Duration(milliseconds: 1000),
      child: child,
    );
  }
}
