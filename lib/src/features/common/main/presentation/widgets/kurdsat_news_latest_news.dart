import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show ConsumerStatefulWidget, ConsumerState, AsyncValueX;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart' show RouteNameEnum;
import 'package:kurdsat/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import 'package:kurdsat/src/core/common/widgets/empty_state_view.dart';
import 'package:kurdsat/src/core/common/widgets/error_view.dart' show ErrorView;
import 'package:kurdsat/src/core/common/data/models/media_item_model.dart' show MediaItemDataModel;
import 'package:kurdsat/src/features/common/main/presentation/logic/latest_news_notifier.dart' show latestNewsNotifierProvider;
import 'package:kurdsat/src/core/common/widgets/article_card_carosel.dart';

import '../../../../../core/common/widgets/news_card/news_card_view.dart';
import '../../../../../core/constants/const.dart';

class KurdsatNewsLatestNews extends ConsumerStatefulWidget {
  const KurdsatNewsLatestNews({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _KurdsatNewsLatestNewsState();
}

class _KurdsatNewsLatestNewsState extends ConsumerState<KurdsatNewsLatestNews> {
  @override
  Widget build(BuildContext context) {
    final latestNewsState = ref.watch(latestNewsNotifierProvider);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: CategoryTitleSection(
            title: "Kurdsat News",
            onPressed: () {
              context.pushNamed(RouteNameEnum.kurdsatNewsHome.name);
            },
          ),
        ),
        16.verticalSpace,

        latestNewsState.maybeWhen(
          orElse: () => ErrorView(),
          data: (List<MediaItemDataModel> listOfLatestModel) {
            if (listOfLatestModel.isEmpty) {
              return EmptyStateView();
            }
            return ArticleCardCarosel(
              items: listOfLatestModel.map((MediaItemDataModel article) {
                return NewsCardView(
                  imageUrl: "$kBaseAssetUrl${article.imageUrl}",
                  dutation: article.date,
                  title: article.title ?? "",
                  articleId: article.id,
                );
              }).toList(),
              carouselHeight: 300.r,
            );
          },
          loading: () => ArticleCardCaroselShimmer(),
          error: (error, stack) => ErrorView(),
        ),
      ],
    );
  }
}
