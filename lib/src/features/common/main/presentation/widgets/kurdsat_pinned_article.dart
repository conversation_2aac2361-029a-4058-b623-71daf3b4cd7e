import 'package:flutter/cupertino.dart' show BuildContext, Widget, EdgeInsets, Padding, Column;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart' show RouteNameEnum;
import 'package:kurdsat/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import 'package:kurdsat/src/core/common/widgets/empty_state_view.dart' show EmptyStateView;
import 'package:kurdsat/src/core/common/widgets/error_view.dart' show ErrorView;
import 'package:kurdsat/src/core/common/widgets/news_tile_view.dart' show NewsTileView, NewsTileShimmer;
import 'package:kurdsat/src/core/constants/const.dart' show kBaseAssetUrl;
import 'package:kurdsat/src/core/extensions/date_formatter.dart';
import 'package:kurdsat/src/core/common/data/models/media_item_model.dart' show MediaItemDataModel;
import 'package:kurdsat/src/features/common/main/presentation/logic/latest_news_notifier.dart' show latestNewsNotifierProvider;
import 'package:kurdsat/src/features/common/main/presentation/widgets/shimmer_view.dart' show ShimmerView;

class KurdsatPinnedArticle extends ConsumerStatefulWidget {
  const KurdsatPinnedArticle({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _KurdsatPinnedArticleState();
}

class _KurdsatPinnedArticleState extends ConsumerState<KurdsatPinnedArticle> {
  @override
  Widget build(BuildContext context) {
    final latestNewsState = ref.watch(latestNewsNotifierProvider);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: CategoryTitleSection(
            title: "Kurdsat",
            onPressed: () {
              context.pushNamed(RouteNameEnum.kurdsatHome.name);
            },
          ),
        ),

        16.verticalSpace,

        latestNewsState.maybeWhen(
          orElse: () => ErrorView(),
          data: (data) {
            if (data.isEmpty) {
              return EmptyStateView();
            }
            List<MediaItemDataModel> reversedData = data.reversed.toList();
            return Column(
              children: reversedData.map((MediaItemDataModel article) {
                return NewsTileView(
                  articleId: article.id,
                  imageUrl: "$kBaseAssetUrl${article.imageUrl}",
                  title: article.title ?? "",
                  subtitle: article.date?.getSimpleRelativeTime() ?? "",
                );
              }).toList(),
            );
          },
          loading: () {
            return ShimmerView(
              child: Column(
                children: [
                  for (int i = 0; i < 4; i++) ...[
                    NewsTileShimmer(),
                  ],
                ],
              ),
            );
          },
          error: (error, stack) => ErrorView(),
        ),
        32.verticalSpace,
      ],
    );
  }
}
