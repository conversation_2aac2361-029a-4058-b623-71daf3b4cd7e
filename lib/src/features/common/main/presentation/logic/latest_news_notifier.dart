import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/core/common/data/models/media_item_model.dart" show MediaItemDataModel;
import "package:kurdsat/src/features/common/main/domain/repositories/latest_news_repository.dart";

class LatestNewsNotifier extends AsyncNotifier<List<MediaItemDataModel>> {
  late LatestNewsRepository _latestNewsRepositoryProvider;

  @override
  FutureOr<List<MediaItemDataModel>> build() async {
    _latestNewsRepositoryProvider = ref.read(latestNewsRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _latestNewsRepositoryProvider.fetchLatestNews();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }

  // make the state loading
  void makeItLoading() {
    state = const AsyncValue.loading();
  }
}

final latestNewsNotifierProvider = AsyncNotifierProvider<LatestNewsNotifier, List<MediaItemDataModel>>(LatestNewsNotifier.new);
