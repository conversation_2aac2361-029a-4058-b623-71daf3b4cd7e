import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/common/main/data/datasources/latest_news_remote_data_source.dart' show latestNewsDatasourceProvider;
import 'package:kurdsat/src/features/common/main/data/repositories/latest_news_reposetory_impl.dart' show LatestNewsRepositoryImpl;
import 'package:kurdsat/src/core/common/data/models/media_item_model.dart' show MediaItemDataModel;

abstract class LatestNewsRepository {
  Future<Either<ErrorModel, List<MediaItemDataModel>>> fetchLatestNews();
}

final latestNewsRepositoryProvider = Provider<LatestNewsRepository>((ref) {
  return LatestNewsRepositoryImpl(latestNewsRemoteDataSource: ref.read(latestNewsDatasourceProvider));
});
