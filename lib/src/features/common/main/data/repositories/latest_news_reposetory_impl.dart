import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/common/main/data/datasources/latest_news_remote_data_source.dart' show LatestNewsRemoteDataSource;
import 'package:kurdsat/src/core/common/data/models/media_item_model.dart' show MediaItemDataModel, MediaItemResponseModel;
import 'package:kurdsat/src/features/common/main/domain/repositories/latest_news_repository.dart' show LatestNewsRepository;

class LatestNewsRepositoryImpl implements LatestNewsRepository {
  final LatestNewsRemoteDataSource latestNewsRemoteDataSource;

  LatestNewsRepositoryImpl({required this.latestNewsRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<MediaItemDataModel>>> fetchLatestNews() async {
    try {
      final result = await latestNewsRemoteDataSource.fetchLatestNews();

      final news = MediaItemResponseModel.fromJson(result);

      return Right(news.data);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
