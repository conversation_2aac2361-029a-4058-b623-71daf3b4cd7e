import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/common/explore/data/datasources/explore_remote_data_source.dart' show ExploreRemoteDataSource;
import 'package:kurdsat/src/features/common/explore/data/mappers/explore_mapper.dart' show ExploreMapper;
import 'package:kurdsat/src/features/common/explore/data/models/explore_model_data.dart' show ExploreModelData;
import 'package:kurdsat/src/features/common/explore/domain/entities/explore_entity.dart' show ExploreEntity;
import 'package:kurdsat/src/features/common/explore/domain/repositories/explore_repository.dart' show ExploreRepository;

class ExploreRepositoryImpl implements ExploreRepository {
  final ExploreRemoteDataSource exploreRemoteDataSource;

  ExploreRepositoryImpl({required this.exploreRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<ExploreEntity>>> fetchExplore() async {
    try {
      final result = await exploreRemoteDataSource.fetchExplore();

      ExploreModelData model = ExploreModelData.fromJson(result);

      ExploreMapper mapper = ExploreMapper(exploreModelData: model);

      final List<ExploreEntity> finalResponse = mapper.toExploreEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
