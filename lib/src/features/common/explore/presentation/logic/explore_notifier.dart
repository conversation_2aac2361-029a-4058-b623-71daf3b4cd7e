import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/common/explore/domain/entities/explore_entity.dart" show ExploreEntity;
import "package:kurdsat/src/features/common/explore/domain/repositories/explore_repository.dart";

class ExploreNotifier extends AsyncNotifier<List<ExploreEntity>> {
  late ExploreRepository _exploreRepositoryProvider;

  @override
  FutureOr<List<ExploreEntity>> build() async {
    _exploreRepositoryProvider = ref.read(exploreRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _exploreRepositoryProvider.fetchExplore();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final exploreNotifierProvider = AsyncNotifierProvider<ExploreNotifier, List<ExploreEntity>>(ExploreNotifier.new);
