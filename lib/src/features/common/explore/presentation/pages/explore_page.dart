import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart' show AppBarView;

import 'package:kurdsat/src/features/common/explore/presentation/logic/explore_notifier.dart' show exploreNotifierProvider;

class ExplorePage extends ConsumerStatefulWidget {
  const ExplorePage({super.key});

  @override
  ConsumerState<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends ConsumerState<ExplorePage> {
  @override
  Widget build(BuildContext context) {
    final exploreState = ref.watch(exploreNotifierProvider);
    return Scaffold(
      appBar: AppBarView(title: "Explore"),
      backgroundColor: Color(0xff0A0C10),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,

            // Continue
            exploreState.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => Text("Explore Page"),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => Text("Error: $error"),
            ),
          ],
        ),
      ),
    );
  }
}
