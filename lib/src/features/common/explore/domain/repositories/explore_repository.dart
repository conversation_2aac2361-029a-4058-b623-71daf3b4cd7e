import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/common/explore/data/datasources/explore_remote_data_source.dart' show exploreDatasourceProvider;
import 'package:kurdsat/src/features/common/explore/data/repositories/explore_reposetory_impl.dart' show ExploreRepositoryImpl;
import 'package:kurdsat/src/features/common/explore/domain/entities/explore_entity.dart' show ExploreEntity;

abstract class ExploreRepository {
  Future<Either<ErrorModel, List<ExploreEntity>>> fetchExplore();
}

final exploreRepositoryProvider = Provider<ExploreRepository>((ref) {
  return ExploreRepositoryImpl(exploreRemoteDataSource: ref.read(exploreDatasourceProvider));
});
