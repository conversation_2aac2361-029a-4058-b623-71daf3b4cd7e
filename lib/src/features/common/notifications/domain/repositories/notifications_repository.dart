import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/common/notifications/data/datasources/notifications_remote_data_source.dart' show notificationsDatasourceProvider;
import 'package:kurdsat/src/features/common/notifications/data/repositories/notifications_reposetory_impl.dart' show NotificationsRepositoryImpl;
import 'package:kurdsat/src/features/common/notifications/domain/entities/notifications_entity.dart' show NotificationsEntity;

abstract class NotificationsRepository {
  Future<Either<ErrorModel, List<NotificationsEntity>>> fetchNotifications();
}

final notificationsRepositoryProvider = Provider<NotificationsRepository>((ref) {
  return NotificationsRepositoryImpl(notificationsRemoteDataSource: ref.read(notificationsDatasourceProvider));
});
