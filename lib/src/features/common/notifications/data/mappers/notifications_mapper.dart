import 'package:kurdsat/src/features/common/notifications/data/models/notifications_model_data.dart' show NotificationsModelData;
import 'package:kurdsat/src/features/common/notifications/domain/entities/notifications_entity.dart' show NotificationsEntity;

class NotificationsMapper {
  final NotificationsModelData notificationsModelData;

  NotificationsMapper({required this.notificationsModelData});

  List<NotificationsEntity> toNotificationsEntity() {
    return [];
  }
}
