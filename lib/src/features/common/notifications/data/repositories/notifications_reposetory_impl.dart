import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/common/notifications/data/datasources/notifications_remote_data_source.dart' show NotificationsRemoteDataSource;
import 'package:kurdsat/src/features/common/notifications/data/mappers/notifications_mapper.dart' show NotificationsMapper;
import 'package:kurdsat/src/features/common/notifications/data/models/notifications_model_data.dart' show NotificationsModelData;
import 'package:kurdsat/src/features/common/notifications/domain/entities/notifications_entity.dart' show NotificationsEntity;
import 'package:kurdsat/src/features/common/notifications/domain/repositories/notifications_repository.dart' show NotificationsRepository;

class NotificationsRepositoryImpl implements NotificationsRepository {
  final NotificationsRemoteDataSource notificationsRemoteDataSource;

  NotificationsRepositoryImpl({required this.notificationsRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<NotificationsEntity>>> fetchNotifications() async {
    try {
      final result = await notificationsRemoteDataSource.fetchNotifications();

      NotificationsModelData model = NotificationsModelData.fromJson(result);

      NotificationsMapper mapper = NotificationsMapper(notificationsModelData: model);

      final List<NotificationsEntity> finalResponse = mapper.toNotificationsEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
