import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/common/notifications/domain/entities/notifications_entity.dart" show NotificationsEntity;
import "package:kurdsat/src/features/common/notifications/domain/repositories/notifications_repository.dart";

class NotificationsNotifier extends AsyncNotifier<List<NotificationsEntity>> {
  late NotificationsRepository _notificationsRepositoryProvider;

  @override
  FutureOr<List<NotificationsEntity>> build() async {
    _notificationsRepositoryProvider = ref.read(notificationsRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _notificationsRepositoryProvider.fetchNotifications();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final notificationsNotifierProvider = AsyncNotifierProvider<NotificationsNotifier, List<NotificationsEntity>>(NotificationsNotifier.new);
