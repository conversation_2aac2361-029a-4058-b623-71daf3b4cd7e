import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart' show AppBarView;
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';

import 'package:kurdsat/src/features/common/notifications/presentation/logic/notifications_notifier.dart' show notificationsNotifierProvider;

class NotificationsPage extends ConsumerStatefulWidget {
  const NotificationsPage({super.key});

  @override
  ConsumerState<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends ConsumerState<NotificationsPage> {
  @override
  Widget build(BuildContext context) {
    final notificationsState = ref.watch(notificationsNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBarView(
        title: "Notifications",
        showNotificationIcon: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,
            // Continue
            TextView(
              text: "Notifications Page",
              style: TextStyle(color: Colors.white, fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            // notificationsState.maybeWhen(
            //   orElse: () => Text("Loading..."),
            //   data: (data) => Text("Data loaded: ${data.length} items"),
            //   loading: () => CircularProgressIndicator(),
            //   error: (error, stack) => Text("Error: $error"),
            // ),
          ],
        ),
      ),
    );
  }
}
