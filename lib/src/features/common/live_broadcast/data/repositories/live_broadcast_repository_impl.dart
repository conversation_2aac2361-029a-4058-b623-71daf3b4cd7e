import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart';
import 'package:kurdsat/src/core/common/data/models/general_model.dart';

import 'package:kurdsat/src/features/common/live_broadcast/domain/repositories/live_broadcast_repository.dart';
import 'package:kurdsat/src/features/common/live_broadcast/data/datasources/live_broadcast_remote_datasource.dart';
import 'package:kurdsat/src/features/common/live_broadcast/data/mappers/live_broadcast_mapper.dart';

import '../../../../../core/common/domain/entities/strapi_response_entity.dart' show StrapiListResponseEntity;
import '../../domain/entities/broadcast_entity.dart' show BroadcastEntity;
import '../models/broadcast_model.dart' show BroadcastModel;

class LiveBroadcastRepositoryImpl implements LiveBroadcastRepository {
  final LiveBroadcastRemoteDataSource remoteDataSource;

  LiveBroadcastRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<ErrorModel, StrapiListResponseEntity<BroadcastEntity>>> getLiveBroadcasts() async {
    try {
      final responseModel = await remoteDataSource.getLiveBroadcasts();

      final broadcastResponse = StrapiListResponse<BroadcastModel>.fromJson(
        responseModel,
        (json) => BroadcastModel.fromJson(json as Map<String, dynamic>),
      );

      final LiveBroadcastMapper mapper = LiveBroadcastMapper();

      final StrapiListResponseEntity<BroadcastEntity> entityResponse = mapper.toStrapiListEntity(broadcastResponse);

      return Right(entityResponse);
    } catch (e) {
      return Left(ErrorModel.fromException(e));
    }
  }
}
