import 'package:json_annotation/json_annotation.dart';
import 'package:kurdsat/src/core/common/data/models/general_model.dart';

import 'channel_type_model.dart' show ChannelTypeModel;
import 'thumbnail_model.dart' show ThumbnailModel;

part 'broadcast_model.g.dart';

@JsonSerializable()
class BroadcastModel {
  final int? id;
  final String? title;
  final String? link;
  final String? identifier;
  @JsonKey(name: 'channel_type')
  final StrapiResponse<ChannelTypeModel>? channelType;
  final StrapiResponse<ThumbnailModel>? image;
  final StrapiResponse<ThumbnailModel>? logo;

  const BroadcastModel({
    this.id,
    this.title,
    this.link,
    this.identifier,
    this.channelType,
    this.image,
    this.logo,
  });

  factory BroadcastModel.fromJson(Map<String, dynamic> json) => _$BroadcastModelFromJson(json);

  Map<String, dynamic> toJson() => _$BroadcastModelToJson(this);
}
