// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'broadcast_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BroadcastModel _$BroadcastModelFromJson(
  Map<String, dynamic> json,
) => BroadcastModel(
  id: (json['id'] as num?)?.toInt(),
  title: json['title'] as String?,
  link: json['link'] as String?,
  identifier: json['identifier'] as String?,
  channelType:
      json['channel_type'] == null
          ? null
          : StrapiResponse<ChannelTypeModel>.fromJson(
            json['channel_type'] as Map<String, dynamic>,
            (value) => ChannelTypeModel.fromJson(value as Map<String, dynamic>),
          ),
  image:
      json['image'] == null
          ? null
          : StrapiResponse<ThumbnailModel>.fromJson(
            json['image'] as Map<String, dynamic>,
            (value) => ThumbnailModel.fromJson(value as Map<String, dynamic>),
          ),
  logo:
      json['logo'] == null
          ? null
          : StrapiResponse<ThumbnailModel>.fromJson(
            json['logo'] as Map<String, dynamic>,
            (value) => ThumbnailModel.fromJson(value as Map<String, dynamic>),
          ),
);

Map<String, dynamic> _$BroadcastModelToJson(BroadcastModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'link': instance.link,
      'identifier': instance.identifier,
      'channel_type': instance.channelType?.toJson((value) => value),
      'image': instance.image?.toJson((value) => value),
      'logo': instance.logo?.toJson((value) => value),
    };
