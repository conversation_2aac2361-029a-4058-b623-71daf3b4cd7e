import 'package:json_annotation/json_annotation.dart' show JsonSerializable;

part 'channel_type_model.g.dart';

@JsonSerializable()
class ChannelTypeModel {
  final String title; // "Radio" or "TV"

  const ChannelTypeModel({required this.title});

  factory ChannelTypeModel.fromJson(Map<String, dynamic> json) => _$ChannelTypeModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelTypeModelToJson(this);
}
