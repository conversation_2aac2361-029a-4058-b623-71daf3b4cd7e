import 'package:kurdsat/src/core/common/data/models/general_model.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart';
import 'package:kurdsat/src/features/common/live_broadcast/data/models/channel_type_model.dart' show ChannelTypeModel;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/channel_type_entity.dart' show ChannelTypeEntity;
import 'package:kurdsat/src/core/enums/channel_type_enum.dart' show ChannelTypeEnum;

import '../../domain/entities/broadcast_entity.dart' show BroadcastEntity, BroadcastIdentifierEnum;
import '../models/broadcast_model.dart' show BroadcastModel;
import '../models/thumbnail_model.dart' show ThumbnailModel;
import '../../domain/entities/thumbnail_entity.dart' show ThumbnailEntity;

class LiveBroadcastMapper {
  // Make these instance variables instead of static for better testability
  final ChannelTypeMapper _channelTypeMapper = ChannelTypeMapper();
  final ThumbnailMapper _thumbnailMapper = ThumbnailMapper();

  // ========== ENTITY CONVERSION ==========

  BroadcastEntity toEntity(BroadcastModel model) {
    return BroadcastEntity(
      id: model.id,
      title: model.title,
      link: model.link,
      identifier: BroadcastIdentifierEnum.fromString(model.identifier),
      channelType: model.channelType != null ? _mapChannelTypeResponseToEntity(model.channelType!) : null,
      image: model.image != null ? _mapThumbnailResponseToEntity(model.image!) : null,
      logo: model.logo != null ? _mapThumbnailResponseToEntity(model.logo!) : null,
    );
  }

  // ========== LIST CONVERSION METHODS ==========

  List<BroadcastEntity> toEntityList(List<BroadcastModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  // ========== STRAPI RESPONSE CONVERSION ==========

  StrapiListResponseEntity<BroadcastEntity> toStrapiListEntity(StrapiListResponse<BroadcastModel> response) {
    return StrapiListResponseEntity(
      data: response.data.map((item) => StrapiItemEntity(id: item.id, attributes: toEntity(item.attributes))).toList(),
      meta: response.meta,
    );
  }

  // ========== PRIVATE HELPER METHODS ==========

  StrapiResponseEntity<ChannelTypeEntity> _mapChannelTypeResponseToEntity(StrapiResponse<ChannelTypeModel> response) {
    return StrapiResponseEntity(
      data: StrapiItemEntity(
        id: response.data.id,
        attributes: _channelTypeMapper.toEntity(response.data.attributes),
      ),
      meta: response.meta,
    );
  }

  StrapiResponseEntity<ThumbnailEntity> _mapThumbnailResponseToEntity(StrapiResponse<ThumbnailModel> response) {
    return StrapiResponseEntity(
      data: StrapiItemEntity(
        id: response.data.id,
        attributes: _thumbnailMapper.toEntity(response.data.attributes),
      ),
      meta: response.meta,
    );
  }
}

// ============== CHILD MAPPERS ==============

class ChannelTypeMapper {
  ChannelTypeEntity toEntity(ChannelTypeModel model) {
    return ChannelTypeEntity(
      title: ChannelTypeEnum.fromString(model.title),
    );
  }
}

class ThumbnailMapper {
  ThumbnailEntity toEntity(ThumbnailModel model) {
    return ThumbnailEntity(
      url: model.url,
    );
  }
}

// ============== USAGE EXAMPLES ==============

class BroadcastService {
  final LiveBroadcastMapper _mapper = LiveBroadcastMapper();

  Future<StrapiListResponseEntity<BroadcastEntity>> getBroadcasts() async {
    // Get data from API (returns models)
    final apiResponse = await _getBroadcastsFromApi();

    // Convert to entities for business logic
    return _mapper.toStrapiListEntity(apiResponse);
  }

  Future<List<BroadcastEntity>> getBroadcastList() async {
    final response = await getBroadcasts();

    // Extract just the list of entities
    return response.data.map((item) => item.attributes).toList();
  }

  Future<List<BroadcastEntity>> getRadioChannels() async {
    final broadcasts = await getBroadcastList();

    return broadcasts.where((broadcast) {
      return broadcast.channelType?.data.attributes.title == ChannelTypeEnum.radio;
    }).toList();
  }

  Future<StrapiListResponse<BroadcastModel>> _getBroadcastsFromApi() async {
    // Your API call logic here
    final jsonResponse = {}; // Your API response

    return StrapiListResponse<BroadcastModel>.fromJson(
      jsonResponse as Map<String, dynamic>,
      (json) => BroadcastModel.fromJson(json as Map<String, dynamic>),
    );
  }
}
