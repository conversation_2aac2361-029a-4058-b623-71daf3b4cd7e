import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/constants/const.dart' show kToken;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart';
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart';

class LiveBroadcastRemoteDataSource {
  final HttpManager httpManager;

  LiveBroadcastRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> getLiveBroadcasts() async {
    final response = await httpManager.request(
      path: Api().kurdsatNewsChannels,
      method: HttpMethods.get,
      headers: {'Authorization': 'Bearer $kToken'},
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final liveBroadcastRemoteDataSourceProvider = Provider<LiveBroadcastRemoteDataSource>((ref) {
  return LiveBroadcastRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
