import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart' show AppBarView;
import 'package:kurdsat/src/core/common/widgets/empty_state_view.dart';
import 'package:kurdsat/src/core/common/widgets/error_view.dart';
import 'package:kurdsat/src/core/common/widgets/loading_view.dart' show LoadingView;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart';
import 'package:kurdsat/src/features/common/live_broadcast/presentation/logic/live_broadcast_notifier.dart' show liveBroadcastNotifierProvider;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/widgets/live_broadcast_grid.dart';
import 'package:kurdsat/src/core/enums/channel_type_enum.dart';

class LiveBroadcastsPage extends ConsumerStatefulWidget {
  const LiveBroadcastsPage({super.key});

  @override
  ConsumerState<LiveBroadcastsPage> createState() => _LiveBroadcastPageState();
}

class _LiveBroadcastPageState extends ConsumerState<LiveBroadcastsPage> {
  @override
  Widget build(BuildContext context) {
    final liveBroadcastState = ref.watch(liveBroadcastNotifierProvider);

    return Scaffold(
      appBar: AppBarView(title: "Choose a channel"),
      backgroundColor: Color(0xff0A0C10),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(liveBroadcastNotifierProvider.notifier).getLiveBroadcasts();
        },
        child: liveBroadcastState.when(
          data: (broadcasts) => _buildBody(broadcasts),
          loading: () => LoadingView(),
          error: (error, stackTrace) => ErrorView(),
        ),
      ),
    );
  }

  Widget _buildBody(List<StrapiItemEntity<BroadcastEntity>> broadcasts) {
    if (broadcasts.isEmpty || broadcasts == []) {
      return Center(child: EmptyStateView());
    }

    // Separate TV and Radio channels
    final tvChannels = broadcasts.where((b) => b.attributes.channelType?.data.attributes.title == ChannelTypeEnum.tv).toList();
    final radioChannels = broadcasts.where((b) => b.attributes.channelType?.data.attributes.title == ChannelTypeEnum.radio).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (tvChannels.isNotEmpty) ...[
            Text(
              'TV Channels',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            12.verticalSpace,
            LiveBroadcastGrid(broadcasts: tvChannels),
            24.verticalSpace,
          ],
          if (radioChannels.isNotEmpty) ...[
            Text(
              'Radio Channels',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            LiveBroadcastGrid(broadcasts: radioChannels),
          ],
        ],
      ),
    );
  }
}
