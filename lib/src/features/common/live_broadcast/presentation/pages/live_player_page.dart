import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart' show AppBarView;
import 'package:kurdsat/src/features/common/main/presentation/widgets/shimmer_view.dart';
import 'package:video_player/video_player.dart';
import 'package:just_audio/just_audio.dart';
import 'package:shimmer/shimmer.dart';
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart';
import 'package:kurdsat/src/core/enums/channel_type_enum.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';

class LivePlayerPage extends ConsumerStatefulWidget {
  final BroadcastEntity broadcast;

  const LivePlayerPage({
    super.key,
    required this.broadcast,
  });

  @override
  ConsumerState<LivePlayerPage> createState() => _LivePlayerPageState();
}

class _LivePlayerPageState extends ConsumerState<LivePlayerPage> {
  VideoPlayerController? _videoController;
  AudioPlayer? _audioPlayer;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _isPlaying = false;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    _videoController?.dispose();
    _audioPlayer?.dispose();
  }

  Future<void> _initializePlayer() async {
    final channelType = widget.broadcast.channelType?.data.attributes.title ?? ChannelTypeEnum.tv;
    final streamUrl = widget.broadcast.link;

    if (streamUrl == null || streamUrl.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Stream URL not available';
      });
      return;
    }

    try {
      if (channelType == ChannelTypeEnum.tv) {
        await _initializeVideoPlayer(streamUrl);
      } else {
        await _initializeAudioPlayer(streamUrl);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load stream: ${e.toString()}';
      });
    }
  }

  Future<void> _initializeVideoPlayer(String url) async {
    _videoController = VideoPlayerController.networkUrl(Uri.parse(url));

    await _videoController!.initialize();

    _videoController!.addListener(() {
      if (mounted) {
        setState(() {
          _isPlaying = _videoController!.value.isPlaying;
        });
      }
    });

    // Auto-play
    await _videoController!.play();

    setState(() {
      _isLoading = false;
      _isPlaying = true;
    });
  }

  Future<void> _initializeAudioPlayer(String url) async {
    _audioPlayer = AudioPlayer();

    // Set audio session for background playback
    await _audioPlayer!.setAudioSource(AudioSource.uri(Uri.parse(url)));

    _audioPlayer!.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    // Auto-play
    await _audioPlayer!.play();

    setState(() {
      _isLoading = false;
      _isPlaying = true;
    });
  }

  void _togglePlayPause() {
    final channelType = widget.broadcast.channelType?.data.attributes.title ?? ChannelTypeEnum.tv;

    if (channelType == ChannelTypeEnum.tv && _videoController != null) {
      if (_videoController!.value.isPlaying) {
        _videoController!.pause();
      } else {
        _videoController!.play();
      }
    } else if (_audioPlayer != null) {
      if (_isPlaying) {
        _audioPlayer!.pause();
        setState(() {
          _isPlaying = false;
        });
      } else {
        _audioPlayer!.play();
        setState(() {
          _isPlaying = true;
        });
      }
    }
  }

  void _toggleControlsVisibility() {
    setState(() {
      _showControls = !_showControls;
    });

    // Auto-hide controls after 3 seconds
    if (_showControls) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _showControls = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final ChannelTypeEnum channelType = widget.broadcast.channelType?.data.attributes.title ?? ChannelTypeEnum.tv;

    if (channelType == ChannelTypeEnum.radio) {
      return _buildPlayerContent(channelType);
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBarView(
        // if TV show live else show radio
        title: channelType == ChannelTypeEnum.tv ? 'Live TV' : 'Live Radio',
        showNotificationIcon: false,
      ),
      body: _isLoading
          ? TvShimmer()
          : Padding(
              padding: const EdgeInsets.all(20.0),
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: Container(
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),
                  child: Stack(
                    children: [
                      GestureDetector(
                        onTap: _toggleControlsVisibility,
                        child: VideoPlayer(_videoController!),
                      ),
                      if (_showControls)
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            padding: EdgeInsets.zero,
                            decoration: ShapeDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: ContinuousRectangleBorder(
                                borderRadius: BorderRadius.circular(32),
                              ),
                            ),
                            margin: const EdgeInsets.all(8),
                            width: double.infinity,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                children: [
                                  Container(
                                    decoration: ShapeDecoration(
                                      color: Color(0xff3693FF),
                                      shape: ContinuousRectangleBorder(
                                        borderRadius: BorderRadius.circular(32),
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                                    child: TextView(
                                      text: "LIVE",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                  8.horizontalSpace,
                                  CupertinoButton(
                                    padding: EdgeInsets.zero,
                                    minimumSize: Size(0, 0),
                                    onPressed: _togglePlayPause,
                                    child: Icon(
                                      _isPlaying ? Icons.pause : Icons.play_arrow,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                  Spacer(),
                                  CupertinoButton(
                                    minimumSize: Size(0, 0),
                                    padding: EdgeInsets.zero,
                                    onPressed: () {
                                      // _videoController.
                                    },
                                    child: Icon(
                                      Icons.fullscreen,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                  2.horizontalSpace,
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
    );

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBarView(
        // if TV show live else show radio
        title: channelType == ChannelTypeEnum.tv ? 'Live TV' : 'Live Radio',
        showNotificationIcon: false,
      ),
      body: Column(
        children: [
          // Main content
          Stack(
            children: [
              GestureDetector(
                onTap: _toggleControlsVisibility,
                child: _buildPlayerContent(channelType),
              ),
              if (_showControls)
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    width: double.infinity,
                    color: Colors.black.withOpacity(0.5),
                    child: IconButton(
                      onPressed: _togglePlayPause,
                      icon: Icon(
                        _isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          // Controls overlay
          // if (_showControls) _buildControlsOverlay(title, channelType),
        ],
      ),
    );
  }

  Widget _buildPlayerContent(ChannelTypeEnum channelType) {
    // if (_isLoading) {
    //   return _buildShimmerLoading(channelType);
    // }

    if (_hasError) {
      // return _buildErrorWidget();
      // return ErrorView.somethingWentWrong(onRefresh: () {});
    }

    if (channelType == ChannelTypeEnum.tv) {
      return _buildVideoPlayer();
    } else {
      return _buildAudioPlayer();
    }
  }

  Widget _buildVideoPlayer() {
    if (_videoController == null || !_videoController!.value.isInitialized) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 0),
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(shape: ContinuousRectangleBorder(borderRadius: BorderRadius.circular(32))),
        child: AspectRatio(
          aspectRatio: _videoController!.value.aspectRatio,
          child: VideoPlayer(_videoController!),
          // child: Image.network(kBaseAssetUrl + (widget.broadcast.logo?.data.attributes.url ?? "")),
        ),
      ),
    );
  }

  Widget _buildAudioPlayer() {
    return Scaffold(
      backgroundColor: const Color(0xff0A0C10),
      appBar: AppBarView(
        title: widget.broadcast.title ?? 'Radio Stream',
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.0.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            32.verticalSpace,
            AspectRatio(
              aspectRatio: 16 / 9,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Expanded(
                      child: Image.asset(
                        widget.broadcast.identifier == BroadcastIdentifierEnum.radio963 ? "assets/tv_logos/radio-96.3.png" : "assets/tv_logos/radio-95.7.png",
                        fit: BoxFit.contain,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.zero,
                      decoration: ShapeDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                      ),
                      margin: const EdgeInsets.all(8),
                      width: double.infinity,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Container(
                              decoration: ShapeDecoration(
                                color: Color(0xff3693FF),
                                shape: ContinuousRectangleBorder(
                                  borderRadius: BorderRadius.circular(32),
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                              child: TextView(
                                text: "LIVE",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            8.horizontalSpace,
                            CupertinoButton(
                              padding: EdgeInsets.zero,
                              minimumSize: Size(0, 0),
                              onPressed: _togglePlayPause,
                              child: Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            Spacer(),
                            CupertinoButton(
                              minimumSize: Size(0, 0),
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                // _videoController.
                              },
                              child: Icon(
                                Icons.fullscreen,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                            2.horizontalSpace,
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 40),

            // // Title
            // DefaultTextStyle(
            //   style: const TextStyle(
            //     color: Colors.white,
            //     fontSize: 24,
            //     fontWeight: FontWeight.bold,
            //   ),
            //   child: TextView(
            //     text: widget.broadcast.title ?? 'Radio Stream',
            //   ),
            // ),
            // const SizedBox(height: 20),

            // // Play/Pause button
            // GestureDetector(
            //   onTap: _togglePlayPause,
            //   child: Container(
            //     width: 80,
            //     height: 80,
            //     decoration: BoxDecoration(
            //       color: Colors.white.withOpacity(0.2),
            //       shape: BoxShape.circle,
            //     ),
            //     child: Icon(
            //       _isPlaying ? Icons.pause : Icons.play_arrow,
            //       size: 40,
            //       color: Colors.white,
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerLoading(ChannelTypeEnum channelType) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[800]!,
      highlightColor: Colors.grey[600]!,
      child: SizedBox(
        width: double.infinity,
        height: 400,
        child: channelType == ChannelTypeEnum.tv
            ? Container(
                color: Colors.grey[800],
                child: const Center(
                  child: Icon(
                    Icons.tv,
                    size: 100,
                    color: Colors.white,
                  ),
                ),
              )
            : Container(
                color: Colors.grey[800],
                child: const Center(
                  child: Icon(
                    Icons.radio,
                    size: 100,
                    color: Colors.white,
                  ),
                ),
              ),
      ),
    );
  }

  // ignore: unused_element
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextView(
              text: _errorMessage ?? 'Failed to load stream',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
                _hasError = false;
                _errorMessage = null;
              });
              _initializePlayer();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xff14191F),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // is it have error
  Widget _buildControlsOverlay(String title, ChannelTypeEnum channelType) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,

      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              channelType == ChannelTypeEnum.tv ? Icons.tv : Icons.radio,
              color: Colors.white,
              size: 100,
            ),
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TvShimmer extends StatelessWidget {
  const TvShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.0.r, 20.0.r, 20.0.r, 0),
      child: ShimmerView(
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            decoration: ShapeDecoration(
              color: Colors.black.withOpacity(0.5),
              shape: ContinuousRectangleBorder(
                borderRadius: BorderRadius.circular(32),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
