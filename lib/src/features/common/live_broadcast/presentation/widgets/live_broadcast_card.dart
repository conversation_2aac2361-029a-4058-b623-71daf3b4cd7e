import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/core/constants/const.dart';
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity;
import 'package:kurdsat/src/core/enums/channel_type_enum.dart';

class LiveBroadcastCard extends StatelessWidget {
  final StrapiItemEntity<BroadcastEntity> broadcast;

  const LiveBroadcastCard({super.key, required this.broadcast});

  @override
  Widget build(BuildContext context) {
    final BroadcastEntity broadcast = this.broadcast.attributes;

    return CupertinoButton(
      onPressed: () {
        context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
      },
      padding: EdgeInsets.zero,
      minimumSize: const Size(0, 0),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: ShapeDecoration(
          color: Color(0xff14191F),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(32),
            side: BorderSide(
              color: Color(0xff1F2A3A),
              width: 1,
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: broadcast.logo?.data.attributes.url != null
              ? Image.network(
                  kBaseAssetUrl + (broadcast.logo?.data.attributes.url).toString(),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholder();
                  },
                )
              : _buildPlaceholder(),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: FittedBox(
          child: Icon(
            broadcast.attributes.channelType?.data.attributes.title == ChannelTypeEnum.tv ? Icons.tv : Icons.radio,
            color: Colors.grey[500],
          ),
        ),
      ),
    );
  }
}
