import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart';
import 'package:kurdsat/src/features/common/live_broadcast/presentation/widgets/live_broadcast_card.dart';

class LiveBroadcastGrid extends StatelessWidget {
  final List<StrapiItemEntity<BroadcastEntity>> broadcasts;

  const LiveBroadcastGrid({
    super.key,
    required this.broadcasts,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 10,
        childAspectRatio: 1,
      ),
      itemBuilder: (context, index) {
        return LiveBroadcastCard(broadcast: broadcasts[index]);
      },
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: broadcasts.length,
    );
  }
}
