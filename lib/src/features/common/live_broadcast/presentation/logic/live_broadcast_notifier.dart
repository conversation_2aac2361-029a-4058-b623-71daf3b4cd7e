import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity, BroadcastIdentifierEnum;
import 'package:kurdsat/src/features/common/live_broadcast/domain/repositories/live_broadcast_repository.dart' show liveBroadcastsRepositoryProvider, LiveBroadcastRepository;

class LiveBroadcastNotifier extends AsyncNotifier<List<StrapiItemEntity<BroadcastEntity>>> {
  late LiveBroadcastRepository _liveBroadcastsRepository;

  @override
  FutureOr<List<StrapiItemEntity<BroadcastEntity>>> build() async {
    _liveBroadcastsRepository = ref.read(liveBroadcastsRepositoryProvider);

    await getLiveBroadcasts();

    return state.value ?? [];
  }

  /// Fetches all live broadcasts [TV] & [Radio] from the repository
  Future<void> getLiveBroadcasts() async {
    state = const AsyncValue.loading();

    final result = await _liveBroadcastsRepository.getLiveBroadcasts();

    result.fold(
      (left) {
        AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right.data);
      },
    );
  }

  /// get a specific broadcast by identifier TODO: remove it
  Future<StrapiItemEntity<BroadcastEntity>?> getBroadcastByIdentifier(BroadcastIdentifierEnum identifier) async {
    if (state.isLoading) {
      return null;
    }

    final List<StrapiItemEntity<BroadcastEntity>>? broadcasts = state.value;

    try {
      return broadcasts?.firstWhere((broadcast) => broadcast.attributes.identifier == identifier);
    } catch (_) {
      return null;
    }
  }
}

final liveBroadcastNotifierProvider = AsyncNotifierProvider<LiveBroadcastNotifier, List<StrapiItemEntity<BroadcastEntity>>>(LiveBroadcastNotifier.new);
