import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiListResponseEntity;
import 'package:kurdsat/src/features/common/live_broadcast/data/datasources/live_broadcast_remote_datasource.dart' show liveBroadcastRemoteDataSourceProvider;
import 'package:kurdsat/src/features/common/live_broadcast/data/repositories/live_broadcast_repository_impl.dart' show LiveBroadcastRepositoryImpl;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity;

abstract class LiveBroadcastRepository {
  Future<Either<ErrorModel, StrapiListResponseEntity<BroadcastEntity>>> getLiveBroadcasts();
}

final liveBroadcastsRepositoryProvider = Provider<LiveBroadcastRepository>((ref) {
  return LiveBroadcastRepositoryImpl(remoteDataSource: ref.read(liveBroadcastRemoteDataSourceProvider));
});
