import 'package:equatable/equatable.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart';
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/channel_type_entity.dart';

import 'thumbnail_entity.dart' show ThumbnailEntity;

class BroadcastEntity extends Equatable {
  final int? id;
  final String? title;
  final String? link;
  final BroadcastIdentifierEnum? identifier;
  final StrapiResponseEntity<ChannelTypeEntity>? channelType;
  final StrapiResponseEntity<ThumbnailEntity>? image;
  final StrapiResponseEntity<ThumbnailEntity>? logo;

  const BroadcastEntity({
    this.id,
    this.title,
    this.link,
    this.identifier,
    this.channelType,
    this.image,
    this.logo,
  });

  @override
  List<Object?> get props => [id, title, link, identifier, channelType, image, logo];
}

enum BroadcastIdentifierEnum {
  radio957('Radio_95.7'),
  radio963('Radio_96.3'),
  kurdsatTV('Live_KursdsatTV'),
  kurdsatSportTV('Live_KurdsatSport'),
  kurdsatNewsTV('Live_Kurdsatnews');

  const BroadcastIdentifierEnum(this.value);

  final String value;

  // Helper method to get enum from string
  static BroadcastIdentifierEnum? fromString(String? value) {
    if (value == null) return null;

    for (BroadcastIdentifierEnum identifier in BroadcastIdentifierEnum.values) {
      if (identifier.value == value) {
        return identifier;
      }
    }
    return null;
  }

  // Helper method to get display name for UI
  String get displayName {
    switch (this) {
      case BroadcastIdentifierEnum.radio957:
        return 'Radio_95.7';
      case BroadcastIdentifierEnum.radio963:
        return 'Radio_96.3';
      case BroadcastIdentifierEnum.kurdsatTV:
        return "Kurdsat TV";
      case BroadcastIdentifierEnum.kurdsatSportTV:
        return "Kurdsat Sport TV";
      case BroadcastIdentifierEnum.kurdsatNewsTV:
        return "Kurdsat News TV";
    }
  }
}
