import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/common/settings/data/datasources/settings_remote_data_source.dart' show settingsDatasourceProvider;
import 'package:kurdsat/src/features/common/settings/data/repositories/settings_reposetory_impl.dart' show SettingsRepositoryImpl;
import 'package:kurdsat/src/features/common/settings/domain/entities/settings_entity.dart' show SettingsEntity;

abstract class SettingsRepository {
  Future<Either<ErrorModel, List<SettingsEntity>>> fetchSettings();
}

final settingsRepositoryProvider = Provider<SettingsRepository>((ref) {
  return SettingsRepositoryImpl(settingsRemoteDataSource: ref.read(settingsDatasourceProvider));
});
