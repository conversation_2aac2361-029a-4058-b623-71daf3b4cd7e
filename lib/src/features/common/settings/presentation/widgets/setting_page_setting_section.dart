import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view_item.dart';
import 'package:kurdsat/src/core/common/widgets/text_size_slider.dart';
import 'package:pull_down_button/pull_down_button.dart' show PullDownButton, PullDownButtonInheritedTheme, PullDownButtonTheme, PullDownMenuDividerTheme, PullDownMenuItem, PullDownMenuItemTheme, PullDownMenuRouteTheme, PullDownMenuTitleTheme;

class SettingSection extends StatelessWidget {
  const SettingSection({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoListView(
      title: "Setting",
      children: [
        LanguageSettingTile(),
        NitificationTile(),
        CupertinoListViewItem(
          label: "Article Text Size",
          icon: "assets/setting/icons/article_size.svg",
          isSvg: true,
          isLast: false,
        ),
        const TextSizeSlider(),
      ],
    );
  }
}

class NitificationTile extends StatefulWidget {
  const NitificationTile({
    super.key,
  });

  @override
  State<NitificationTile> createState() => _NitificationTileState();
}

class _NitificationTileState extends State<NitificationTile> {
  bool isNotificationEnabled = false;
  @override
  Widget build(BuildContext context) {
    return CupertinoListViewItem(
      label: "Notification",
      icon: "assets/setting/icons/notification.svg",
      trailing: Switch(
        activeTrackColor: Color(0xff3693FF),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        padding: EdgeInsets.symmetric(vertical: -5.h),
        value: isNotificationEnabled,
        onChanged: (value) {
          setState(() {
            isNotificationEnabled = value;
            HapticFeedback.selectionClick();
          });
        },
      ),
      isSvg: true,
    );
  }
}

class LanguageSettingTile extends StatefulWidget {
  final Function(String)? onLanguageChanged;

  const LanguageSettingTile({
    super.key,
    this.onLanguageChanged,
  });

  @override
  State<LanguageSettingTile> createState() => _LanguageSettingTileState();
}

class _LanguageSettingTileState extends State<LanguageSettingTile> {
  String selectedLanguage = 'English';

  void _changeLanguage(String newLanguage) {
    setState(() {
      selectedLanguage = newLanguage;
    });
    HapticFeedback.selectionClick();
    widget.onLanguageChanged?.call(newLanguage);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoListViewItem(
      onPressed: null,
      trailing: PullDownButtonInheritedTheme(
        data: PullDownButtonTheme(
          routeTheme: PullDownMenuRouteTheme(
            backgroundColor: const Color(0xff28323E),
            borderRadius: BorderRadius.circular(12),
          ),
          itemTheme: const PullDownMenuItemTheme(
            textStyle: TextStyle(color: Colors.white),
          ),
          titleTheme: const PullDownMenuTitleTheme(
            style: TextStyle(color: Colors.white),
          ),
          dividerTheme: const PullDownMenuDividerTheme(
            dividerColor: Color(0xff14191F),
          ),
        ),
        child: PullDownButton(
          itemBuilder: (context) => [
            PullDownMenuItem.selectable(
              title: 'English',
              selected: selectedLanguage == 'English',
              onTap: () => _changeLanguage('English'),
            ),
            PullDownMenuItem.selectable(
              title: 'کوردی ناوەندی',
              selected: selectedLanguage == 'کوردی ناوەندی',
              onTap: () => _changeLanguage('کوردی ناوەندی'),
            ),
            PullDownMenuItem.selectable(
              title: 'العربية',
              selected: selectedLanguage == 'العربية',
              onTap: () => _changeLanguage('العربية'),
            ),
          ],
          buttonBuilder: (context, showMenu) {
            return CupertinoButton(
              onPressed: showMenu,
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              child: Text(
                selectedLanguage,
                style: const TextStyle(color: Color(0xff3693FF)),
              ),
            );
          },
        ),
      ),
      label: "Language",
      icon: "assets/setting/icons/globe.svg",
      isSvg: true,
    );
  }
}
