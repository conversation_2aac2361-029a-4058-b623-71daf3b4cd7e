import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' show Colors;
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view_item.dart';

class HelpSection extends StatelessWidget {
  const HelpSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoListView(
      title: "Help",
      children: [
        CupertinoListViewItem(
          onPressed: () {},
          label: "FAQ",
          icon: "assets/setting/icons/faq.svg",
          isSvg: true,
        ),
        CupertinoListViewItem(
          onPressed: () {},
          label: "About Us",
          icon: "assets/setting/icons/about_us.svg",
          isSvg: true,
        ),
        CupertinoListViewItem(
          onPressed: () => context.pushNamed(RouteNameEnum.kurdbinHomePage.name),
          label: "Privacy Policy",
          icon: "assets/setting/icons/privacy_policy.svg",
          isSvg: true,
        ),
        CupertinoListViewItem(
          onPressed: () => context.pushNamed(RouteNameEnum.kurdbinHomePage.name),
          label: "Support",
          icon: "assets/setting/icons/support.svg",
          isSvg: true,
          isLast: false,
        ),
      ],
    );
  }
}
