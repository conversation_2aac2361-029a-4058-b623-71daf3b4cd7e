import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show HapticFeedback;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CupertinoExpandableListItem extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Widget? expandedContent;
  final String? expandedText;
  final bool isLast;
  final VoidCallback? onTap;
  final VoidCallback? onExpansionChanged;
  final Widget? leading;
  final String? iconPath;
  final bool isSvgIcon;
  final bool initiallyExpanded;
  final Color? titleColor;
  final Color? subtitleColor;
  final Color? expandedTextColor;
  final Color? dividerColor;
  final Color? backgroundColor;
  final Color? expandedBackgroundColor;
  final IconData? trailingIcon;
  final bool showTrailingIcon;
  final double? iconSize;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? expandedPadding;
  final Duration animationDuration;
  final Curve animationCurve;
  final bool enableHapticFeedback;

  const CupertinoExpandableListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.expandedContent,
    this.expandedText,
    this.isLast = false,
    this.onTap,
    this.onExpansionChanged,
    this.leading,
    this.iconPath,
    this.isSvgIcon = false,
    this.initiallyExpanded = false,
    this.titleColor = Colors.white,
    this.subtitleColor = Colors.white70,
    this.expandedTextColor = Colors.white70,
    this.dividerColor = const Color(0xff28323E),
    this.backgroundColor,
    this.expandedBackgroundColor,
    this.trailingIcon = CupertinoIcons.chevron_down,
    this.showTrailingIcon = true,
    this.iconSize = 24,
    this.contentPadding = const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
    this.expandedPadding = const EdgeInsets.only(left: 40, right: 20, top: 8, bottom: 12),
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.enableHapticFeedback = true,
  });

  @override
  State<CupertinoExpandableListItem> createState() => _CupertinoExpandableListItemState();
}

class _CupertinoExpandableListItemState extends State<CupertinoExpandableListItem> with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _expansionAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;

    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _expansionAnimation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(_expansionAnimation);

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    if (widget.enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }

    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });

    widget.onExpansionChanged?.call();
    widget.onTap?.call();
  }

  Widget _buildIcon() {
    if (widget.leading != null) {
      return widget.leading!;
    }

    if (widget.iconPath != null) {
      return widget.isSvgIcon
          ? SvgPicture.asset(
              widget.iconPath!,
              width: widget.iconSize,
              height: widget.iconSize,
            )
          : Image.asset(
              widget.iconPath!,
              width: widget.iconSize,
              height: widget.iconSize,
            );
    }

    return const SizedBox.shrink();
  }

  Widget _buildExpandedContent() {
    if (widget.expandedContent != null) {
      return widget.expandedContent!;
    }

    if (widget.expandedText != null) {
      return Text(
        widget.expandedText!,
        style: TextStyle(
          color: widget.expandedTextColor,
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
        ),
      );
    }

    return Text(
      "Expanded Content",
      style: TextStyle(
        color: widget.expandedTextColor,
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.animationDuration,
      curve: widget.animationCurve,
      decoration: BoxDecoration(
        color: _isExpanded ? widget.expandedBackgroundColor ?? widget.backgroundColor : widget.backgroundColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CupertinoButton(
            onPressed: _toggleExpansion,
            minimumSize: Size.zero,
            padding: EdgeInsets.zero,
            child: Padding(
              padding: widget.contentPadding!,
              child: Row(
                children: [
                  // Leading icon or widget
                  if (widget.leading != null || widget.iconPath != null) ...[
                    _buildIcon(),
                    SizedBox(width: 12.w),
                  ],

                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.title,
                          style: TextStyle(
                            color: widget.titleColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 16.sp,
                          ),
                        ),
                        if (widget.subtitle != null) ...[
                          SizedBox(height: 2.h),
                          Text(
                            widget.subtitle!,
                            style: TextStyle(
                              color: widget.subtitleColor,
                              fontWeight: FontWeight.w300,
                              fontSize: 13.sp,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Trailing chevron icon
                  if (widget.showTrailingIcon) ...[
                    SizedBox(width: 8.w),
                    AnimatedBuilder(
                      animation: _rotationAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotationAnimation.value * 2 * 3.14159,
                          child: Icon(
                            widget.trailingIcon,
                            color: widget.titleColor?.withOpacity(0.6),
                            size: 16.sp,
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Expanded content with smooth animation
          SizeTransition(
            sizeFactor: _expansionAnimation,
            axisAlignment: -1.0,
            child: Padding(
              padding: widget.expandedPadding!,
              child: FadeTransition(
                opacity: _expansionAnimation,
                child: _buildExpandedContent(),
              ),
            ),
          ),

          // Divider
          if (!widget.isLast) ...[
            Divider(
              height: 1,
              thickness: 0.5,
              color: widget.dividerColor,
              indent: 20,
            ),
          ],
        ],
      ),
    );
  }
}

// class CupertinoListViewExpandedItem extends StatefulWidget {
//   final String label;
//   final bool? isLast;
//   final VoidCallback? onPressed;
//   final Widget? leading;
//   final String? icon;
//   final bool? isSvg;

//   const CupertinoListViewExpandedItem({
//     super.key,
//     required this.label,
//     this.isLast,
//     this.onPressed,
//     this.leading,
//     this.icon,
//     this.isSvg = false,
//   });

//   @override
//   State<CupertinoListViewExpandedItem> createState() => _CupertinoListViewExpandedItemState();
// }

// class _CupertinoListViewExpandedItemState extends State<CupertinoListViewExpandedItem> {
//   bool isExpanded = false;

//   @override
//   Widget build(BuildContext context) {
//     return AnimatedContainer(
//       duration: const Duration(milliseconds: 300),
//       child: CupertinoButton(
//         onPressed: () {
//           setState(() {
//             isExpanded = !isExpanded;
//           });
//         },
//         minimumSize: Size.zero,
//         padding: EdgeInsets.zero,
//         child: Padding(
//           padding: const EdgeInsets.only(left: 20),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               12.verticalSpace,
//               Row(
//                 children: [
//                   if (widget.leading != null) ...[
//                     widget.leading!,
//                     12.horizontalSpace,
//                   ],
//                   if (widget.icon != null && widget.isSvg == true) ...[
//                     SvgPicture.asset(
//                       widget.icon!,
//                       width: 24.w,
//                       height: 24.h,
//                     ),
//                     12.horizontalSpace,
//                   ] else if (widget.icon != null && widget.isSvg == false) ...[
//                     Image.asset(
//                       widget.icon!,
//                       width: 24.w,
//                       height: 24.h,
//                     ),
//                     12.horizontalSpace,
//                   ],

//                   Text(
//                     widget.label,
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontWeight: FontWeight.w400,
//                       fontSize: 16.sp,
//                     ),
//                   ),
//                 ],
//               ),
//               if (isExpanded) ...[
//                 Padding(
//                   padding: const EdgeInsets.only(left: 20, top: 8, bottom: 8),
//                   child: Text(
//                     "This is an expanded item. You can add more content here.",
//                     style: TextStyle(
//                       color: Colors.white70,
//                       fontSize: 14.sp,
//                     ),
//                   ),
//                 ),
//               ],
//               if (widget.isLast ?? true) ...[
//                 12.verticalSpace,
//                 Divider(
//                   height: 1,
//                   color: const Color(0xff28323E),
//                 ),
//               ] else ...[
//                 6.verticalSpace,
//               ],
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
