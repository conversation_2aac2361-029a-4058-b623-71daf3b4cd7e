import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CupertinoListViewItem extends StatelessWidget {
  final String label;
  final bool? isLast;
  final VoidCallback? onPressed;
  final Widget? leading;
  final String? icon;
  final bool? isSvg;
  final Widget? trailing;

  const CupertinoListViewItem({
    super.key,
    required this.label,
    this.isLast,
    this.onPressed,
    this.leading,
    this.icon,
    this.isSvg = false,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      onPressed: onPressed,
      minimumSize: Size.zero,
      padding: EdgeInsets.zero,
      child: Padding(
        padding: EdgeInsets.only(left: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            12.verticalSpace,
            Row(
              children: [
                if (leading != null) ...[
                  leading!,
                  12.horizontalSpace,
                ],
                if (icon != null && isSvg == true) ...[
                  SvgPicture.asset(
                    icon!,
                    width: 24.w,
                    height: 24.h,
                  ),
                  12.horizontalSpace,
                ] else if (icon != null && isSvg == false) ...[
                  Image.asset(
                    icon!,
                    width: 24.w,
                    height: 24.h,
                  ),
                  12.horizontalSpace,
                ],

                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                    fontSize: 16.sp,
                  ),
                ),
                if (trailing != null) ...[
                  const Spacer(),
                  trailing!,
                ],
                20.horizontalSpace,
              ],
            ),
            if (isLast ?? true) ...[
              12.verticalSpace,
              Divider(
                height: 1,
                color: const Color(0xff28323E),
              ),
            ] else ...[
              6.verticalSpace,
            ],
          ],
        ),
      ),
    );
  }
}
