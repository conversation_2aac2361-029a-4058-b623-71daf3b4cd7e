import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/sicoal_icon_button.dart';

class FollowUsSection extends StatelessWidget {
  const FollowUsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "Follow us on",
          style: TextStyle(
            color: Color(0xffA2B0C3),
            fontWeight: FontWeight.w400,
            fontSize: 16.sp,
          ),
        ),
        12.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 24.w,
          children: [
            SicoalIconButton(
              svgIcon: "assets/setting/socials/YouTube.svg",
              url: "https://www.youtube.com/@TheKurdsat",
            ),
            SicoalIconButton(
              svgIcon: "assets/setting/socials/Instagram.svg",
              url: "https://www.instagram.com/kurdsat.broadcasting/",
            ),
            SicoalIconButton(
              svgIcon: "assets/setting/socials/X.svg",
              url: "https://x.com/kurdsat",
            ),
            SicoalIconButton(
              svgIcon: "assets/setting/socials/Facebook.svg",
              url: "https://www.facebook.com/Kurdsat/?locale=ku_TR",
            ),
          ],
        ),
      ],
    );
  }
}
