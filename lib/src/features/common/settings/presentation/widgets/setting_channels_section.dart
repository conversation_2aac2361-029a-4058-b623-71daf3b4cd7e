import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_expandable_list_item.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view_item.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/kurdsat_news_menu_channel_expention_section.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/setting_radio_sections.dart';

class SettingChannelsSection extends ConsumerWidget {
  const SettingChannelsSection({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CupertinoListView(
      title: "Channels",
      children: [
        KurdsatNewsMenuChannelExpentionSection(),
        CupertinoExpandableListItem(
          title: "Kurdsat TV",
          iconPath: "assets/setting/channels_icon/Kurdsat.png",
          isSvgIcon: false,
        ),
        CupertinoExpandableListItem(
          title: "Kurdbin",
          iconPath: "assets/setting/channels_icon/kurdbin.png",
          isSvgIcon: false,
        ),
        SettingRadioSections(),
      ],
    );
  }
}
