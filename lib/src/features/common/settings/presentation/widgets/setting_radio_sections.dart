import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart';
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity, BroadcastIdentifierEnum;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/logic/live_broadcast_notifier.dart' show liveBroadcastNotifierProvider;
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view_item.dart';

class SettingRadioSections extends ConsumerStatefulWidget {
  const SettingRadioSections({
    super.key,
  });

  @override
  ConsumerState<SettingRadioSections> createState() => _RadiListState();
}

class _RadiListState extends ConsumerState<SettingRadioSections> {
  @override
  Widget build(BuildContext context) {
    final liveStreamState = ref.watch(liveBroadcastNotifierProvider);

    return liveStreamState.maybeWhen(
      orElse: () => SizedBox(),
      data: (List<StrapiItemEntity<BroadcastEntity>> broadcasts) {
        return Column(
          children: [
            CupertinoListViewItem(
              onPressed: () {
                final BroadcastEntity broadcast = broadcasts.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.radio963).attributes;
                context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
              },
              label: "Dangi Kurdsat",
              icon: "assets/setting/channels_icon/radio-96.3.png",
            ),
            CupertinoListViewItem(
              onPressed: () {
                final BroadcastEntity broadcast = broadcasts.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.radio957).attributes;
                context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
              },
              label: "Dangi Kurdsat",
              icon: "assets/setting/channels_icon/radio-95.7.png",
              isLast: false,
            ),
          ],
        );
      },
    );
  }
}
