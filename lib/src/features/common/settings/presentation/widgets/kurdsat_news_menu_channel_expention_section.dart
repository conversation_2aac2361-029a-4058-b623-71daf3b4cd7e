import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' show Divider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart' show RouteNameEnum;
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_expandable_list_item.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/cupertino_list_view_item.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_data_entity.dart' show KurdsatNewsHomeDataEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart' show KurdsatNewsHomeResponseEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/logic/kurdsat_news_home_notifier.dart' show kurdsatNewsHomeNotifierProvider;

class KurdsatNewsMenuChannelExpentionSection extends ConsumerWidget {
  const KurdsatNewsMenuChannelExpentionSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final kurdsatNewsState = ref.watch(kurdsatNewsHomeNotifierProvider);
    return CupertinoExpandableListItem(
      title: "Kurdsat News",
      iconPath: "assets/setting/channels_icon/kurdsat-news.png",
      expandedContent: kurdsatNewsState.maybeWhen(
        orElse: () => SizedBox(),
        data: (KurdsatNewsHomeResponseEntity kurdsatNewsHomeData) {
          final List<KurdsatNewsHomeDataEntity> data = kurdsatNewsHomeData.data ?? [];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12,
            children: data.map((KurdsatNewsHomeDataEntity kurdsatNewsHomeEntity) {
              return CupertinoButton(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                onPressed: () {
                  if (kurdsatNewsHomeEntity.articles.isEmpty && kurdsatNewsHomeEntity.articleCategories.isNotEmpty) {
                    context.pushNamed(RouteNameEnum.articleListOfCategory.name, extra: kurdsatNewsHomeEntity.articleCategories);
                  } else if (kurdsatNewsHomeEntity.articles.isNotEmpty) {
                    context.pushNamed(RouteNameEnum.articleCategory.name, extra: kurdsatNewsHomeEntity.articles);
                  } else {
                    context.pushNamed(RouteNameEnum.articleCategory.name, extra: kurdsatNewsHomeEntity.articles);
                  }
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextView(
                      text: (kurdsatNewsHomeEntity.title ?? "").toUpperCase(),

                      style: TextStyle(
                        color: Color(0xffA2B0C3),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    12.verticalSpace,
                    if (data.last != kurdsatNewsHomeEntity) ...[
                      Divider(
                        height: 1,
                        thickness: 0.5,
                        color: const Color(0xff28323E),
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }
}
