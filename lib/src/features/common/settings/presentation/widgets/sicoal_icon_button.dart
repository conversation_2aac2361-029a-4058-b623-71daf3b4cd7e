import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

class SicoalIconButton extends StatelessWidget {
  final String svgIcon;
  final String url;

  const SicoalIconButton({
    super.key,
    required this.svgIcon,
    required this.url,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      minimumSize: Size.zero,
      onPressed: () {
        launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      },
      child: SvgPicture.asset(
        svgIcon,
        width: 24.w,
        height: 24.h,
      ),
    );
  }
}
