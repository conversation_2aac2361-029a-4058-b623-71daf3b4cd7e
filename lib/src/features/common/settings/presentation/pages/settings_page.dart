import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart' show AppBarView;
import 'package:kurdsat/src/features/common/settings/presentation/widgets/follow_us_section.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/setting_channels_section.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/setting_help_section.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/setting_page_setting_section.dart';
import 'package:kurdsat/src/features/common/settings/presentation/widgets/version_section.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar<PERSON>iew(title: "<PERSON><PERSON>", showNotificationIcon: false),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            24.verticalSpace,
            SettingChannelsSection(),

            SettingSection(),

            HelpSection(),

            FollowUsSection(),

            VersionSection(),
          ],
        ),
      ),
    );
  }
}
