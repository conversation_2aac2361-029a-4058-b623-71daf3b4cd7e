import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/common/settings/domain/entities/settings_entity.dart" show SettingsEntity;
import "package:kurdsat/src/features/common/settings/domain/repositories/settings_repository.dart";

class SettingsNotifier extends AsyncNotifier<List<SettingsEntity>> {
  late SettingsRepository _settingsRepositoryProvider;

  @override
  FutureOr<List<SettingsEntity>> build() async {
    _settingsRepositoryProvider = ref.read(settingsRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _settingsRepositoryProvider.fetchSettings();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final settingsNotifierProvider = AsyncNotifierProvider<SettingsNotifier, List<SettingsEntity>>(SettingsNotifier.new);
