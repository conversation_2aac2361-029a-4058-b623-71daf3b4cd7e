import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/constants/const.dart';
import 'package:shared_preferences/shared_preferences.dart' show SharedPreferences;

class TextSizeNotifier extends StateNotifier<double> {
  SharedPreferences? _cachedPrefs;

  TextSizeNotifier() : super(0.0) {
    _loadSavedSize();
  }

  Future<void> _loadSavedSize() async {
    try {
      final prefs = await _getSharedPreferences();
      if (mounted) {
        state = prefs.getDouble(kArticleTextSizeKey) ?? 0.0;
      }
    } catch (e) {
      if (mounted) {
        state = 0.0;
      }
    }
  }

  Future<void> setTextSize(double size) async {
    state = size;
    _saveToPreferences(size);
  }

  Future<void> _saveToPreferences(double size) async {
    try {
      final prefs = await _getSharedPreferences();
      await prefs.setDouble(kArticleTextSizeKey, size);
    } catch (e) {
      if (kDebugMode) {
        print("Error saving text size: $e");
      }
    }
  }

  @override
  void dispose() {
    _cachedPrefs = null;
    super.dispose();
  }

  Future<SharedPreferences> _getSharedPreferences() async {
    if (_cachedPrefs != null) {
      return _cachedPrefs!;
    }

    _cachedPrefs = await SharedPreferences.getInstance();
    return _cachedPrefs!;
  }
}

final textSizeProvider = StateNotifierProvider<TextSizeNotifier, double>((ref) {
  return TextSizeNotifier();
});
