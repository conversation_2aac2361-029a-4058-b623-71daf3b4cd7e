import 'dart:convert' show json;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class SettingsRemoteDataSource {
  final HttpManager httpManager;

  SettingsRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> fetchSettings() async {
    final response = await httpManager.request(
      path: Api().baseUrl,
      method: HttpMethods.get,
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final settingsDatasourceProvider = Provider<SettingsRemoteDataSource>((ref) {
  return SettingsRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
