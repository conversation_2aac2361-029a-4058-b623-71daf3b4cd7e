import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/common/settings/data/datasources/settings_remote_data_source.dart' show SettingsRemoteDataSource;
import 'package:kurdsat/src/features/common/settings/data/mappers/settings_mapper.dart' show SettingsMapper;
import 'package:kurdsat/src/features/common/settings/data/models/settings_model_data.dart' show SettingsModelData;
import 'package:kurdsat/src/features/common/settings/domain/entities/settings_entity.dart' show SettingsEntity;
import 'package:kurdsat/src/features/common/settings/domain/repositories/settings_repository.dart' show SettingsRepository;

class SettingsRepositoryImpl implements SettingsRepository {
  final SettingsRemoteDataSource settingsRemoteDataSource;

  SettingsRepositoryImpl({required this.settingsRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<SettingsEntity>>> fetchSettings() async {
    try {
      final result = await settingsRemoteDataSource.fetchSettings();

      SettingsModelData model = SettingsModelData.fromJson(result);

      SettingsMapper mapper = SettingsMapper(settingsModelData: model);

      final List<SettingsEntity> finalResponse = mapper.toSettingsEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
