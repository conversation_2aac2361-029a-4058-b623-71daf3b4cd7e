import 'package:flutter/material.dart';

import '../../../../../app/theme/colors.dart';
import '../../../../../core/common/widgets/loading_view.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 1500));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: BackButton(),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            LoadingView(loadingSize: 50, loadingColor: ColorPalette.primaryColor),
            const TextView(text: "loading_screen_loading_text", style: TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }
}
