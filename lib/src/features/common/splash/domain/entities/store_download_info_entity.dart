import 'package:equatable/equatable.dart';

class DownloadAppInfo extends Equatable {
  final String? androidUrl;
  final String? iosUrl;

  const DownloadAppInfo({
    this.androidUrl,
    this.iosUrl,
  });

  DownloadAppInfo copyWith({
    String? androidUrl,
    String? iosUrl,
  }) {
    return DownloadAppInfo(
      androidUrl: androidUrl ?? this.androidUrl,
      iosUrl: iosUrl ?? this.iosUrl,
    );
  }

  @override
  List<Object?> get props {
    return [
      androidUrl,
      iosUrl,
    ];
  }
}
