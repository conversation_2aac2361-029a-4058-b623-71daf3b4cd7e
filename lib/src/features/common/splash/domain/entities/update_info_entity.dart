import 'package:equatable/equatable.dart';

class UpdateInfo extends Equatable {
  final String updateUrl;
  final bool forceUpdate;
  final bool isAndroid;

  const UpdateInfo({
    required this.updateUrl,
    required this.forceUpdate,
    required this.isAndroid,
  });

  UpdateInfo copyWith({
    String? updateUrl,
    bool? isInternetAvailable,
    bool? forceUpdate,
    bool? isAndroid,
  }) {
    return UpdateInfo(
      updateUrl: updateUrl ?? this.updateUrl,
      forceUpdate: forceUpdate ?? this.forceUpdate,
      isAndroid: isAndroid ?? this.isAndroid,
    );
  }

  @override
  List<Object?> get props {
    return [
      updateUrl,
      forceUpdate,
      isAndroid,
    ];
  }
}
