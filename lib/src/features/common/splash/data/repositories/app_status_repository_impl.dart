import 'package:dartz/dartz.dart';

import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/common/domain/entities/custom_header.dart';
import '../../../../../core/utils/helpers/error_parser.dart';
import '../../../../../core/utils/helpers/gms_hms_checker.dart';
import '../../domain/entities/app_status.dart';
import '../../domain/repositories/app_status_repository.dart';
import '../datasources/remote/app_status_data_source.dart';
import '../mappers/app_status_mapper.dart';
import '../models/app_status_model.dart';

class AppStatusRepositoryImpl implements AppStatusRepository {
  final AppStatusDataSource appStatusDataSource;
  final GmsAndHmsChecker gmsAndHmsChecker;

  AppStatusRepositoryImpl({required this.appStatusDataSource, required this.gmsAndHmsChecker});

  @override
  Future<Either<ErrorModel, AppStatus>> getAppStatusData(CustomHeaders header) async {
    try {
      final appStatusModelData = await appStatusDataSource.getData(header);

      final AppStatusModel appStatusModel = AppStatusModel.fromJson(appStatusModelData);

      final GmsAndHmsCheckerStatus gmsAndHmsCheckerStatus = await gmsAndHmsChecker.check();
      final bool isHms = gmsAndHmsCheckerStatus == GmsAndHmsCheckerStatus.hms;

      final AppStatusEntityMapper appStatusEntityMapper = AppStatusEntityMapper(appStatusModelData: appStatusModel.data, isHms: isHms);

      final AppStatus appStatus = appStatusEntityMapper.toAppStatus();

      return Right(appStatus);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}
