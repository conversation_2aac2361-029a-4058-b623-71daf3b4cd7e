// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_status_model_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppStatusModelData _$AppStatusModelDataFromJson(Map<String, dynamic> json) =>
    AppStatusModelData(
      androidUrl: json['android_url'] as String?,
      iosUrl: json['ios_url'] as String?,
      latestVersion: json['latest_version'] as String?,
      forceUpdate: json['force_update'] as bool?,
      maintenance: json['maintenance'] as bool?,
      lookupDomain: json['lookup_domain'] as String?,
      aboutUs: json['about_us'] as bool?,
      prize: json['prize'] as bool?,
      iqShowRoomLat: (json['iq_show_room_lat'] as num?)?.toDouble(),
      iqShowRoomLong: (json['iq_show_room_long'] as num?)?.toDouble(),
      huaweiUrl: json['huawei_url'] as String?,
    );

Map<String, dynamic> _$AppStatusModelDataToJson(AppStatusModelData instance) =>
    <String, dynamic>{
      'android_url': instance.androidUrl,
      'ios_url': instance.iosUrl,
      'huawei_url': instance.huaweiUrl,
      'latest_version': instance.latestVersion,
      'force_update': instance.forceUpdate,
      'maintenance': instance.maintenance,
      'lookup_domain': instance.lookupDomain,
      'about_us': instance.aboutUs,
      'prize': instance.prize,
      'iq_show_room_lat': instance.iqShowRoomLat,
      'iq_show_room_long': instance.iqShowRoomLong,
    };
