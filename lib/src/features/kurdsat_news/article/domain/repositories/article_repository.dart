import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/features/kurdsat_news/article/data/datasources/article_remote_data_source.dart' show articleDatasourceProvider;
import 'package:kurdsat/src/features/kurdsat_news/article/data/repositories/article_reposetory_impl.dart' show ArticleRepositoryImpl;
import 'package:kurdsat/src/features/kurdsat_news/article/domain/entities/article_entity.dart' show ArticleResponseEntity;

abstract class ArticleRepository {
  Future<Either<ErrorModel, ArticleResponseEntity>> getArticle(int articleId);
}

final articleRepositoryProvider = Provider<ArticleRepository>((ref) {
  return ArticleRepositoryImpl(articleRemoteDataSource: ref.read(articleDatasourceProvider));
});
