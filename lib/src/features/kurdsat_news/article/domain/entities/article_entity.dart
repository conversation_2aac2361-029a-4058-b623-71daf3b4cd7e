import 'package:equatable/equatable.dart';

class ArticleEntity extends Equatable {
  final int? id;
  final DateTime? date;
  final String? title;
  final String? body;
  final String? subtitle;
  final List<ArticleCategoryEntity>? articleCategories;
  final List<ArticleTypeEntity>? articleTypes;
  final ArticleImageEntity? image;
  final ArticleAuthorEntity? author;

  const ArticleEntity({this.id, this.date, this.title, this.body, this.subtitle, this.articleCategories, this.articleTypes, this.image, this.author});

  @override
  List<Object?> get props => [id, date, title, body, subtitle, articleCategories, articleTypes, image, author];
}

class ArticleCategoryEntity extends Equatable {
  final int? id;
  final String? title;

  const ArticleCategoryEntity({this.id, this.title});

  @override
  List<Object?> get props => [id, title];
}

class ArticleTypeEntity extends Equatable {
  final int? id;
  final String? title;

  const ArticleTypeEntity({this.id, this.title});

  @override
  List<Object?> get props => [id, title];
}

class ArticleImageEntity extends Equatable {
  final int? id;
  final ArticleImageDataEntity? image;

  const ArticleImageEntity({this.id, this.image});

  @override
  List<Object?> get props => [id, image];
}

class ArticleImageDataEntity extends Equatable {
  final int? id;
  final String? url;

  const ArticleImageDataEntity({this.id, this.url});

  @override
  List<Object?> get props => [id, url];
}

class ArticleAuthorEntity extends Equatable {
  final int? id;
  final String? fullName;

  const ArticleAuthorEntity({this.id, this.fullName});

  @override
  List<Object?> get props => [id, fullName];
}

class ArticleResponseEntity extends Equatable {
  final ArticleEntity? data;
  final Map<String, dynamic>? meta;

  const ArticleResponseEntity({this.data, this.meta});

  @override
  List<Object?> get props => [data, meta];
}
