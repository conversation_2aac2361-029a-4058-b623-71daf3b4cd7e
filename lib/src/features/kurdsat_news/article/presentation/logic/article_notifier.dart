import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat/src/features/kurdsat_news/article/domain/entities/article_entity.dart" show ArticleEntity;
import "package:kurdsat/src/features/kurdsat_news/article/domain/repositories/article_repository.dart";

class ArticleNotifier extends AsyncNotifier<ArticleEntity?> {
  late ArticleRepository _articleRepositoryProvider;

  @override
  FutureOr<ArticleEntity?> build() async {
    _articleRepositoryProvider = ref.read(articleRepositoryProvider);
    return null;
  }

  Future<void> fetchArticle(int articleId) async {
    state = const AsyncValue.loading();

    final response = await _articleRepositoryProvider.getArticle(articleId);

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right.data);
      },
    );
  }
}

final articleNotifierProvider = AsyncNotifierProvider<ArticleNotifier, ArticleEntity?>(ArticleNotifier.new);
