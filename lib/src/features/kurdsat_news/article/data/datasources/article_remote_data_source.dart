import 'dart:convert' show json;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/constants/const.dart' show kToken;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class ArticleRemoteDataSource {
  final HttpManager httpManager;

  ArticleRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> getArticle(int articleId) async {
    final response = await httpManager.request(
      path: Api().kurdsatNewsArticle(articleId),
      method: HttpMethods.get,
      headers: {
        "Authorization": "Bearer $kToken",
        // "X-Firebase-AppCheck": kToken,
      },
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final articleDatasourceProvider = Provider<ArticleRemoteDataSource>((ref) {
  return ArticleRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
