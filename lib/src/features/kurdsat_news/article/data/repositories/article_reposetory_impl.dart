import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/kurdsat_news/article/data/datasources/article_remote_data_source.dart' show ArticleRemoteDataSource;
import 'package:kurdsat/src/features/kurdsat_news/article/data/models/article_model.dart' show ArticleResponseModel;
import 'package:kurdsat/src/features/kurdsat_news/article/domain/entities/article_entity.dart' show ArticleResponseEntity;
import 'package:kurdsat/src/features/kurdsat_news/article/domain/repositories/article_repository.dart' show ArticleRepository;

class ArticleRepositoryImpl implements ArticleRepository {
  final ArticleRemoteDataSource articleRemoteDataSource;

  ArticleRepositoryImpl({required this.articleRemoteDataSource});

  @override
  Future<Either<ErrorModel, ArticleResponseEntity>> getArticle(int articleId) async {
    try {
      final result = await articleRemoteDataSource.getArticle(articleId);

      ArticleResponseModel model = ArticleResponseModel.fromJson(result);

      final ArticleResponseEntity finalResponse = model.toEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
