// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'article_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ArticleModel _$ArticleModelFromJson(Map<String, dynamic> json) => ArticleModel(
  id: (json['id'] as num?)?.toInt(),
  date: json['date'] as String?,
  title: json['title'] as String?,
  body: json['body'] as String?,
  subtitle: json['subtitle'] as String?,
  articleCategories:
      json['article_categories'] == null
          ? null
          : ArticleCategoriesModel.fromJson(
            json['article_categories'] as Map<String, dynamic>,
          ),
  articleTypes:
      json['article_types'] == null
          ? null
          : ArticleTypesModel.fromJson(
            json['article_types'] as Map<String, dynamic>,
          ),
  image:
      json['image'] == null
          ? null
          : ArticleImageModel.fromJson(json['image'] as Map<String, dynamic>),
  author:
      json['author'] == null
          ? null
          : ArticleAuthorModel.fromJson(json['author'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ArticleModelToJson(ArticleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': instance.date,
      'title': instance.title,
      'body': instance.body,
      'subtitle': instance.subtitle,
      'article_categories': instance.articleCategories,
      'article_types': instance.articleTypes,
      'image': instance.image,
      'author': instance.author,
    };

ArticleCategoriesModel _$ArticleCategoriesModelFromJson(
  Map<String, dynamic> json,
) => ArticleCategoriesModel(
  data:
      (json['data'] as List<dynamic>?)
          ?.map((e) => ArticleCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$ArticleCategoriesModelToJson(
  ArticleCategoriesModel instance,
) => <String, dynamic>{'data': instance.data};

ArticleCategoryModel _$ArticleCategoryModelFromJson(
  Map<String, dynamic> json,
) => ArticleCategoryModel(
  id: (json['id'] as num?)?.toInt(),
  attributes:
      json['attributes'] == null
          ? null
          : ArticleCategoryAttributesModel.fromJson(
            json['attributes'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ArticleCategoryModelToJson(
  ArticleCategoryModel instance,
) => <String, dynamic>{'id': instance.id, 'attributes': instance.attributes};

ArticleCategoryAttributesModel _$ArticleCategoryAttributesModelFromJson(
  Map<String, dynamic> json,
) => ArticleCategoryAttributesModel(title: json['title'] as String?);

Map<String, dynamic> _$ArticleCategoryAttributesModelToJson(
  ArticleCategoryAttributesModel instance,
) => <String, dynamic>{'title': instance.title};

ArticleTypesModel _$ArticleTypesModelFromJson(Map<String, dynamic> json) =>
    ArticleTypesModel(
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => ArticleTypeModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$ArticleTypesModelToJson(ArticleTypesModel instance) =>
    <String, dynamic>{'data': instance.data};

ArticleTypeModel _$ArticleTypeModelFromJson(Map<String, dynamic> json) =>
    ArticleTypeModel(
      id: (json['id'] as num?)?.toInt(),
      attributes:
          json['attributes'] == null
              ? null
              : ArticleTypeAttributesModel.fromJson(
                json['attributes'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$ArticleTypeModelToJson(ArticleTypeModel instance) =>
    <String, dynamic>{'id': instance.id, 'attributes': instance.attributes};

ArticleTypeAttributesModel _$ArticleTypeAttributesModelFromJson(
  Map<String, dynamic> json,
) => ArticleTypeAttributesModel(title: json['title'] as String?);

Map<String, dynamic> _$ArticleTypeAttributesModelToJson(
  ArticleTypeAttributesModel instance,
) => <String, dynamic>{'title': instance.title};

ArticleImageModel _$ArticleImageModelFromJson(Map<String, dynamic> json) =>
    ArticleImageModel(
      id: (json['id'] as num?)?.toInt(),
      image:
          json['image'] == null
              ? null
              : ArticleImageDataModel.fromJson(
                json['image'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$ArticleImageModelToJson(ArticleImageModel instance) =>
    <String, dynamic>{'id': instance.id, 'image': instance.image};

ArticleImageDataModel _$ArticleImageDataModelFromJson(
  Map<String, dynamic> json,
) => ArticleImageDataModel(
  data:
      json['data'] == null
          ? null
          : ArticleImageDataDataModel.fromJson(
            json['data'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ArticleImageDataModelToJson(
  ArticleImageDataModel instance,
) => <String, dynamic>{'data': instance.data};

ArticleImageDataDataModel _$ArticleImageDataDataModelFromJson(
  Map<String, dynamic> json,
) => ArticleImageDataDataModel(
  id: (json['id'] as num?)?.toInt(),
  attributes:
      json['attributes'] == null
          ? null
          : ArticleImageAttributesModel.fromJson(
            json['attributes'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ArticleImageDataDataModelToJson(
  ArticleImageDataDataModel instance,
) => <String, dynamic>{'id': instance.id, 'attributes': instance.attributes};

ArticleImageAttributesModel _$ArticleImageAttributesModelFromJson(
  Map<String, dynamic> json,
) => ArticleImageAttributesModel(url: json['url'] as String?);

Map<String, dynamic> _$ArticleImageAttributesModelToJson(
  ArticleImageAttributesModel instance,
) => <String, dynamic>{'url': instance.url};

ArticleAuthorModel _$ArticleAuthorModelFromJson(Map<String, dynamic> json) =>
    ArticleAuthorModel(
      data:
          json['data'] == null
              ? null
              : ArticleAuthorDataModel.fromJson(
                json['data'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$ArticleAuthorModelToJson(ArticleAuthorModel instance) =>
    <String, dynamic>{'data': instance.data};

ArticleAuthorDataModel _$ArticleAuthorDataModelFromJson(
  Map<String, dynamic> json,
) => ArticleAuthorDataModel(
  id: (json['id'] as num?)?.toInt(),
  attributes:
      json['attributes'] == null
          ? null
          : ArticleAuthorAttributesModel.fromJson(
            json['attributes'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ArticleAuthorDataModelToJson(
  ArticleAuthorDataModel instance,
) => <String, dynamic>{'id': instance.id, 'attributes': instance.attributes};

ArticleAuthorAttributesModel _$ArticleAuthorAttributesModelFromJson(
  Map<String, dynamic> json,
) => ArticleAuthorAttributesModel(fullName: json['full_name'] as String?);

Map<String, dynamic> _$ArticleAuthorAttributesModelToJson(
  ArticleAuthorAttributesModel instance,
) => <String, dynamic>{'full_name': instance.fullName};

ArticleResponseModel _$ArticleResponseModelFromJson(
  Map<String, dynamic> json,
) => ArticleResponseModel(
  data:
      json['data'] == null
          ? null
          : ArticleDataModel.fromJson(json['data'] as Map<String, dynamic>),
  meta: json['meta'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ArticleResponseModelToJson(
  ArticleResponseModel instance,
) => <String, dynamic>{'data': instance.data, 'meta': instance.meta};

ArticleDataModel _$ArticleDataModelFromJson(Map<String, dynamic> json) =>
    ArticleDataModel(
      id: (json['id'] as num?)?.toInt(),
      attributes:
          json['attributes'] == null
              ? null
              : ArticleModel.fromJson(
                json['attributes'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$ArticleDataModelToJson(ArticleDataModel instance) =>
    <String, dynamic>{'id': instance.id, 'attributes': instance.attributes};
