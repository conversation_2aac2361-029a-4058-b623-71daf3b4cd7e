import 'dart:developer' show inspect;

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart';
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart';
import 'package:kurdsat/src/features/kurdsat_news/related_news/data/datasources/related_news_remote_data_source.dart' show RelatedNewsRemoteDataSource;
import 'package:kurdsat/src/features/kurdsat_news/related_news/data/models/related_news_model.dart';
import 'package:kurdsat/src/features/kurdsat_news/related_news/domain/repositories/related_news_repository.dart';

class RelatedNewsRepositoryImpl implements RelatedNewsRepository {
  final RelatedNewsRemoteDataSource relatedNewsRemoteDataSource;

  RelatedNewsRepositoryImpl({required this.relatedNewsRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<RelatedNewsDataModel>>> fetchRelatedNews() async {
    try {
      final result = await relatedNewsRemoteDataSource.fetchRelatedNews();

      final RelatedNewsResponseModel news = RelatedNewsResponseModel.fromJson(result);

      return Right(news.data);
    } catch (error, stackTrace) {
      inspect(error);
      return Left(errorParser(error, stackTrace));
    }
  }
}
