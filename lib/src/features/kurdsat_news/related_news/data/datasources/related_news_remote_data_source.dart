import 'dart:convert' show json;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;

class RelatedNewsRemoteDataSource {
  final HttpManager httpManager;

  RelatedNewsRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> fetchRelatedNews() async {
    final response = await httpManager.request(
      path: Api().relatedNews,
      method: HttpMethods.get,
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final relatedNewsDatasourceProvider = Provider<RelatedNewsRemoteDataSource>((ref) {
  return RelatedNewsRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
