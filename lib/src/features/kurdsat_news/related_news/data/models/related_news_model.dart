class RelatedNewsResponseModel {
  final List<RelatedNewsDataModel> data;

  RelatedNewsResponseModel({required this.data});

  factory RelatedNewsResponseModel.fromJson(Map<String, dynamic> json) {
    return RelatedNewsResponseModel(
      data: (json['data'] as List<dynamic>).map((e) {
        return RelatedNewsDataModel.fromJson(e as Map<String, dynamic>);
      }).toList(),
    );
  }
}

class RelatedNewsDataModel {
  final int? id;
  final DateTime? date;
  final String? title;
  final DateTime? createdAt;
  final String? locale;
  final String? imageUrl;

  RelatedNewsDataModel({
    this.id,
    this.date,
    this.title,
    this.createdAt,
    this.locale,
    this.imageUrl,
  });

  factory RelatedNewsDataModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>?;
    return RelatedNewsDataModel(
      id: json['id'] as int?,
      date: attributes?['date'] != null ? DateTime.tryParse(attributes!['date']) : null,
      title: attributes?['title'] as String?,
      createdAt: attributes?['createdAt'] != null ? DateTime.tryParse(attributes!['createdAt']) : null,
      locale: attributes?['locale'] as String?,
      imageUrl: attributes?['image']?['image']?['data']?['attributes']?['url'] ?? "",
    );
  }
}
