import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart';
import 'package:kurdsat/src/features/kurdsat_news/related_news/data/models/related_news_model.dart';
import 'package:kurdsat/src/features/kurdsat_news/related_news/data/repositories/related_news_reposetory_impl.dart';
import 'package:kurdsat/src/features/kurdsat_news/related_news/data/datasources/related_news_remote_data_source.dart' show relatedNewsDatasourceProvider;

abstract class RelatedNewsRepository {
  Future<Either<ErrorModel, List<RelatedNewsDataModel>>> fetchRelatedNews();
}

final relatedNewsRepositoryProvider = Provider<RelatedNewsRepository>((ref) {
  return RelatedNewsRepositoryImpl(relatedNewsRemoteDataSource: ref.read(relatedNewsDatasourceProvider));
});
