import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import "package:kurdsat/src/features/kurdsat_news/related_news/data/models/related_news_model.dart" show RelatedNewsDataModel;
import "package:kurdsat/src/features/kurdsat_news/related_news/domain/repositories/related_news_repository.dart";

class RelatedNewsNotifier extends AsyncNotifier<List<RelatedNewsDataModel>> {
  late RelatedNewsRepository _relatedNewsRepository;

  @override
  FutureOr<List<RelatedNewsDataModel>> build() async {
    _relatedNewsRepository = ref.read(relatedNewsRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();
    final response = await _relatedNewsRepository.fetchRelatedNews();
    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final relatedNewsNotifierProvider = AsyncNotifierProvider<RelatedNewsNotifier, List<RelatedNewsDataModel>>(RelatedNewsNotifier.new);
