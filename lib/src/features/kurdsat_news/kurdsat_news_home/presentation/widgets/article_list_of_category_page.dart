import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart' show SizeExtension;
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart' show ArticleCategoryEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/article_category.dart' show ArticleCategory;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/live_stream_section.dart';

class ArticleListOfCategoryPage extends StatelessWidget {
  final List<ArticleCategoryEntity> categories;

  const ArticleListOfCategoryPage({super.key, required this.categories});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarView(title: "Article Category"),
      backgroundColor: Color(0xff0A0C10),
      body: SingleChildScrollView(
        child: Column(
          children: [
            LiveStreamSection(),
            32.verticalSpace,
            ...categories.map((category) {
              if (category.articles.isEmpty) {
                return const SizedBox();
              }

              return ArticleCategory(category: category);
            }),
            80.verticalSpace,
          ],
        ),
      ),
    );
  }
}
