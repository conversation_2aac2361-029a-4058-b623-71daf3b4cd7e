import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart' show RouteNameEnum;
import 'package:kurdsat/src/core/common/domain/entities/strapi_response_entity.dart' show StrapiItemEntity;
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat/src/core/constants/const.dart';
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart' show BroadcastEntity, BroadcastIdentifierEnum;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/logic/live_broadcast_notifier.dart' show liveBroadcastNotifierProvider;

class LiveStreamSection extends ConsumerStatefulWidget {
  const LiveStreamSection({
    super.key,
  });

  @override
  ConsumerState<LiveStreamSection> createState() => _LiveStreamSectionState();
}

class _LiveStreamSectionState extends ConsumerState<LiveStreamSection> {
  @override
  Widget build(BuildContext context) {
    final liveStreamState = ref.watch(liveBroadcastNotifierProvider);

    return liveStreamState.maybeWhen(
      orElse: () => Image.asset("assets/images/liveThumbnailPlaceholder.png", fit: BoxFit.cover),
      data: (List<StrapiItemEntity<BroadcastEntity>> broadcasts) {
        final BroadcastEntity broadcast = broadcasts.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.kurdsatNewsTV).attributes;

        return AspectRatio(
          aspectRatio: 16 / 9,
          child: CupertinoButton(
            onPressed: () {
              context.pushNamed(RouteNameEnum.livePlayer.name, extra: broadcast);
            },
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white12,
                    Colors.black45,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  Image.network(
                    kBaseAssetUrl + (broadcast.image!.data.attributes.url),
                    fit: BoxFit.cover,
                  ),

                  // TODO: make [broadcasts] nullable to avoid null check errors
                  // if (broadcasts == null || broadcasts.attributes.image == null) {
                  //   return Image.asset(
                  //     "assets/images/liveThumbnailPlaceholder.png",
                  //     fit: BoxFit.cover,
                  //   );
                  // }
                  Positioned.fill(
                    child: Container(
                      // background: linear-gradient(180deg, rgba(20, 20, 20, 0) 0%, #141414 100%);
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Color(0xff141414),
                          ],
                        ),
                      ),
                    ),
                  ),

                  Center(
                    child: Icon(
                      Icons.play_circle_outline_rounded,
                      size: 60.sp,
                      color: Colors.white,
                    ),
                  ),
                  Positioned(
                    top: 20.r,
                    left: 20.r,
                    child: SvgPicture.asset(
                      "assets/icons/live_icon.svg",
                      fit: BoxFit.cover,
                    ),
                  ),

                  Positioned(
                    bottom: 20.r,
                    left: 20.r,
                    child: TextView(
                      text: 'Live Stream',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text("Error: $error"),
    );

    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white12,
              Colors.black45,
            ],
          ),
        ),
        child: Stack(
          children: [
            liveStreamState.maybeWhen(
              data: (List<StrapiItemEntity<BroadcastEntity>> data) {
                final StrapiItemEntity<BroadcastEntity> broadcasts = data.firstWhere((broadcast) => broadcast.attributes.identifier == BroadcastIdentifierEnum.kurdsatNewsTV);

                return Image.network(
                  kBaseAssetUrl + (broadcasts.attributes.image!.data.attributes.url),
                  fit: BoxFit.cover,
                );

                // TODO: make [broadcasts] nullable to avoid null check errors
                // if (broadcasts == null || broadcasts.attributes.image == null) {
                //   return Image.asset(
                //     "assets/images/liveThumbnailPlaceholder.png",
                //     fit: BoxFit.cover,
                //   );
                // }
              },
              orElse: () => Image.asset(
                "assets/images/liveThumbnailPlaceholder.png",
                fit: BoxFit.cover,
              ),
            ),

            Positioned.fill(
              child: Container(
                // background: linear-gradient(180deg, rgba(20, 20, 20, 0) 0%, #141414 100%);
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Color(0xff141414),
                    ],
                  ),
                ),
              ),
            ),

            Center(
              child: Icon(
                Icons.play_circle_outline_rounded,
                size: 60.sp,
                color: Colors.white,
              ),
            ),
            Positioned(
              top: 20.r,
              left: 20.r,
              child: SvgPicture.asset(
                "assets/icons/live_icon.svg",
                fit: BoxFit.cover,
              ),
            ),

            Positioned(
              bottom: 20.r,
              left: 20.r,
              child: TextView(
                text: 'Live Stream',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
