import 'package:flutter/material.dart' show Widget, BuildContext, Scaffold;
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/appbar/app_bar_view.dart';
import 'package:kurdsat/src/core/common/widgets/empty_state_view.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/pages/kurdsat_news_home_page.dart' show ArticleCategoryList;

class ArticleCategoryPage extends StatelessWidget {
  final List<ArticleEntity> articles;

  const ArticleCategoryPage({super.key, required this.articles});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar<PERSON>iew(
        title: "Article Category",
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            12.verticalSpace,
            if (articles.isNotEmpty) ...[
              ArticleCategoryList(articles: articles),
            ] else ...[
              const Center(
                child: EmptyStateView(),
              ),
            ],
            // ArticleCategoryList(articles: articles),
          ],
        ),
      ),
    );
  }
}
