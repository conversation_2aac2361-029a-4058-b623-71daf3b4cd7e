import 'package:flutter/material.dart' show Build<PERSON>ontext, Column, EdgeInsets, Padding, Widget, StatelessWidget;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/app/routes/router.dart' show RouteNameEnum;
import 'package:kurdsat/src/core/common/widgets/article_card_carosel.dart' show ArticleCardCarosel;
import 'package:kurdsat/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import 'package:kurdsat/src/core/common/widgets/news_card/news_card_view.dart' show NewsCardView;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart' show ArticleCategoryEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;

import '../../../../../core/constants/const.dart' show kBaseAssetUrl;

class ArticleCategory extends StatelessWidget {
  final ArticleCategoryEntity category;

  const ArticleCategory({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        24.verticalSpace,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.r),
          child: CategoryTitleSection(
            title: category.title ?? "Unknown",
            onPressed: () {
              context.pushNamed(RouteNameEnum.articleCategory.name, extra: category.articles);
            },
          ),
        ),
        16.verticalSpace,

        ArticleCardCarosel(
          carouselHeight: 300.r,
          items: [
            ...category.articles.map(
              (ArticleEntity article) {
                return NewsCardView(
                  imageUrl: "$kBaseAssetUrl${article.image?.url ?? ""}",
                  dutation: article.publishedAt,
                  title: article.title ?? "",
                  author: article.author?.fullName,
                  articleId: article.id,
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
