import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show Color, FontWeight, HapticFeedback, Size;
import 'package:flutter_screenutil/flutter_screenutil.dart' show SizeExtension;
import 'package:flutter_riverpod/flutter_riverpod.dart' show AsyncValueX, ConsumerState, ConsumerStatefulWidget;
import 'package:go_router/go_router.dart' show GoRouterHelper;
import 'package:kurdsat/src/core/common/widgets/empty_state_view.dart' show EmptyStateView;
import 'package:kurdsat/src/core/common/widgets/loading_view.dart' show LoadingView;
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart' show TextView;
import 'package:kurdsat/src/core/constants/const.dart';
import 'package:kurdsat/src/core/extensions/date_formatter.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_data_entity.dart';

import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart' show KurdsatNewsHomeResponseEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/logic/kurdsat_news_home_notifier.dart' show kurdsatNewsHomeNotifierProvider;
import 'package:kurdsat/src/core/common/widgets/news_tile_view.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/article_category.dart' show ArticleCategory;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/live_stream_section.dart';

class KurdsatNewsHomePage extends ConsumerStatefulWidget {
  const KurdsatNewsHomePage({super.key});

  @override
  ConsumerState<KurdsatNewsHomePage> createState() => _KurdsatNewsHomePageState();
}

class _KurdsatNewsHomePageState extends ConsumerState<KurdsatNewsHomePage> with TickerProviderStateMixin {
  TabController? _tabController;
  List<KurdsatNewsHomeDataEntity> tabs = [];
  int selectedTabIndex = 0;
  bool isTabsLoaded = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 1,
      vsync: this,
    );
  }

  void _setupTabsFromData(KurdsatNewsHomeResponseEntity data) {
    if (mounted) {
      setState(() {
        tabs = data.data ?? [];
        isTabsLoaded = true;

        _tabController?.dispose();
        _tabController = TabController(
          length: tabs.length,
          vsync: this,
          initialIndex: selectedTabIndex.clamp(0, tabs.length - 1),
        );

        _tabController!.addListener(() {
          if (_tabController!.indexIsChanging) {
            setState(() {
              selectedTabIndex = _tabController!.index;
            });
          }
        });
      });
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sectionState = ref.watch(kurdsatNewsHomeNotifierProvider);

    sectionState.whenOrNull(
      data: (data) {
        if (!isTabsLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _setupTabsFromData(data);
          });
        }
      },
    );

    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: Color(0xff14191F),
        leading: context.canPop()
            ? BackButton(
                color: Colors.white,
                onPressed: () {
                  context.pop();
                },
              )
            : null,
        title: const TextView(
          text: "Kurdsat News",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        bottom: isTabsLoaded && tabs.isNotEmpty
            ? PreferredSize(
                preferredSize: Size.fromHeight(32),
                child: Container(
                  color: Color(0xff14191F),
                  child: TabBar(
                    isScrollable: true,
                    indicatorColor: Color(0xffF5F4F4),
                    labelColor: Color(0xffF5F4F4),
                    unselectedLabelColor: Color(0xffA2B0C3),
                    labelStyle: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontWeight: FontWeight.normal,
                      fontSize: 14.sp,
                    ),
                    dividerHeight: 0,
                    tabAlignment: TabAlignment.start,
                    indicatorPadding: EdgeInsets.zero,
                    labelPadding: EdgeInsets.zero,
                    indicator: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Color(0xffF5F4F4), width: 0)),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 0),
                    onTap: (value) => HapticFeedback.mediumImpact(),
                    tabs: List.generate(tabs.length, (index) {
                      final tab = tabs[index];
                      final isLast = index == tabs.length - 1;

                      return Tab(
                        height: 29.r,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(tab.title ?? "Unknown"),
                            if (!isLast) ...[
                              const SizedBox(width: 14),
                              Container(
                                width: 1,
                                height: 16,
                                color: Color(0xff28323E),
                              ),
                              const SizedBox(width: 14),
                            ],
                          ],
                        ),
                      );
                    }),
                    controller: _tabController,
                  ),
                ),
              )
            : null,
      ),
      body: sectionState.when(
        data: (data) {
          if (!isTabsLoaded || tabs.isEmpty) {
            return const Center(
              child: TextView(
                text: "Loading tabs...",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            );
          }

          return TabBarView(
            physics: NeverScrollableScrollPhysics(),
            controller: _tabController,
            children: tabs.map<Widget>(
              (tab) {
                return SingleChildScrollView(
                  child: Column(
                    children: [
                      if (tab.articleCategories.isNotEmpty) ...[
                        LiveStreamSection(),
                        32.verticalSpace,
                        ...tab.articleCategories.map((category) {
                          if (category.articles.isEmpty) {
                            return const SizedBox();
                          }

                          return ArticleCategory(category: category);
                        }),
                        40.verticalSpace,
                      ] else if (tab.articles.isNotEmpty) ...[
                        16.verticalSpace,
                        ArticleCategoryList(articles: tab.articles),
                      ] else ...[
                        const Center(
                          child: EmptyStateView(),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ).toList(),
          );
        },
        loading: () => const Center(
          child: LoadingView(),
        ),
        error: (error, stackTrace) => Center(
          child: TextView(
            text: "Error loading data: $error",
            style: const TextStyle(color: Colors.red, fontSize: 16),
          ),
        ),
      ),
    );
  }
}

class ArticleCategoryList extends StatelessWidget {
  final List<ArticleEntity> articles;

  const ArticleCategoryList({super.key, required this.articles});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ...articles.map((ArticleEntity article) {
          return NewsTileView(
            articleId: article.id,
            imageUrl: "$kBaseAssetUrl${article.image?.url}",
            title: article.title ?? "",
            subtitle: article.publishedAt?.getSimpleRelativeTime() ?? "",
          );
        }),
      ],
    );
  }
}
