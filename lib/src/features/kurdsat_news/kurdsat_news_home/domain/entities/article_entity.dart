// lib/entities/news_response_entity.dart

import 'package:equatable/equatable.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/author_entity.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/image_entity.dart';

class ArticleEntity extends Equatable {
  final int? id;
  final String? title;
  final DateTime? publishedAt;
  final ImageEntity? image;
  final AuthorEntity? author;

  const ArticleEntity({
    this.id,
    this.title,
    this.publishedAt,
    this.image,
    this.author,
  });

  @override
  List<Object?> get props => [id, title, publishedAt, image, author];
}
