// lib/entities/news_response_entity.dart

import 'package:equatable/equatable.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart';

class ArticleCategoryEntity extends Equatable {
  final int? id;
  final String? title;
  final int? order;
  final List<ArticleEntity> articles;

  const ArticleCategoryEntity({
    this.id,
    this.title,
    this.order,
    required this.articles,
  });

  @override
  List<Object?> get props => [id, title, order, articles];
}
