// lib/entities/news_response_entity.dart

import 'package:equatable/equatable.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_data_entity.dart';

class KurdsatNewsHomeResponseEntity extends Equatable {
  final List<KurdsatNewsHomeDataEntity>? data;

  const KurdsatNewsHomeResponseEntity({
    this.data,
  });

  @override
  List<Object?> get props => [data];
}
