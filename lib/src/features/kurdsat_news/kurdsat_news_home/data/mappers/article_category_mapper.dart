// lib/mappers/news_response_mapper.dart

import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/article_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/article_category_model.dart' show ArticleCategoryModel;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart' show ArticleCategoryEntity;

class ArticleCategoryMapper {
  static ArticleCategoryEntity toEntity(ArticleCategoryModel? model) {
    if (model == null) return const ArticleCategoryEntity(articles: []);

    return ArticleCategoryEntity(
      id: model.id,
      title: model.title,
      order: model.order,
      articles: model.articles!.map((article) => ArticleMapper.toEntity(article)).toList(),
    );
  }
}
