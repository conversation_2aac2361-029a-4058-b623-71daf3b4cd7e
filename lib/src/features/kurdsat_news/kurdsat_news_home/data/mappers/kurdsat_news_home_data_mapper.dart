// lib/mappers/news_response_mapper.dart

import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/article_category_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/article_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/kurdsat_news_home_data_model.dart' show KurdsatNewsHomeDataModel;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_data_entity.dart';

class KurdsatNewsHomeDataMapper {
  static KurdsatNewsHomeDataEntity toEntity(KurdsatNewsHomeDataModel? model) {
    if (model == null) return const KurdsatNewsHomeDataEntity(viewMode: '', articleCategories: [], articles: []);

    return KurdsatNewsHomeDataEntity(
      id: model.id,
      title: model.title,
      value: model.value,
      order: model.order,
      viewMode: model.viewMode ?? "",
      locale: model.locale,
      articleCategories: model.articleCategories!.map((category) => ArticleCategoryMapper.toEntity(category)).toList(),
      articles: model.articles!.map((article) => ArticleMapper.toEntity(article)).toList(),
    );
  }
}
