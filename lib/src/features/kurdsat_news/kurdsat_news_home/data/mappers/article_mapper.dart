// lib/mappers/news_response_mapper.dart

import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/author_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/image_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/article_model.dart' show ArticleModel;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/image/parent_image_model.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;

import '../../../../../core/utils/helpers/date_formatter.dart' show parseDateTime;

class ArticleMapper {
  static ArticleEntity toEntity(ArticleModel? model) {
    if (model == null) return const ArticleEntity();

    return ArticleEntity(
      id: model.id,
      title: model.title,
      publishedAt: parseDateTime(model.publishedAt),
      image: ImageMapper.toEntity(ParentImageModel(image: model.image?.image)),
      author: AuthorMapper.toEntity(model.author),
    );
  }
}
