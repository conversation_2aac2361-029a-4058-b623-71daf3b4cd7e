// lib/mappers/news_response_mapper.dart
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/mappers/kurdsat_news_home_data_mapper.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/kurdsat_news_home_response_model.dart' show KurdsatNewsHomeResponseModel;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart' show KurdsatNewsHomeResponseEntity;

class KurdsatNewsHomeResponseMapper {
  KurdsatNewsHomeResponseEntity toEntity(KurdsatNewsHomeResponseModel? model) {
    if (model == null) return const KurdsatNewsHomeResponseEntity();

    return KurdsatNewsHomeResponseEntity(
      data: model.data?.map((category) => KurdsatNewsHomeDataMapper.toEntity(category)).toList(),
    );
  }
}
