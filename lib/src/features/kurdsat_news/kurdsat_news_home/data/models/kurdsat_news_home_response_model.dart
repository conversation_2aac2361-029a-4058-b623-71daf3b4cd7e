import 'package:json_annotation/json_annotation.dart' show JsonSerializable, JsonKey;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/kurdsat_news_home_data_model.dart' show KurdsatNewsHomeDataModel;

part 'kurdsat_news_home_response_model.g.dart';

@JsonSerializable()
class KurdsatNewsHomeResponseModel {
  @JsonKey(name: 'data')
  final List<KurdsatNewsHomeDataModel>? data;

  const KurdsatNewsHomeResponseModel({
    this.data,
  });

  factory KurdsatNewsHomeResponseModel.fromJson(Map<String, dynamic> json) => _$KurdsatNewsHomeResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$KurdsatNewsHomeResponseModelToJson(this);
}
