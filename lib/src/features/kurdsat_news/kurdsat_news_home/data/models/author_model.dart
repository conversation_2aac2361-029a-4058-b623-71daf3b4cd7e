import 'package:json_annotation/json_annotation.dart' show JsonSerializable, JsonKey;

part 'author_model.g.dart';

@JsonSerializable()
class AuthorModel {
  @JsonKey(name: 'id')
  final int? id;

  @JsonKey(name: 'full_name')
  final String? fullName;

  const AuthorModel({
    this.id,
    this.fullName,
  });

  factory AuthorModel.fromJson(Map<String, dynamic> json) => _$AuthorModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorModelToJson(this);
}
