import 'package:json_annotation/json_annotation.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/data/models/article_model.dart' show ArticleModel;

part 'article_category_model.g.dart';

@JsonSerializable()
class ArticleCategoryModel {
  @Json<PERSON>ey(name: 'id')
  final int? id;

  @Json<PERSON>ey(name: 'title')
  final String? title;

  @JsonKey(name: 'order')
  final int? order;

  @<PERSON>son<PERSON>ey(name: 'articles')
  final List<ArticleModel>? articles;

  const ArticleCategoryModel({
    this.id,
    this.title,
    this.order,
    this.articles,
  });

  factory ArticleCategoryModel.fromJson(Map<String, dynamic> json) => _$ArticleCategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleCategoryModelToJson(this);
}
