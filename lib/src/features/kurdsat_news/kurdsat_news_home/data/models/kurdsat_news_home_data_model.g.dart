// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'kurdsat_news_home_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KurdsatNewsHomeDataModel _$KurdsatNewsHomeDataModelFromJson(
  Map<String, dynamic> json,
) => KurdsatNewsHomeDataModel(
  id: (json['id'] as num?)?.toInt(),
  title: json['title'] as String?,
  value: json['value'] as String?,
  order: (json['order'] as num?)?.toInt(),
  viewMode: json['view_mode'] as String?,
  locale: json['locale'] as String?,
  articleCategories:
      (json['article_categories'] as List<dynamic>?)
          ?.map((e) => ArticleCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  articles:
      (json['articles'] as List<dynamic>?)
          ?.map((e) => ArticleModel.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$KurdsatNewsHomeDataModelToJson(
  KurdsatNewsHomeDataModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'value': instance.value,
  'order': instance.order,
  'view_mode': instance.viewMode,
  'locale': instance.locale,
  'article_categories': instance.articleCategories,
  'articles': instance.articles,
};
