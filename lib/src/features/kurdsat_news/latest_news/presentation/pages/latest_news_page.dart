import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart' show TextView;
import 'package:kurdsat/src/core/constants/const.dart';

import 'package:kurdsat/src/features/common/main/presentation/logic/latest_news_notifier.dart' show latestNewsNotifierProvider;

class LatestNewsPage extends ConsumerStatefulWidget {
  const LatestNewsPage({super.key});

  @override
  ConsumerState<LatestNewsPage> createState() => _LatestNewsPageState();
}

class _LatestNewsPageState extends ConsumerState<LatestNewsPage> {
  @override
  Widget build(BuildContext context) {
    final latestNewsState = ref.watch(latestNewsNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: Color(0xff14191F),
        leading: context.canPop()
            ? BackButton(
                color: Colors.white,
                onPressed: () {
                  context.pop();
                },
              )
            : null,
        title: const TextView(
          text: "Choose a channel",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,

            // Continue
            latestNewsState.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => Column(
                children: data
                    .map(
                      (news) => Card(
                        color: Colors.white10,
                        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        child: ListTile(
                          leading: news.imageUrl != "" ? Image.network(kBaseAssetUrl + news.imageUrl!, width: 60, height: 60, fit: BoxFit.cover) : const Icon(Icons.image, size: 60),
                          title: Text(news.title ?? 'No Title', style: const TextStyle(color: Colors.white)),
                          subtitle: Text(
                            news.createdAt != null ? news.createdAt!.toLocal().toString().substring(0, 16) : '',
                            style: const TextStyle(color: Colors.white70),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => Text("Error: $error"),
            ),
          ],
        ),
      ),
    );
  }
}
