import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/datasources/kurd_bin_remote_data_source.dart' show KurdBinRemoteDataSource;
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/mappers/kurd_bin_main_page_mapper.dart' show KurdBinMainPageMapper;
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart' show KurdBinMainPageEntity;
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/domain/repositories/kurd_bin_main_page_repository.dart' show KurdBinMainPageRepository;

import '../models/kurdbin_section_model.dart';

class KurdBinMainPageRepositoryImpl implements KurdBinMainPageRepository {
  final KurdBinRemoteDataSource kurdBinMainPageRemoteDataSource;

  KurdBinMainPageRepositoryImpl({required this.kurdBinMainPageRemoteDataSource});

  @override
  Future<Either<ErrorModel, KurdBinMainPageEntity>> fetchKurdBinMainPage() async {
    try {
      final result = await kurdBinMainPageRemoteDataSource.fetchKurdBinMainPage();

      List<KurdBinSectionModel> list = kurdBinMainPageModelDataFromJson(result);

      KurdBinMainPageMapper mapper = KurdBinMainPageMapper(list: list);

      final KurdBinMainPageEntity finalResponse = mapper.toKurdBinMainPageEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
