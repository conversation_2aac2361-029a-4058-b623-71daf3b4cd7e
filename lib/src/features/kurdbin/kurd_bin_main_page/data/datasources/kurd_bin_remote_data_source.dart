import 'dart:convert' show json;
import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class KurdBinRemoteDataSource {
  final HttpManager httpManager;

  KurdBinRemoteDataSource({required this.httpManager});

  Future<List<dynamic>> fetchKurdBinMainPage() async {
    final response = await httpManager.request(
      path: Api().kurdBinHomePage,
      params: {
        "videosLimit": 10,
        "limit": 10,
      },
      method: HttpMethods.get,
      headers: {
        'Authorization': 'Bearer ${dotenv.env['KURDBINTOKEN']}',
      },
    );

    return json.decode(response.data as String)['data'] as List<dynamic>;
  }

  Future<Map<String, dynamic>> getSeries(id, page, pageSize) async {
    final response = await httpManager.request(
      path: Api().kurdBinSeries,
      params: {
        'fields[0]': 'title',
        'fields[1]': 'locale',
        'sort[0]': 'id:desc',
        'pagination[pageSize]': pageSize,
        'pagination[page]': page,
        'populate[thumbnail][fields][0]': 'url',
        'filters[types][id][\$eq]': id,
      },
      method: HttpMethods.get,
      headers: {
        'Authorization': 'Bearer ${dotenv.env['KURDBINTOKEN']}',
      },
    );
    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final kurdbinMainPageDatasourceProvider = Provider<KurdBinRemoteDataSource>((ref) {
  return KurdBinRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
