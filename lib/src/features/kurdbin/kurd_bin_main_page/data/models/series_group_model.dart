import 'package:kurdsat/src/core/utils/helpers/image_url_builder.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/models/banner_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/models/thumbnail_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurdbin_show_card.dart';

class SeriesGroupModel {
  int? id;
  String? title;
  BannerModel? bannerModel;
  ThumbnailModel? thumbnail;

  SeriesGroupModel(this.id, this.title, this.bannerModel, this.thumbnail);

  SeriesGroupModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    bannerModel = json["banner"] != null ? BannerModel.fromJson(json['banner']) : null;
    thumbnail = json['thumbnail'] != null ? ThumbnailModel.fromJson(json['thumbnail']) : null;
  }

  KurdbinShowCardEntity toKurdbinShowCardEntity() {
    return KurdbinShowCardEntity(id: id, title: title ?? "", image: constructKurdbinImageUrl(thumbnail?.url ?? ""));
  }
}
