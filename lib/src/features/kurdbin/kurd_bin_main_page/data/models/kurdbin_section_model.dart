import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/models/series_group_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/models/video_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurdbin_section.dart';

import '../../domain/entities/kurdbin_show_card.dart';
import '../enums/image_orientation_enum.dart';

List<KurdBinSectionModel> kurdBinMainPageModelDataFromJson(List<dynamic> json) => List<KurdBinSectionModel>.from((json).map((x) => KurdBinSectionModel.fromJson(x)));

class KurdBinSectionModel {
  int? id;
  String? title;
  String? thumbnailOrientation;
  String? locale;
  String? hasSeries;
  List<SeriesGroupModel>? seriesGroupList;
  List<VideoModel>? videos;

  KurdBinSectionModel({this.id, this.title, this.locale, this.hasSeries, this.seriesGroupList, this.videos});

  KurdBinSectionModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    thumbnailOrientation = json['thumbnail_orientation'];
    hasSeries = json['has_series'];
    seriesGroupList = (json['series_groups'] as List).map((e) => SeriesGroupModel.fromJson(e)).toList();
    videos = (json['videos'] as List).map((e) => VideoModel.fromJson(e)).toList();
  }

  KurdBinSectionEntity toKurdBinSectionEntity() {
    return KurdBinSectionEntity(
      id: id ?? 0,
      title: title ?? "",
      showsList: _getShowList(),
      orientationEnum: mapThumbnailOrientation(thumbnailOrientation ?? "horizontal"),
      hasSeries: seriesGroupList?.isNotEmpty ?? false,
    );
  }

  List<KurdbinShowCardEntity> _getShowList() {
    List<KurdbinShowCardEntity> list = [];
    if (seriesGroupList?.isNotEmpty ?? false) {
      for (SeriesGroupModel item in seriesGroupList ?? []) {
        list.add(item.toKurdbinShowCardEntity());
      }
    }
    if (videos?.isNotEmpty ?? false) {
      for (VideoModel item in videos ?? []) {
        list.add(item.toKurdbinShowCardEntity());
      }
    }
    return list;
  }

  OrientationEnum mapThumbnailOrientation(String value) {
    return {
          "vertical": OrientationEnum.portrait,
          "horizontal": OrientationEnum.landscape,
        }[value] ??
        (throw Exception("Invalid thumbnailOrientation: $value"));
  }
}
