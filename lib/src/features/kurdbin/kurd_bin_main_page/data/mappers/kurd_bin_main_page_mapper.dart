import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/models/kurdbin_section_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart' show KurdBinMainPageEntity;

class KurdBinMainPageMapper {
  final List<KurdBinSectionModel> list;

  KurdBinMainPageMapper({required this.list});

  KurdBinMainPageEntity toKurdBinMainPageEntity() {
    return KurdBinMainPageEntity(kurdBinSections: list.map((e) => e.toKurdBinSectionEntity()).toList());
  }
}
