import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/presentation/logic/kurd_bin_main_page_notifier.dart' show kurdBinMainPageNotifierProvider, kurd_bin_main_pageNotifierProvider;
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/presentation/widgets/kurdbin_page_content.dart';
import '../../../../../core/common/widgets/news_card/news_card_view.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../data/enums/image_orientation_enum.dart';
import '../../domain/entities/kurdbin_section.dart';
import '../../domain/entities/kurdbin_show_card.dart';
import '../widgets/vertical_design_grid_view.dart';

class KurdBinMainPagePage extends ConsumerStatefulWidget {
  const KurdBinMainPagePage({super.key});

  @override
  ConsumerState<KurdBinMainPagePage> createState() => _KurdBinMainPagePageState();
}

class _KurdBinMainPagePageState extends ConsumerState<KurdBinMainPagePage> with TickerProviderStateMixin {
  int selectedTabIndex = 0;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final kurdBinMainPageState = ref.watch(kurdBinMainPageNotifierProvider);
    return Scaffold(
      appBar: kurdBinMainPageState.maybeWhen(
        orElse: () {
          return PreferredSize(preferredSize: Size(0, 0), child: SizedBox());
        },
        data: (data) {
          _tabController = TabController(
            length: data.kurdBinSections.length + 1,
            vsync: this,
            initialIndex: selectedTabIndex,
          );
          _tabController.addListener(() {
            if (_tabController.indexIsChanging) {
              setState(() {
                selectedTabIndex = _tabController.index;
              });
            }
          });

          return AppBar(
            backgroundColor: Color(0xff14191F),
            leading: BackButton(
              color: Colors.white,
              onPressed: () {
                context.pop();
              },
            ),
            title: const TextView(
              text: "Kurdbin",
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
            elevation: 0,
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(32),
              child: Container(
                color: Color(0xff14191F),
                child: TabBar(
                  isScrollable: true,
                  indicatorColor: Color(0xffF5F4F4),

                  labelColor: Color(0xffF5F4F4),
                  unselectedLabelColor: Color(0xffA2B0C3),
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 14.sp,
                  ),
                  dividerHeight: 0,
                  tabAlignment: TabAlignment.start,
                  indicatorPadding: EdgeInsets.zero,
                  labelPadding: EdgeInsets.zero,
                  indicator: BoxDecoration(
                    border: Border(bottom: BorderSide(color: Color(0xffF5F4F4), width: 0)),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 0),
                  onTap: (value) => HapticFeedback.mediumImpact(),
                  tabs: [
                    Tab(
                      height: 29.r,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text("هەموو"),

                          Container(
                            width: 1,
                            height: 16,
                            color: Color(0xff28323E),
                          ),
                          const SizedBox(width: 14),
                        ],
                      ),
                    ),
                    ...List.generate(data.kurdBinSections.length, (index) {
                      final tab = data.kurdBinSections[index].title;
                      final isLast = index == data.kurdBinSections.length - 1;

                      return Tab(
                        height: 29.r,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(tab),
                            if (!isLast) ...[
                              const SizedBox(width: 14),
                              Container(
                                width: 1,
                                height: 16,
                                color: Color(0xff28323E),
                              ),
                              const SizedBox(width: 14),
                            ],
                          ],
                        ),
                      );
                    }),
                  ],

                  controller: _tabController,
                ),
              ),
            ),
          );
        },
      ),
      backgroundColor: Color(0xff0A0C10),

      body: kurdBinMainPageState.maybeWhen(
        orElse: () => Text("Loading..."),
        data: (data) => TabBarView(
          controller: _tabController,
          children: [
            _buildAllTab(data),
            ...data.kurdBinSections.map((section) => _buildSectionTab(section)),
          ],
        ),
        loading: () => Center(child: CircularProgressIndicator()),
        error: (error, stack) => Text("Error: $error"),
      ),
    );
  }

  _buildAllTab(data) {
    return KurdbinPageContent(
      data: data,
    );
  }

  Widget _buildSectionTab(KurdBinSectionEntity section) {
    if (section.orientationEnum == OrientationEnum.landscape) {
      return _buildHorizontalLayout(section.showsList);
    } else {
      return _buildVerticalLayout(section.showsList, section.hasSeries);
    }
  }

  Widget _buildHorizontalLayout(List<KurdbinShowCardEntity> shows) {
    return SingleChildScrollView(
      child: Column(
        children: [
          24.verticalSpace,
          GridView(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
            ),
            children: shows.map((show) {
              return InkWell(
                onTap: () {
                  context.push("/kurdbin_videos_page/${show.id}/true");
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6.r),
                  child: Image.network(show.image, fit: BoxFit.fill),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // Vertical layout for section tabs
  Widget _buildVerticalLayout(List<KurdbinShowCardEntity> shows, bool hasSeries) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: VerticalDesignGridView(
        hasSeries: hasSeries,
        showList: shows,
        scrollPhysics: ScrollPhysics(),
        mainAxisSpacing: 16.r,
      ),
    );
  }
}
