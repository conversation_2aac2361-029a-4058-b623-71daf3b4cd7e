import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../kurdbin_series_list/data/enums/series_page_content_type_enum.dart';
import '../../domain/entities/kurdbin_show_card.dart';

class VerticalDesignGridView extends StatelessWidget {
  final List<KurdbinShowCardEntity> showList;
  final ScrollPhysics scrollPhysics;
  final double mainAxisSpacing;
  final bool  hasSeries;

  const VerticalDesignGridView({
    super.key,
    required this.showList,
    required this.scrollPhysics,
    required this.mainAxisSpacing,
    required this.hasSeries,
  });

  @override
  Widget build(BuildContext context) {
    return GridView(
      shrinkWrap: true,
      physics: scrollPhysics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 10,
        mainAxisSpacing: mainAxisSpacing,
        childAspectRatio: .75,
      ),
      children: showList.map((show) {
        return InkWell(
          onTap: (){
            if (show.id != null) {
              final SeriesContentTypeEnum contentTypeEnum = hasSeries ? SeriesContentTypeEnum.series : SeriesContentTypeEnum.videos;
              if (contentTypeEnum == SeriesContentTypeEnum.series) {
                context.push("/kurdbin_series_page/${show.id}");
              } else {
                if (show.videoLink?.isNotEmpty ?? false) {
                  context.push("/kurdbin_video_player_page?url=${Uri.encodeComponent(show.videoLink!)}");
                } else {
                  //todo handle empty video link
                }
              }
            }
          },
          child: AspectRatio(
            aspectRatio: 0.68.r,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6.r),
              child: Image.network(show.image, fit: BoxFit.cover),
            ),
          ),
        );
      }).toList(),
    );
  }
}
