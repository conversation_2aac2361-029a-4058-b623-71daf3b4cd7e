import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/article_card_carosel.dart';
import 'package:kurdsat/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/enums/series_page_content_type_enum.dart';
import '../../domain/entities/kurdbin_section.dart';
import '../../domain/entities/kurdbin_show_card.dart';
import 'kurdbin_card_view.dart';

class HorizontalShowDesignWidget extends StatefulWidget {
  final KurdBinSectionEntity sectionEntity;

  const HorizontalShowDesignWidget({
    super.key,
    required this.sectionEntity,
  });

  @override
  State<HorizontalShowDesignWidget> createState() => _HorizontalShowDesignWidgetState();
}

class _HorizontalShowDesignWidgetState extends State<HorizontalShowDesignWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.r),
          child: CategoryTitleSection(
            title: widget.sectionEntity.title,
            onPressed: () {
              final SeriesContentTypeEnum contentTypeEnum = widget.sectionEntity.hasSeries ? SeriesContentTypeEnum.series : SeriesContentTypeEnum.videos;
              if (contentTypeEnum == SeriesContentTypeEnum.series) {
                context.push("/kurdbin_series_page/${widget.sectionEntity.id}");
              } else {
                context.push("/kurdbin_videos_page/${widget.sectionEntity.id}/false");
              }
            },
          ),
        ),
        16.verticalSpace,
        ArticleCardCarosel(
          items: widget.sectionEntity.showsList.map((KurdbinShowCardEntity item) {
            return KurdBinCardView(
              kurdbinShowCardEntity: item,
              hasSeries: widget.sectionEntity.hasSeries,
            );
          }).toList(),
          carouselHeight: 270.r,
        ),

        16.verticalSpace,
      ],
    );
  }
}
