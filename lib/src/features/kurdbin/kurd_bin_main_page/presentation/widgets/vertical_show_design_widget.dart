import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import '../../../kurdbin_series_list/data/enums/series_page_content_type_enum.dart';
import '../../domain/entities/kurdbin_section.dart';
import 'vertical_design_grid_view.dart';

class VerticalShowDesignWidget extends StatelessWidget {
  final KurdBinSectionEntity sectionEntity;

  const VerticalShowDesignWidget({
    super.key,
    required this.sectionEntity,
  });

  @override
  Widget build(BuildContext context) {
    final hasEnoughShows = sectionEntity.showsList.length >= 2;

    if (!hasEnoughShows) return const SizedBox.shrink();

    final shortenedList = sectionEntity.showsList.take(2).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.r),
      child: Column(
        children: [
          CategoryTitleSection(
            title: sectionEntity.title,
            // id: sectionEntity.id,
            // contentTypeEnum: sectionEntity.hasSeries ? SeriesContentTypeEnum.series : SeriesContentTypeEnum.videos,
            onPressed: () {
              final SeriesContentTypeEnum contentTypeEnum = sectionEntity.hasSeries ? SeriesContentTypeEnum.series : SeriesContentTypeEnum.videos;
              if (contentTypeEnum == SeriesContentTypeEnum.series) {
                context.push("/kurdbin_series_page/${sectionEntity.id}");
              } else {
                context.push("/kurdbin_videos_page/${sectionEntity.id}/false");
              }
            },
          ),
          16.verticalSpace,
          VerticalDesignGridView(
            hasSeries: sectionEntity.hasSeries,
            showList: shortenedList,
            mainAxisSpacing: 12.r,
            scrollPhysics: NeverScrollableScrollPhysics(),
          ),
        ],
      ),
    );
  }
}
