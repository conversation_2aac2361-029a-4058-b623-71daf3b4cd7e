import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat/src/core/extensions/date_formatter.dart';

import '../../../kurdbin_series_list/data/enums/series_page_content_type_enum.dart';
import '../../domain/entities/kurdbin_show_card.dart';

class KurdBinCardView extends StatelessWidget {
  final KurdbinShowCardEntity kurdbinShowCardEntity;
  final bool hasSeries;

  const KurdBinCardView({
    super.key,
    required this.kurdbinShowCardEntity,
    required this.hasSeries,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      minimumSize: Size(0, 0),
      onPressed: () {
        if (kurdbinShowCardEntity.id != null) {
          final SeriesContentTypeEnum contentTypeEnum = hasSeries ? SeriesContentTypeEnum.series : SeriesContentTypeEnum.videos;
          if (contentTypeEnum == SeriesContentTypeEnum.series) {
            context.push("/kurdbin_series_page/${kurdbinShowCardEntity.id}");
          } else {
            if (kurdbinShowCardEntity.videoLink?.isNotEmpty ?? false) {
              inspect("${kurdbinShowCardEntity.videoLink}");
              context.push("/kurdbin_video_player_page?url=${Uri.encodeComponent(kurdbinShowCardEntity.videoLink!)}");
            } else {
              //todo handle empty video link
            }
          }
        }
      },
      child: Container(
        margin: EdgeInsets.only(right: 12).r,
        // clipBehavior: Clip.hardEdge,
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: Color(0xff14191f),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Image.network(
                width: double.infinity,
                kurdbinShowCardEntity.image,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    color: Colors.grey[700],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
            12.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12).r,
              child: DefaultTextStyle(
                style: TextStyle(
                  color: Color(0xffEFF2F5),
                  fontSize: 16.r,
                ),
                child: TextView(
                  text: kurdbinShowCardEntity.title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),

            12.verticalSpace,
          ],
        ),
      ),
    );
  }
}

class NewsCardShimmer extends StatelessWidget {
  const NewsCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 12).r,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Color(0xff14191f),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.grey[700],
            ),
          ),
          12.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12).r,
            child: DefaultTextStyle(
              style: TextStyle(
                color: Color(0xffEFF2F5),
                fontSize: 16.r,
              ),
              child: TextView(
                text: "Title",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          10.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12).r,
            child: DefaultTextStyle(
              style: TextStyle(
                color: Color(0xff576C87),
                fontSize: 12.r,
              ),
              child: TextView(
                text: "kurdsatnews - 10 minuties",
              ),
            ),
          ),
          12.verticalSpace,
        ],
      ),
    );
  }
}
