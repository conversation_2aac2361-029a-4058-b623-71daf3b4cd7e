import 'package:equatable/equatable.dart';

import '../../data/enums/image_orientation_enum.dart';
import 'kurdbin_section.dart';
import 'kurdbin_show_card.dart';

class KurdBinMainPageEntity extends Equatable {
  // final List<String> categories;
  final List<KurdBinSectionEntity> kurdBinSections;

  const KurdBinMainPageEntity({required this.kurdBinSections});

  List<String> get sectionTitles => kurdBinSections.map((section) => section.title).toList();

  List<KurdbinShowCardEntity> get allShows => kurdBinSections.expand((section) => section.showsList).toList();

  List<KurdbinShowCardEntity> getShowsBySection(String sectionTitle) {
    final section = kurdBinSections.firstWhere(
      (sec) => sec.title.toLowerCase() == sectionTitle.toLowerCase(),
      orElse: () => KurdBinSectionEntity(
        id: -1,
        title: '',
        showsList: [],
        orientationEnum: OrientationEnum.landscape,
        hasSeries: false,
      ),
    );
    return section.showsList;
  }

  KurdBinSectionEntity? getSectionByTitle(String sectionTitle) {
    try {
      return kurdBinSections.firstWhere(
        (sec) => sec.title.toLowerCase() == sectionTitle.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  List<KurdBinSectionEntity> get horizontalSections => kurdBinSections.where((section) => section.orientationEnum == OrientationEnum.landscape).toList();

  List<KurdBinSectionEntity> get verticalSections => kurdBinSections.where((section) => section.orientationEnum == OrientationEnum.portrait).toList();


  List<KurdBinSectionEntity> get seriesSections => kurdBinSections.where((section) => section.hasSeries).toList();

  @override
  List<Object?> get props => [kurdBinSections];
}
