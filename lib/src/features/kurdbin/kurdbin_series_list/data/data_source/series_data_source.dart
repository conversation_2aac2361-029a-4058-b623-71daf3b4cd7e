import 'dart:convert' show json;
import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat/src/core/api/api.dart' show Api;
import 'package:kurdsat/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class SeriesDataSource {
  final HttpManager httpManager;

  SeriesDataSource({required this.httpManager});

  Future<Map<String, dynamic>> getSeries({required int pageSize, required int page, required int id}) async {
    final response = await httpManager.request(
      path: Api().kurdBinSeries,
      method: HttpMethods.get,
      params: {
        'fields[0]': 'title',
        'fields[1]': 'locale',
        'sort[0]': 'id:desc',
        'pagination[pageSize]': 20,
        'pagination[page]': 1,
        'populate[thumbnail][fields][0]': 'url',
        'filters[types][id][\$eq]': id,
      },
      headers: {
        'Authorization': 'Bearer ${dotenv.env['KURDBINTOKEN']}',
      },
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> getVideos({required int pageSize, required int page, required int id, required bool isSeriesVideos}) async {
    Map<String, dynamic> params = {'fields[0]': 'title', 'fields[1]': 'video_link', 'fields[2]': 'episode', 'fields[3]': 'locale', 'fields[4]': 'ua_certificate', 'filters[type][id][\$eq]': id, '[populate][banner][populate][portrait][fields][0]': 'url', '[populate][banner][populate][landscape][fields][1]': 'url', 'sort[0]': 'episode:desc', 'pagination[page]': '1', 'pagination[pageSize]': '100', 'populate[type][fields][0]': 'thumbnail_orientation'};

    if (isSeriesVideos) {
      params = {
        'fields[0]': 'title',
        'fields[1]': 'video_link',
        'filters[series_group][id][\$eq]': id,
        'fields[2]': 'episode',
        'populate[thumbnail][fields][0]': 'url',
        'populate[series_group][fields][0]': 'title',
        'populate[series_group][fields][1]': 'locale',
        'fields[3]': 'locale',
        'sort[0]': 'episode',
        'pagination[page]': '1',
        'pagination[pageSize]': '100',
        'fields[4]': 'ua_certificate',
        '[populate][banner][populate][portrait][fields][0]': 'url',
        '[populate][banner][populate][landscape][fields][1]': 'url',
        'populate[series_group][populate][banner][populate][landscape][fields][0]': 'url',
        'populate[series_group][populate][banner][populate][portrait][fields][0]': 'url',
      };
    }

    final response = await httpManager.request(
      path: Api().kurdBinVideos,
      method: HttpMethods.get,
      params: params,
      headers: {
        'Authorization': 'Bearer ${dotenv.env['KURDBINTOKEN']}',
      },
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final seriesDatasourceProvider = Provider<SeriesDataSource>((ref) {
  return SeriesDataSource(httpManager: ref.read(httpManagerProvider));
});
