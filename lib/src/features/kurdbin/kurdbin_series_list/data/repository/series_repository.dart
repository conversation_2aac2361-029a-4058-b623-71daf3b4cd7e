import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/data_source/series_data_source.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/models/series_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/models/videos_response_model.dart';

class SeriesRepository {
  final SeriesDataSource dataSource;

  SeriesRepository({required this.dataSource});

  Future<Either<ErrorModel, SeriesResponseModel>> getSeries({required int pageSize, required int page, required int id}) async {
    try {
      final response = await dataSource.getSeries(id: id, page: page, pageSize: pageSize);

      final result = SeriesResponseModel.fromJson(response);

      return Right(result);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }

  Future<Either<ErrorModel, VideosResponseModel>> getVideos({required int pageSize, required int page, required int id, required bool isSeriesVideos}) async {
    try {
      final response = await dataSource.getVideos(
        id: id,
        page: page,
        pageSize: pageSize,
        isSeriesVideos: isSeriesVideos,
      );

      final result = VideosResponseModel.fromJson(response);

      return Right(result);
    } catch (error, stackTrace) {
      return Left(errorParser(error, stackTrace));
    }
  }
}

final seriesRepositoryProvider = Provider<SeriesRepository>((ref) {
  return SeriesRepository(dataSource: ref.read(seriesDatasourceProvider));
});
