import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'pagination_model.dart';

part 'meta_model.g.dart';

@JsonSerializable()
class MetaModel extends Equatable {
  final PaginationModel? paginationModel;

  const MetaModel({
    this.paginationModel,
  });

  factory MetaModel.fromJson(Map<String, dynamic> json) {
    return _$MetaModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$MetaModelToJson(this);

  @override
  List<Object?> get props => [
    paginationModel,
  ];
}
