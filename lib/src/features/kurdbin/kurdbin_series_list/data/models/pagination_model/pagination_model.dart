import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pagination_model.g.dart';

@JsonSerializable()
class PaginationModel extends Equatable {
  final int? page;
  final int? pageSize;
  final int? pageCount;
  final int? total;

  const PaginationModel({
    this.page,
    this.pageSize,
    this.pageCount,
    this.total,
  });

  factory PaginationModel.fromJson(Map<String, dynamic> json) {
    return _$PaginationModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PaginationModelToJson(this);

  @override
  List<Object?> get props => [
    page,
    pageSize,
    pageCount,
    total,
  ];
}
