import 'package:kurdsat/src/core/constants/const.dart';
import '../../../kurd_bin_main_page/data/enums/image_orientation_enum.dart';
import 'pagination_model/meta_model.dart';

class VideosResponseModel {
  final List<VideoModel> data;
  final MetaModel? metaModel;
  final OrientationEnum orientation;

  VideosResponseModel({
    required this.data,
    required this.metaModel,
    required this.orientation,
  });

  factory VideosResponseModel.fromJson(Map<String, dynamic> json) {
    final dataList = (json['data'] as List<dynamic>? ?? []).map((e) => VideoModel.fromJson(e as Map<String, dynamic>)).toList();

    final meta = json['meta'] != null ? MetaModel.fromJson(json['meta'] as Map<String, dynamic>) : null;

    final orientation = dataList.first.orientation;

    return VideosResponseModel(
      data: dataList,
      metaModel: meta,
      orientation: orientation,
    );
  }

  @override
  String toString() {
    return 'VideosResponseModel{data: $data, metaModel: $metaModel, orientation: $orientation}';
  }
}

class VideoModel {
  final int episode;
  final String title;
  final String locale;
  final String videoLink;
  final String thumbnail;
  final String imageUrl;
  final OrientationEnum orientation;

  VideoModel({
    required this.episode,
    required this.title,
    required this.locale,
    required this.videoLink,
    required this.thumbnail,
    required this.imageUrl,
    required this.orientation,
  });

  factory VideoModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>? ?? {};

    final thumbnailUrl = attributes['thumbnail']?['data']?['attributes']?['url'] as String? ?? '';
    final fullThumbnail = '$kBaseKurdBinAssetUrl$thumbnailUrl';

    final banner = attributes['banner'] as Map<String, dynamic>? ?? {};
    final portraitUrl = banner['portrait']?['data']?['attributes']?['url'] as String?;
    final landscapeUrl = banner['landscape']?['data']?['attributes']?['url'] as String?;
    final bannerUrl = portraitUrl ?? landscapeUrl ?? '';
    final fullBanner = '$kBaseKurdBinAssetUrl$bannerUrl';
    final orientationValue = attributes['type']?['data']?['attributes']?['thumbnail_orientation'];

    return VideoModel(episode: attributes['episode'] ?? 1, title: attributes['title'] ?? '', locale: attributes['locale'] ?? '', videoLink: attributes['video_link'] ?? '', thumbnail: fullThumbnail, imageUrl: fullBanner, orientation: orientationValue == 'vertical' ? OrientationEnum.portrait : OrientationEnum.landscape);
  }

  @override
  String toString() {
    return 'VideoModel{episode: $episode, title: $title, locale: $locale, videoLink: $videoLink, thumbnail: $thumbnail, imageUrl: $imageUrl, orientation: $orientation}';
  }
}
