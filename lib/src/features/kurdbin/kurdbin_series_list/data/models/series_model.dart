import 'package:kurdsat/src/core/constants/const.dart';

import 'pagination_model/meta_model.dart';

class SeriesResponseModel {
  final List<SeriesModel> data;
  final MetaModel? metaModel;

  SeriesResponseModel({
    required this.data,
    required this.metaModel,
  });

  factory SeriesResponseModel.fromJson(Map<String, dynamic> json) {
    return SeriesResponseModel(
      data: (json['data'] as List<dynamic>).map((e) {
        return SeriesModel.fromJson(e as Map<String, dynamic>);
      }).toList(),
      metaModel: MetaModel.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }
}

class SeriesModel {
  int? id;
  String? title;
  String? locale;
  String? imageUrl;

  SeriesModel({
    required this.id,
    this.title,
    this.locale,
    this.imageUrl,
  });

  factory SeriesModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>?;
    return SeriesModel(
      id: json['id'],
      title: attributes?['title'] ?? "",
      locale: attributes?['locale'] ?? "",
      imageUrl: "$kBaseKurdBinAssetUrl${attributes?['thumbnail']?['data']?['attributes']?['url'] ?? ''}",
    );
  }
}
