

class SingleVideoResponseModel {
  final List<SingleVideoModel> data;

  SingleVideoResponseModel({
    required this.data,
  });

  factory SingleVideoResponseModel.fromJson(Map<String, dynamic> json) {
    return SingleVideoResponseModel(
      data: (json['data'] as List<dynamic>).map((e) {
        return SingleVideoModel.fromJson(e as Map<String, dynamic>);
      }).toList(),
    );
  }
}

class SingleVideoModel {
  int? id;
  String? videoLink;

  SingleVideoModel({
    required this.id,
    this.videoLink,
  });

  factory SingleVideoModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>?;
    return SingleVideoModel(
      id: json['id'],
    );
  }
}
