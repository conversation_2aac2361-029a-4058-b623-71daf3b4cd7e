import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/enums/image_orientation_enum.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/models/series_model.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/models/videos_response_model.dart';

import '../../data/repository/series_repository.dart';

class VideosNotifier extends AsyncNotifier<VideosResponseModel> {
  late SeriesRepository _seriesRepositoryProvider;

  @override
  FutureOr<VideosResponseModel> build() async {
    _seriesRepositoryProvider = ref.read(seriesRepositoryProvider);
    return state.value ??
        VideosResponseModel(
          data: [],
          metaModel: null,
          orientation: OrientationEnum.landscape,
        );
  }

  Future<void> getVideosData({
    required int id,
    required bool isSeriesVideos,
  }) async {
    state = const AsyncValue.loading();

    final response = await _seriesRepositoryProvider.getVideos(
      id: id,
      page: 1,
      pageSize: 20,
      isSeriesVideos: isSeriesVideos,
    );

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final videosNotifierProvider = AsyncNotifierProvider<VideosNotifier, VideosResponseModel>(VideosNotifier.new);
