import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/data/models/series_model.dart';

import '../../data/repository/series_repository.dart';

class SeriesNotifier extends AsyncNotifier<SeriesResponseModel> {
  late SeriesRepository _seriesRepositoryProvider;

  @override
  FutureOr<SeriesResponseModel> build() async {
    _seriesRepositoryProvider = ref.read(seriesRepositoryProvider);
    return state.value ?? SeriesResponseModel(data: [], metaModel: null);
  }

  Future<void> getSeriesData({required int id}) async {
    state = const AsyncValue.loading();

    final response = await _seriesRepositoryProvider.getSeries(id: id, page: 1, pageSize: 20);

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final seriesNotifierProvider = AsyncNotifierProvider<SeriesNotifier, SeriesResponseModel>(SeriesNotifier.new);
