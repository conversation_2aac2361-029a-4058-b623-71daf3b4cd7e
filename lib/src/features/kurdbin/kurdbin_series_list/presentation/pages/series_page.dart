import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart' show TextView;
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/presentation/logic/series_notifier.dart';

class SeriesPage extends ConsumerStatefulWidget {
  final int id;

  const SeriesPage({
    super.key,
    required this.id,
  });

  @override
  ConsumerState<SeriesPage> createState() => _SeriesPageState();
}

class _SeriesPageState extends ConsumerState<SeriesPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(seriesNotifierProvider.notifier).getSeriesData(id: widget.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    final seriesState = ref.watch(seriesNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: Color(0xff14191F),
        leading: context.canPop()
            ? BackButton(
                color: Colors.white,
                onPressed: () {
                  context.pop();
                },
              )
            : null,
        title: const TextView(
          text: "",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,

            // Continue
            seriesState.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => GridView(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.5,
                ),
                children: data.data.map((show) {
                  return InkWell(
                    onTap: () {
                      context.push("/kurdbin_videos_page/${show.id}/true");
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6.r),
                      child: Image.network(show.imageUrl ?? "", fit: BoxFit.fill),
                    ),
                  );
                }).toList(),
              ),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => Text(
                "Error: $error",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
