import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/data/enums/image_orientation_enum.dart';
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../logic/videos_notifier.dart';
import '../widgets/video_card_page.dart';

class VideosPage extends ConsumerStatefulWidget {
  final int id;
  final bool isSeriesVideo;

  const VideosPage({
    super.key,
    required this.id,
    required this.isSeriesVideo,
  });

  @override
  ConsumerState<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends ConsumerState<VideosPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(videosNotifierProvider.notifier)
          .getVideosData(
            id: widget.id,
            isSeriesVideos: widget.isSeriesVideo,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final videosState = ref.watch(videosNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: Color(0xff14191F),
        leading: context.canPop()
            ? BackButton(
                color: Colors.white,
                onPressed: () {
                  context.pop();
                },
              )
            : null,
        title: const TextView(
          text: "",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,

            // Continue
            videosState.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => GridView(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 12,
                  childAspectRatio: data.orientation == OrientationEnum.portrait ? .7 : 1.5,
                ),
                children: data.data.map((video) {
                  return VideoCardWidget(
                    video: video,
                  );
                }).toList(),
              ),
              loading: () => Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                "Error: $error",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
