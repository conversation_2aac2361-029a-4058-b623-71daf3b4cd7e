import 'package:go_router/go_router.dart' show Go<PERSON><PERSON><PERSON>, GoRouter;
import 'package:kurdsat/src/features/common/live_broadcast/presentation/pages/live_broadcasts_page.dart' show LiveBroadcastsPage;
import 'package:kurdsat/src/features/common/live_broadcast/domain/entities/broadcast_entity.dart';
import 'package:kurdsat/src/features/common/main/presentation/pages/root_page.dart';
import 'package:kurdsat/src/features/common/notifications/presentation/pages/notifications_page.dart' show NotificationsPage;
import 'package:kurdsat/src/features/common/splash/presentation/pages/splash_page.dart';
import 'package:kurdsat/src/features/kurdbin/kurd_bin_main_page/presentation/pages/kurd_bin_main_page_page.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/presentation/pages/video_player_page.dart';
import 'package:kurdsat/src/features/kurdbin/kurdbin_series_list/presentation/pages/videos_page.dart';
import 'package:kurdsat/src/features/kurdsat/kurdsat_home/presentation/pages/kurdsat_home_page.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart' show ArticleCategoryEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/article_list_of_category_page.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/pages/kurdsat_news_home_page.dart';
import 'package:kurdsat/src/features/kurdsat_news/article/presentation/pages/article_page.dart';
import 'package:kurdsat/src/features/kurdsat_news/kurdsat_news_home/presentation/widgets/article_category_page.dart' show ArticleCategoryPage;
import 'package:kurdsat/src/features/kurdsat_news/latest_news/presentation/pages/latest_news_page.dart' show LatestNewsPage;
import 'package:kurdsat/src/features/kurdsat_news/related_news/presentation/pages/related_news_page.dart' show RelatedNewsPage;

import '../../features/common/live_broadcast/presentation/pages/live_player_page.dart' show LivePlayerPage;

import '../../features/kurdbin/kurdbin_series_list/presentation/pages/series_page.dart';

enum RouteNameEnum {
  splash,
  kurdsatNewsHome,
  article,
  kurdbinHomePage,
  kurdbinSeriesPage,
  kurdbinVideosPage,
  kurdbinVideoPlayerPage,
  kurdsatHome,
  liveBroadcast,
  livePlayer,
  latestNews,
  relatedNews,
  main,
  notifications,
  articleCategory,
  articleListOfCategory,
}

final GoRouter router = GoRouter(
  // initialLocation: "/kurdbin_home_page",
  // initialLocation: "/kurdsat-news",
  // initialLocation: "/live-broadcast",
  // initialLocation: "/latest-news",
  // initialLocation: "/related-news",
  // initialLocation: "/kurdsat",
  initialLocation: "/",
  //
  routes: [
    GoRoute(
      name: RouteNameEnum.main.name,
      path: "/",
      builder: (context, state) => RootPage(),
    ),
    GoRoute(
      name: RouteNameEnum.splash.name,
      path: "/splash",
      builder: (context, state) => SplashPage(),
    ),
    GoRoute(
      name: RouteNameEnum.notifications.name,
      path: "/notifications",
      builder: (context, state) => NotificationsPage(),
    ),

    //
    //
    //
    //   _  __             _           _     _   _
    //  | |/ /            | |         | |   | \ | |
    //  | ' /_   _ _ __ __| |___  __ _| |_  |  \| | _____      _____
    //  |  <| | | | '__/ _` / __|/ _` | __| | . ` |/ _ \ \ /\ / / __|
    //  | . \ |_| | | | (_| \__ \ (_| | |_  | |\  |  __/\ V  V /\__ \
    //  |_|\_\__,_|_|  \__,_|___/\__,_|\__| |_| \_|\___| \_/\_/ |___/
    GoRoute(
      name: RouteNameEnum.kurdsatNewsHome.name,
      path: "/kurdsat-news",
      builder: (context, state) => KurdsatNewsHomePage(),
    ),
    GoRoute(
      name: RouteNameEnum.article.name,
      path: "/article/:id",
      builder: (context, state) {
        final articleId = int.tryParse(state.pathParameters['id'] ?? '');
        if (articleId == null) {
          return const KurdsatNewsHomePage();
        }
        return ArticlePage(articleId: articleId);
      },
    ),
    // category page route
    GoRoute(
      name: RouteNameEnum.articleCategory.name,
      path: "/article_category",
      builder: (context, state) {
        return ArticleCategoryPage(articles: state.extra as List<ArticleEntity>);
      },
    ),
    GoRoute(
      name: RouteNameEnum.articleListOfCategory.name,
      path: "/article_category_list",
      builder: (context, state) {
        return ArticleListOfCategoryPage(categories: state.extra as List<ArticleCategoryEntity>);
      },
    ),

    GoRoute(
      name: RouteNameEnum.liveBroadcast.name,
      path: "/live-broadcast",
      builder: (context, state) => LiveBroadcastsPage(),
    ),
    // Live player route with broadcast parameter
    GoRoute(
      name: RouteNameEnum.livePlayer.name,
      path: "/live-player",
      builder: (context, state) {
        final BroadcastEntity? broadcast = state.extra as BroadcastEntity?;
        if (broadcast == null) {
          return const LiveBroadcastsPage();
        }
        return LivePlayerPage(broadcast: broadcast);
      },
    ),

    GoRoute(
      name: RouteNameEnum.relatedNews.name,
      path: "/related-news",
      builder: (context, state) => const RelatedNewsPage(),
    ),
    GoRoute(
      name: RouteNameEnum.latestNews.name,
      path: "/latest-news",
      builder: (context, state) => const LatestNewsPage(),
    ),

    //
    //
    //
    //   _  __             _ _     _
    //  | |/ /            | | |   (_)
    //  | ' /_   _ _ __ __| | |__  _ _ __
    //  |  <| | | | '__/ _` | '_ \| | '_ \
    //  | . \ |_| | | | (_| | |_) | | | | |
    //  |_|\_\__,_|_|  \__,_|_.__/|_|_| |_|
    GoRoute(
      name: RouteNameEnum.kurdbinHomePage.name,
      path: "/kurdbin_home_page",
      builder: (context, state) => KurdBinMainPagePage(),
    ),
    GoRoute(
      name: RouteNameEnum.kurdbinSeriesPage.name,
      path: "/kurdbin_series_page/:id",
      builder: (context, state) {
        final id = int.tryParse(state.pathParameters['id'] ?? '');

        return SeriesPage(
          id: id as int,
        );
      },
    ),
    GoRoute(
      name: RouteNameEnum.kurdbinVideosPage.name,
      path: "/kurdbin_videos_page/:id/:isSeriesVideo",
      builder: (context, state) {
        final id = int.tryParse(state.pathParameters['id'] ?? '');
        final isSeries = bool.tryParse(state.pathParameters['isSeriesVideo'] ?? '');

        return VideosPage(
          id: id as int,
          isSeriesVideo: isSeries as bool,
        );
      },
    ),
    GoRoute(
      name: RouteNameEnum.kurdbinVideoPlayerPage.name,
      path: "/kurdbin_video_player_page",
      builder: (context, state) {
        final url = state.uri.queryParameters['url'];
        return VideoPlayerPage(
          videoUrl: url ?? '',
        );
      },
    ),

    //
    //
    //
    //   _  __             _           _
    //  | |/ /            | |         | |
    //  | ' /_   _ _ __ __| |___  __ _| |_
    //  |  <| | | | '__/ _` / __|/ _` | __|
    //  | . \ |_| | | | (_| \__ \ (_| | |_
    //  |_|\_\__,_|_|  \__,_|___/\__,_|\__|
    GoRoute(
      name: RouteNameEnum.kurdsatHome.name,
      path: "/kurdsat",
      builder: (context, state) => KurdsatHomePage(),
    ),
  ],
);
