import 'package:flutter/material.dart';

class ColorPalette {
  ////////////////PRIMARY COLORS///////////////////
  ///
  static const Color primaryColor = Color(0xFF0D6EFD);
  static const Color secondaryColor = Color(0xFF6C757D);
  static const Color tertiaryColor = Color(0xFF0DCAF0);

  /////////////////TEXT COLORS////////////////////
  static const Color black = Color(0xFF000000);
  static const Color textGrey = Color(0xFF6C757D);
  static const Color hintColor = Color(0xFFADB5BD);
  static const Color onSurface = Color(0xFF212529);

  ///////////////SURFACE COLORS///////////////////
  ///
  static const Color white = Color(0xFFFFFFFF);
  static const Color scaffold = Color(0xFFF8F9FA);
  // static const Color surface = Color(0xFFF8F9FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color outline = Color(0xFFDEE2E6);
  static const Color outlineDK = Color(0xFFCED4DA);
  static const Color surfaceDim = Color(0xFFE9ECEF);
  static const Color surfaceContainer = Color(0xFFCFE2FF);

  ///////////////ERRORS///////////////////////////
  ///
  static const Color red = Color(0xFFDC3545);
  static const Color darkRed = Color(0xFF58151C);
  static const Color lightRed = Color(0xFFF8D7DA);

  ///////////////CONTAINERS////////////////////////
  ///
  static const Color primaryContainer = Color(0xFFCFE2FF);
  static const Color secondaryContainer = Color(0xFFE2E3E5);

  ///////////////ACCENT COLORS////////////////////////
  ///
  static const Color secondaryLight = Color(0xFFC4C8CB);
  static const Color secondaryLighter = Color(0xFFE2E3E5);
  static const Color secondaryHigh = Color(0xFF495057);
  static const Color primaryLow = Color(0xFF052C65);
  static const Color primaryLower = Color(0xFF0A58CA);

  ///////////////LIGHT COLORS////////////////////////
  ///
  static const Color greenLight = Color(0xFFD1E7DD);
  static const Color greenLighter = Color(0xFFA3CFBB);
  static const Color greenLightest = Color(0xFFD1E7DD);
  static const Color tertiaryLight = Color(0xFFCFF4FC);
  static const Color tertiaryLighter = Color(0xFF9EEAF9);
  static const Color tertiaryLightest = Color(0xFFCFF4FC);

  ///////////////DARK COLORS////////////////////////
  ///
  static const Color darkGreen = Color(0xFF0A3622);
  static const Color darkGreenLight = Color(0xFF198754);
  static const Color darkGreenLighter = Color(0xFF198754);
  static const Color darkGreenDarker = Color(0xFF0A3622);
  static const Color darkGreenDarkest = Color(0xFF0A3622);
  static const Color tertiaryDarkest = Color(0xFF055160);

  ///////////////PROGRESS COLORS////////////////////////
  ///
  static const Color progressRed = Color(0xFFDC3545);
  static const Color progressOrange = Color(0xFFFD7E14);
  static const Color progressYellow = Color(0xFFFFC107);
  static const Color progressGreen = Color(0xFF198754);

  ///////////////DISABLED COLORS////////////////////////
  ///
  static const Color disabledColor = Color(0xFFE6E7E8);
}
