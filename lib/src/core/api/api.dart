import 'package:kurdsat/src/core/constants/const.dart';

const String kGoogleCheckEndpoint = "https://google.com";

class Api {
  String get baseUrl => "";

  //
  //
  //
  //   _  __             _           _     _   _
  //  | |/ /            | |         | |   | \ | |
  //  | ' /_   _ _ __ __| |___  __ _| |_  |  \| | _____      _____
  //  |  <| | | | '__/ _` / __|/ _` | __| | . ` |/ _ \ \ /\ / / __|
  //  | . \ |_| | | | (_| \__ \ (_| | |_  | |\  |  __/\ V  V /\__ \
  //  |_|\_\__,_|_|  \__,_|___/\__,_|\__| |_| \_|\___| \_/\_/ |___/

  String get _baseKurdsatNewsUrl => kBaseKurdsatNewsUrl;

  String get baseKurdsatNewsUrl => _baseKurdsatNewsUrl;

  String _constructKurdsatNewsUrl(String endpoint) => "$_baseKurdsatNewsUrl$endpoint";

  // String get kurdsatNewsHomePage => _constructKurdsatNewsUrl("/mobile/newshomepage?articleLimit=12&articleCategoryLimit=5&locale=en");
  String get kurdsatNewsHomePage => _constructKurdsatNewsUrl("/mobile/newshomepage?locale=en&populate[articles][fields][0]=publishedAt&populate[articles][fields][1]=id&populate[articles][fields][2]=title&populate[articles][populate][image][populate][image][fields][0]=id&populate[articles][populate][image][populate][image][fields][1]=url&populate[articles][populate][image][fields][0]=id&populate[articles][populate][authors][fields][0]=id&populate[articles][populate][authors][fields][1]=full_name&populate[articles][pagination][pageSize]=1&populate[articles][sort][0]=publishedAt:desc&populate[article_category][sort][0]=publishedAt:desc&populate[article_categories][fields][0]=id&populate[article_categories][fields][1]=title&populate[article_categories][fields][2]=order&populate[article_categories][fields][3]=view_mode&populate[article_categories][sort][0]=order:asc&fields[0]=id&fields[1]=title&fields[2]=value&fields[3]=order&fields[4]=locale&filters[publishedAt][\$null]=false&filters[\$or][0][channel][\$ne]=website_only&filters[\$or][1][channel][\$null]=true&populate[articles][filters][publishedAt][\$null]=false&populate[articles][filters][\$or][0][channel][\$ne]=website_only&populate[articles][filters][\$or][1][channel][\$null]=true&populate[article_categories][filters][publishedAt][\$null]=false&populate[article_categories][\$or][0][channel][\$ne]=website_only&populate[article_categories][\$or][1][channel][\$null]=true&populate[article_categories][populate][articles][pagination][pageSize]=2&populate[article_categories][populate][articles][fields][0]=title&populate[article_categories][populate][articles][fields][1]=date&populate[article_categories][populate][articles][fields][2]=publishedAt&populate[article_categories][populate][articles][populate][image][populate][image][fields][0]=url&populate[article_categories][populate][articles][populate][image][fields][0]=id&populate[article_categories][populate][articles][filters][publishedAt][\$null]=false&populate[article_categories][populate][articles][filters][\$or][0][channel][\$ne]=website_only&populate[article_categories][populate][articles][filters][\$or][1][channel][\$null]=true");

  String kurdsatNewsArticle(int articleId) => _constructKurdsatNewsUrl("/articles/custom/$articleId?populate[article_categories][fields][0]=title&populate[article_types][fields][0]=title&populate[image][populate][image][fields][0]=url&populate[author][fields][0]=full_name&populate[image][fields][0]=id&fields[0]=date&fields[1]=title&fields[2]=body&fields[3]=subtitle");

  String get kurdsatNewsChannels => _constructKurdsatNewsUrl("/channels?fields[0]=title&fields[1]=link&populate[channel_type][fields][0]=title&populate[image][fields][0]=url&locale=ckb&filters[channel_type][title][\$in][0]=TV&filters[channel_type][title][\$in][1]=Radio&populate[logo][fields][0]=url&fields[2]=identifier");

  String get latestNews => _constructKurdsatNewsUrl("/articles?sort[0]=date:desc&fields[0]=title&fields[1]=createdAt&fields[2]=locale&populate[image][fields][0]=image&populate[image][populate][image][fields][0]=url&pagination[limit]=4&locale=en&populate[authors][fields][0]=full_name&fields[3]=date");

  String get relatedNews => _constructKurdsatNewsUrl("/articles?sort[0]=date:desc&fields[0]=title&fields[1]=createdAt&fields[2]=locale&populate[image][fields][0]=image&populate[image][populate][image][fields][0]=url&pagination[limit]=12&locale=ckb&filters[article_categories][id][\$eq]=1");

  //
  //
  //
  //   _  __             _ _     _
  //  | |/ /            | | |   (_)
  //  | ' /_   _ _ __ __| | |__  _ _ __
  //  |  <| | | | '__/ _` | '_ \| | '_ \
  //  | . \ |_| | | | (_| | |_) | | | | |
  //  |_|\_\__,_|_|  \__,_|_.__/|_|_| |_|

  String get _kurdBinBaseUrl => "https://test-kurdbin-portal.rndlabs.dev";
  String get kurdBinBaseUrl => _kurdBinBaseUrl;
  String _constructKurdBinUrl(String endpoint) => "$_kurdBinBaseUrl$endpoint";

  String get kurdBinHomePage => _constructKurdBinUrl("/api/mobile/kurdbinhomepage");

  String get kurdBinSeries => _constructKurdBinUrl("/api/series-groups");
  String get kurdBinVideos => _constructKurdBinUrl("/api/videos");

  //
  //
  //
  //   _  __             _           _
  //  | |/ /            | |         | |
  //  | ' /_   _ _ __ __| |___  __ _| |_
  //  |  <| | | | '__/ _` / __|/ _` | __|
  //  | . \ |_| | | | (_| \__ \ (_| | |_
  //  |_|\_\__,_|_|  \__,_|___/\__,_|\__|

  String get _kurdsatBaseUrl => "https://test-kurdsat-portal.rndlabs.dev";
  String get kurdsatBaseUrl => _kurdsatBaseUrl;
  String _constructKurdsat(String endpoint) => "$_kurdsatBaseUrl$endpoint";

  String get kurdsatHomePage => _constructKurdsat("/api/mobile/kurdsathomepage");
}
