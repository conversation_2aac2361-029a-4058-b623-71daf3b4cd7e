import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../app/logic/app_settings.dart';
import '../../../injection.dart';
import '../../enums/secure_storage_key.dart';
import '../../utils/managers/database/database_manager.dart';

class ImageBuilder extends StatelessWidget {
  final String? imageUrl;
  final BoxFit? boxFit;
  final bool isSVG;
  final Color? color;
  final double? width;
  final double? height;
  final Widget? placeholder;
  final Widget? errorWidget;

  const ImageBuilder({
    super.key,
    this.imageUrl,
    this.boxFit,
    this.isSVG = false,
    this.color,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      return ImageProviderForWeb(
        imageUrl: imageUrl,
        boxFit: boxFit,
        color: color,
        width: width,
        hight: height,
      );
    }

    if (isSVG) {
      return ImageProviderForMobileSVG(
        imageUrl: imageUrl,
        boxFit: boxFit,
        color: color,
        width: width,
        hight: height,
        placeholder: placeholder,
      );
    }

    return ImageProviderForMobile(
      imageUrl: imageUrl,
      boxFit: boxFit,
      color: color,
      width: width,
      hight: height,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
}

class ImageProviderForWeb extends StatelessWidget {
  final String? imageUrl;
  final BoxFit? boxFit;
  final Color? color;
  final double? width;
  final double? hight;

  const ImageProviderForWeb({
    super.key,
    this.imageUrl,
    this.boxFit,
    this.color,
    this.width,
    this.hight,
  });

  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl ?? "",
      fit: boxFit ?? BoxFit.fill,
      width: width,
      height: hight,
      color: color,
      loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            color: Colors.black,
            value: loadingProgress.expectedTotalBytes != null ? loadingProgress.cumulativeBytesLoaded / (loadingProgress.expectedTotalBytes ?? 0) : null,
          ),
        );
      },
    );
  }
}

class ImageProviderForMobile extends StatelessWidget {
  final String? imageUrl;
  final BoxFit? boxFit;
  final Color? color;
  final double? width;
  final double? hight;
  final Widget? placeholder;
  final Widget? errorWidget;

  const ImageProviderForMobile({
    super.key,
    this.imageUrl,
    this.boxFit,
    this.color,
    this.width,
    this.hight,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      httpHeaders: {
        "Authorization": "Bearer ${serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token)}",
        "lang": "en",
        "X-channel": serviceLocator<AppSettings>().xChannel,
        "Accept-Language": serviceLocator<AppSettings>().selectedLanguage.backendLangCode,
      },
      imageUrl: imageUrl ?? "",
      width: width,
      height: hight,
      color: color,
      fit: boxFit ?? BoxFit.fill,
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          image: DecorationImage(image: imageProvider, fit: boxFit ?? BoxFit.fill),
        ),
      ),
      errorWidget: (context, url, error) =>
          errorWidget ??
          Icon(
            Icons.image_rounded,
            color: Theme.of(context).primaryColor,
          ),
    );
  }
}

class ImageProviderForMobileSVG extends StatelessWidget {
  final String? imageUrl;
  final BoxFit? boxFit;
  final Color? color;
  final double? width;
  final double? hight;

  final Widget? placeholder;

  const ImageProviderForMobileSVG({
    super.key,
    this.imageUrl,
    this.boxFit,
    this.color,
    this.width,
    this.hight,
    this.placeholder,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.network(
      headers: {
        "X-channel": serviceLocator<AppSettings>().xChannel,
        "Accept-Language": serviceLocator<AppSettings>().selectedLanguage.backendLangCode,
        "lang": "en",
        "Authorization": "Bearer ${serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token)}",
      },
      imageUrl ?? "",
      width: width,
      height: hight,
      colorFilter: color == null
          ? null
          : ColorFilter.mode(
              color!,
              BlendMode.srcIn,
            ),
      fit: boxFit ?? BoxFit.contain,
      placeholderBuilder: (context) =>
          placeholder ??
          Icon(
            Icons.image_rounded,
            color: Theme.of(context).primaryColor,
          ),
    );
  }
}

// class LottieAnimationView extends StatelessWidget {
//   const LottieAnimationView({
//     super.key,
//     this.imageUrl,
//     this.boxFit,
//     this.width,
//     this.hight,
//     this.controller,
//   });
//   final String? imageUrl;
//   final BoxFit? boxFit;
//   final double? width;
//   final double? hight;
//   final Animation<double>? controller;

//   @override
//   Widget build(BuildContext context) {
//     return LottieBuilder.network(
//       headers: {
//         "X-channel": serviceLocator<AppSettings>().xChannel,
//         "Accept-Language":
//             serviceLocator<AppSettings>().selectedLanguage.backendLangCode,
//         "lang": "en",
//         "Authorization":
//             "Bearer ${serviceLocator<DatabaseManager>().getSecureData(SecureStorageKey.token)}",
//       },
//       controller: controller,
//       imageUrl ?? "",
//       width: width,
//       height: hight,
//       fit: boxFit ?? BoxFit.fill,
//       errorBuilder: (context, error, stackTrace) => const Icon(
//         Icons.error,
//         color: Colors.red,
//       ),
//     );
//   }
// }
