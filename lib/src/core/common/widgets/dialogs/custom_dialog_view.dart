import 'package:flutter/material.dart';

import '../../../utils/helpers/size.dart';

class CustomDialogView extends StatelessWidget {
  final String? alertStatus;
  final Widget? title;
  final AlignmentGeometry? alignment;
  final List<Widget>? actions;
  final double? elevation;
  final Widget? content;
  final EdgeInsets? insetPadding;
  final EdgeInsets? titlePadding;
  final EdgeInsetsGeometry? actionsPadding;
  final MainAxisAlignment? actionsAlignment;
  final Widget? icon;
  final bool centerTitle;
  final bool centerContent;
  final bool slideTransition;
  final bool barrierDismissible;
  final bool canPop;
  final Function(bool, dynamic)? onPopInvokedWithResult;
  const CustomDialogView({
    super.key,
    this.content,
    this.title,
    this.alertStatus,
    this.actions,
    this.alignment,
    this.elevation,
    this.insetPadding,
    this.titlePadding,
    this.actionsPadding,
    this.actionsAlignment,
    this.icon,
    this.centerTitle = true,
    this.centerContent = true,
    this.slideTransition = true,
    this.barrierDismissible = true,
    this.onPopInvokedWithResult,
    this.canPop = true,
  });
  static Future<T?> show<T>(
    BuildContext context, {
    Widget? content,
    Widget? title,
    String? alertStatus,
    List<Widget>? actions,
    AlignmentGeometry? alignment,
    double? elevation,
    EdgeInsets? insetPadding,
    EdgeInsets? titlePadding,
    EdgeInsetsGeometry? actionsPadding,
    MainAxisAlignment? actionsAlignment,
    Widget? icon,
    bool centerTitle = true,
    bool slideTransition = true,
    bool barrierDismissible = true,
    bool canPop = true,
    Function(bool, dynamic)? onPopInvokedWithResult,
  }) async {
    return showGeneralDialog<T>(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: barrierDismissible,
      transitionBuilder: slideTransition == false
          ? null
          : (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1, 0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            },
      pageBuilder: (context, animation, secondaryAnimation) {
        return CustomDialogView(
          content: content,
          title: title,
          alertStatus: alertStatus,
          actions: actions,
          alignment: alignment,
          elevation: elevation,
          actionsPadding: actionsPadding,
          insetPadding: insetPadding,
          titlePadding: titlePadding,
          actionsAlignment: actionsAlignment,
          icon: icon,
          centerTitle: centerTitle,
          slideTransition: slideTransition,
          barrierDismissible: barrierDismissible,
          onPopInvokedWithResult: onPopInvokedWithResult,
          canPop: canPop,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: canPop,
      onPopInvokedWithResult: onPopInvokedWithResult,
      child: AlertDialog(
        actionsPadding: actionsPadding,
        titlePadding: titlePadding ?? const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(16),
        insetPadding: insetPadding ??
            EdgeInsets.symmetric(
              vertical: 16,
              horizontal: sidePadding.horizontal / 2,
            ),
        actionsAlignment: MainAxisAlignment.center,
        content: content ?? const SizedBox(),
        elevation: elevation,
        alignment: alignment,
        title: centerTitle
            ? Center(
                child: title ?? const SizedBox(),
              )
            : title ?? const SizedBox(),
        actions: actions,
      ),
    );
  }
}
