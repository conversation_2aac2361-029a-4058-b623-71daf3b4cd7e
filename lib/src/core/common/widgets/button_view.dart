import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../app/theme/app_theme.dart';
import '../../utils/managers/firebase_services_engine/services_engine.dart';
import '../../../injection.dart';

import 'text_widgets/text_view.dart';

enum ButtonType {
  solidButton,
  soldTonalButton,
  outlinedButton,
  textButton,
  iconButton,
  filledButton,
}

class ButtonView extends StatelessWidget {
  final String? title;
  final IconData? icon;
  final Widget? trailing;
  final Widget? imageIcon;
  final ButtonType buttonType;
  final VoidCallback onClick;
  final ButtonStyle? buttonStyle;
  final String semanticLabelValue;
  final Color? iconColor;
  final double? iconSize;
  final Color? buttonTextColor;
  final bool? isDisabled;
  final TextStyle? textStyle;
  final Color? buttonColor;
  final Color? trailingBackgroundColor;
  final Color? iconBackgroundColor;
  final bool? isExpanded;
  final bool? haveBorder;
  final double? borderRadius;

  const ButtonView({
    this.title,
    this.icon,
    this.trailing,
    this.buttonStyle,
    required this.semanticLabelValue,
    required this.buttonType,
    required this.onClick,
    this.iconColor,
    this.iconSize,
    this.buttonColor,
    this.isDisabled,
    this.buttonTextColor,
    this.textStyle,
    this.trailingBackgroundColor,
    this.iconBackgroundColor,
    super.key,
    this.isExpanded,
    this.haveBorder,
    this.imageIcon,
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    switch (buttonType) {
      case ButtonType.solidButton:
        return solidButton(context);
      case ButtonType.soldTonalButton:
        return soldTonalButton(context);
      case ButtonType.outlinedButton:
        return outlineButton(context);
      case ButtonType.textButton:
        return textButton(context);
      case ButtonType.iconButton:
        return iconButton(context);
      case ButtonType.filledButton:
        return filledButton(context);
    }
  }

  Widget solidButton(BuildContext context) {
    return SizedBox(
      width: isExpanded ?? true ? double.maxFinite : null,
      child: ElevatedButton(
        style:
            buttonStyle ??
            ElevatedButton.styleFrom(
              textStyle: textStyle,
              side: haveBorder ?? false
                  ? BorderSide(
                      color: Colors.grey.withValues(alpha: 0.3),
                    )
                  : BorderSide.none,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              backgroundColor: buttonColor,
              disabledBackgroundColor: Theme.of(context).hintColor,
            ).copyWith(
              elevation: ButtonStyleButton.allOrNull(0.0),
            ),
        onPressed: isDisabled ?? false
            ? null
            : () {
                // serviceLocator<ServicesEngine>().logEventFbAnalytics("ElevatedButton", {
                //   'onClick': semanticLabelValue,
                // });
                onClick();
              },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            if (icon != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                child: Icon(
                  icon,
                  color: iconColor,
                ),
              ),
            if (imageIcon != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                child: imageIcon,
              ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
              child: TextView(
                text: title ?? '',
                style:
                    textStyle ??
                    Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: buttonTextColor ?? Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w700,
                      fontSize: kFontSizeM,
                      fontFamily: context.locale.languageCode == 'en' ? 'Titillium' : 'Rabar015',
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget filledButton(BuildContext context) {
    return FilledButton(
      style:
          buttonStyle ??
          FilledButton.styleFrom(
            textStyle: textStyle,
            backgroundColor: buttonColor ?? Theme.of(context).primaryColor,
          ),
      onPressed: isDisabled ?? false ? null : () => onClick(),
      child: Row(
        children: [
          if (icon != null)
            Container(
              height: 30,
              width: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconBackgroundColor ?? Theme.of(context).colorScheme.secondaryContainer,
              ),
              child: Icon(
                icon,
                color: iconColor ?? Theme.of(context).colorScheme.onSurface,
              ),
            ),
          Expanded(
            child: TextView(
              text: title ?? '',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: buttonTextColor ?? Colors.white,
              ),
              textAlignment: icon == null ? TextAlign.start : TextAlign.center,
            ),
          ),
          trailing ??
              Container(
                height: 30,
                width: 30,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: trailingBackgroundColor ?? Theme.of(context).colorScheme.secondary,
                ),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: iconColor ?? Theme.of(context).colorScheme.onSurface,
                ),
              ),
        ],
      ),
    );
  }

  // not used
  Widget soldTonalButton(BuildContext context) {
    return ElevatedButton(
      style:
          buttonStyle ??
          ElevatedButton.styleFrom(
            textStyle: textStyle,
            foregroundColor: Theme.of(context).colorScheme.onSecondaryContainer,
            backgroundColor: Theme.of(context).primaryColor,
            shape: const StadiumBorder(),
          ).copyWith(elevation: ButtonStyleButton.allOrNull(0.0)),
      onPressed: isDisabled ?? false
          ? null
          : () {
              serviceLocator<ServicesEngine>().logEventFbAnalytics("ElevatedTonalButton", {
                'onClick': semanticLabelValue,
              });
              onClick();
            },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        child: TextView(
          text: title ?? '',
          style: Theme.of(context).textTheme.displayMedium?.copyWith(color: buttonTextColor),
        ),
      ),
    );
  }

  Widget outlineButton(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return OutlinedButton(
      onPressed: isDisabled ?? false
          ? null
          : () {
              serviceLocator<ServicesEngine>().logEventFbAnalytics("OutlineButton", {
                'onClick': semanticLabelValue,
              });
              onClick();
            },
      style:
          buttonStyle ??
          OutlinedButton.styleFrom(
            side: BorderSide(color: colorScheme.outline),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 8),
            ),
          ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: isDisabled ?? false
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: buttonTextColor ?? colorScheme.onSurface,
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        icon,
                        color: iconColor ?? colorScheme.onSurface,
                      ),
                    ),
                  if (imageIcon != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: imageIcon,
                    ),
                  TextView(
                    text: title ?? 'Cancel'.tr(),
                    style:
                        textStyle ??
                        textTheme.labelLarge?.copyWith(
                          color: buttonTextColor ?? colorScheme.onSurface,
                        ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget textButton(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: isDisabled ?? false
          ? null
          : () {
              serviceLocator<ServicesEngine>().logEventFbAnalytics("TextButton", {'onClick': semanticLabelValue});
              onClick();
            },
      child: TextView(
        style:
            textStyle ??
            TextStyle(
              color: buttonTextColor,
              fontFamily: context.locale.languageCode == 'en' ? 'Titillium' : 'Rabar015',
            ),
        text: title ?? '',
      ),
    );
  }

  Widget iconButton(BuildContext context) {
    return IconButton(
      onPressed: isDisabled ?? false
          ? null
          : () {
              serviceLocator<ServicesEngine>().logEventFbAnalytics("IconButton", {
                'onClick': semanticLabelValue,
              });
              onClick();
            },
      icon: Icon(
        icon ?? Icons.add_box,
        color: iconColor,
        size: iconSize,
      ),
    );
  }
}
