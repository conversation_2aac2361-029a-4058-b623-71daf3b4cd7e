import 'package:flutter/material.dart';

import '../text_widgets/text_view.dart';

class SnackbarView {
  static void show(BuildContext context,
      {required String message,
      Color backgroundColor = const Color(0xff22C55E),
      Duration duration = const Duration(seconds: 3),
      String? actionLabel,
      VoidCallback? onAction}) {
    ///
    ///
    ///
    ///
    final snackBar = SnackBar(
      content: TextView(
        text: message,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: backgroundColor,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      duration: duration,
      action: actionLabel != null
          ? SnackBarAction(
              label: actionLabel,
              onPressed: onAction ?? () {},
              textColor: Colors.white,
            )
          : null,
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
