import 'package:flutter/material.dart';

class LinearProgressWidget extends StatelessWidget {
  const LinearProgressWidget({super.key, this.value});

  final double? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
        ),
      ),
      child: LinearProgressIndicator(
        color: Theme.of(context).colorScheme.primary,
        value:value,
        minHeight: 15,
        borderRadius: BorderRadius.circular(10),
        semanticsLabel: "Lessons progress",
        backgroundColor: Theme.of(context).colorScheme.surfaceBright,
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
  }
}
