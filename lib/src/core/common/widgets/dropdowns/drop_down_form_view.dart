import 'package:flutter/material.dart';

import '../../../../app/theme/colors.dart';
import '../text_widgets/header_subheader_widget.dart';
import '../text_widgets/text_view.dart';

class DropDownFormView<T> extends StatelessWidget {
  final String? errorMessage;
  final String hintText;
  final String? headerText;
  final String? subHeaderText;
  final List<T?>? options;
  final bool isDense;
  final T? value;
  final void Function(T?)? onChanged;
  final void Function(T?)? onSaved;
  final String Function(T?) getLabel;

  const DropDownFormView({
    required this.options,
    required this.getLabel,
    this.headerText,
    this.subHeaderText,
    required this.onChanged,
    required this.onSaved,
    this.errorMessage,
    this.value,
    this.isDense = false,
    required this.hintText,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        HeaderAndSubHeaderTextWidget(
          headerText: headerText,
          subHeaderText: subHeaderText,
        ),
        DropdownButtonFormField<T>(
          decoration: InputDecoration(
            errorMaxLines: 1,
            filled: false,
            fillColor: Theme.of(context).colorScheme.surfaceBright,
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: Color(0xffCCCCCC),
              ),
            ),
            border: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: Color(0xffCCCCCC),
              ),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: Color(0xffCCCCCC),
              ),
            ),
            focusedErrorBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: ColorPalette.red,
              ),
            ),
            errorBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: ColorPalette.progressRed,
              ),
            ),
            errorStyle: Theme.of(context).inputDecorationTheme.errorStyle,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          hint: TextView(
            overflow: TextOverflow.visible,
            text: hintText,
            maxLines: 2,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
          ),
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: Theme.of(context).hintColor,
          ),
          focusColor: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          isExpanded: true,
          value: value,
          onSaved: onSaved,
          menuMaxHeight: MediaQuery.sizeOf(context).height / 4,
          validator: (value) => value == null ? errorMessage : null,
          selectedItemBuilder: (context) {
            return options?.map<Widget>(
                  (T? value) {
                    return TextView(
                      overflow: TextOverflow.fade,
                      text: getLabel(value),
                      maxLines: 1,
                      style: Theme.of(context).textTheme.labelMedium,
                    );
                  },
                ).toList() ??
                [];
          },
          items: options?.map<DropdownMenuItem<T>>((T? value) {
            return DropdownMenuItem<T>(
              value: value,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 12.0, vertical: 8.0), // Add padding here
                child: TextView(
                  overflow: TextOverflow.visible,
                  text: getLabel(value),
                  maxLines: 2,
                  style: Theme.of(context).textTheme.labelMedium,
                ),
              ),
            );
          }).toList(),
          onChanged: (newValue) {
            onChanged!(newValue);
          },
        ),
      ],
    );
  }
}
