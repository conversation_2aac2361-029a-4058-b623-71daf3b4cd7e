// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';

import '../text_widgets/text_view.dart';

class LanguageDropDownButtonView<T> extends StatefulWidget {
  final List<T?>? options;
  T? value;
  final void Function(T?)? onChanged;
  final String Function(T?) getLabel;
  final String? semanticLabelValue;
  final String? shortDisplayLabel;
  final Color? textColor;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;

  LanguageDropDownButtonView({
    required this.options,
    required this.getLabel,
    required this.shortDisplayLabel,
    required this.onChanged,
    this.semanticLabelValue,
    this.selectedItemBuilder,
    this.value,
    this.textColor,
    super.key,
  });

  @override
  _LanguageDropDownButtonViewState<T> createState() =>
      _LanguageDropDownButtonViewState<T>();
}

class _LanguageDropDownButtonViewState<T>
    extends State<LanguageDropDownButtonView<T>> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  void _toggleDropdown() {
    if (_overlayEntry == null) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    } else {
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;
    Offset offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          GestureDetector(
            onTap: () {
              if (_overlayEntry != null) {
                _overlayEntry?.remove();
                _overlayEntry = null;
              }
            },
            child: Container(
              color: Colors.transparent,
            ),
          ),
          Positioned(
            width: size.width,
            left: offset.dx,
            top: offset.dy + size.height,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, size.height),
              child: Material(
                elevation: 10,
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: widget.options?.length ?? 0,
                  itemBuilder: (context, index) {
                    final value = widget.options?[index];
                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 5.0, vertical: 0.0),
                      title: Center(
                        child: TextView(
                          text: widget.getLabel(value),
                          style: TextStyle(
                            color: widget.textColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          widget.value = value;
                        });
                        widget.onChanged?.call(value);
                        _toggleDropdown();
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const Divider(
                      color: Colors.grey,
                      height: 1,
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_overlayEntry != null) {
          _overlayEntry?.remove();
          _overlayEntry = null;
        }
      },
      child: CompositedTransformTarget(
        link: _layerLink,
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
            child: Row(
              children: [
                TextView(
                  text: widget.shortDisplayLabel ?? "",
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: Theme.of(context).colorScheme.surface,
                        fontSize: 16,
                      ),
                ),
                Icon(
                  Icons.keyboard_arrow_down_outlined,
                  color: Theme.of(context).colorScheme.surface,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
