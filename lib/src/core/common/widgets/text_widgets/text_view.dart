import 'package:easy_localization/easy_localization.dart' as tr;
import 'package:flutter/material.dart';

class TextView extends StatelessWidget {
  final String text;
  final int? maxLines;
  final TextStyle? style;
  final TextAlign? textAlignment;
  final TextOverflow? overflow;
  final TextDirection? textDirection;

  const TextView({
    super.key,
    required this.text,
    this.maxLines,
    this.style,
    this.overflow,
    this.textAlignment,
    this.textDirection,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text.tr(),
      maxLines: maxLines,
      overflow: overflow,
      style: style,
      textAlign: textAlignment,
      textDirection: textDirection,
    );
  }
}
