import 'package:flutter/material.dart';

import '../app_space_widget.dart';
import 'text_view.dart';

class HeaderAndSubHeaderTextWidget extends StatelessWidget {
  const HeaderAndSubHeaderTextWidget({
    super.key,
    this.headerText,
    this.subHeaderText,
  });

  final String? headerText;
  final String? subHeaderText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (headerText != null) ...[
          TextView(
            text: headerText ?? '',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
          ),
          AppSpacer.p8(),
        ],
        if (subHeaderText != null) ...[
          TextView(
            text: subHeaderText ?? '',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          AppSpacer.p8(),
        ],
      ],
    );
  }
}
