import 'dart:ui' as ui;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../constants/const.dart';
import '../../../utils/helpers/input_validation_mixin.dart';
import 'header_subheader_widget.dart';

enum TextFormFieldTypes {
  email,
  simpleEmail,
  password,
  phone,
  requiredText,
  text,
  nonRequiredPhone,
  adultDateOfBirth
}

// NOTE: should be have a different error message and specified inside this widget to have better UX.
class TextFormFieldView extends StatelessWidget with InputValidationMixin {
  final String? title;
  final String? subtitle;
  final TextFormFieldTypes textFormFieldTypes;
  final String? errorMessage;
  final EdgeInsets? scrollPadding;
  final int? maxLines;
  final int? maxLength;
  final int? minLength;
  final bool? autofocus;
  final bool? obscureText;
  final String? initialValue;
  final TextInputType? keyboardType;
  final TextStyle? textStyle;
  final void Function(String?)? onSaved;
  final TextEditingController? controller;
  final double? fontSize;
  final Widget? counterWidget;
  final String? hintText;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final Widget? prefix;
  final TextStyle? prefixStyle;
  final bool? needLTR;
  final TextStyle? hintStyle;
  final bool? isLtr;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String value)? onChanged;
  final Widget? Function(BuildContext,
      {required int currentLength,
      required bool isFocused,
      required int? maxLength})? buildCounter;

  TextFormFieldView({
    this.suffixIcon,
    required this.textFormFieldTypes,
    this.errorMessage,
    this.scrollPadding = const EdgeInsets.only(bottom: 200),
    this.maxLines,
    this.prefix,
    this.maxLength,
    this.minLength,
    this.prefixStyle,
    this.prefixIcon,
    this.initialValue,
    this.keyboardType = TextInputType.text,
    this.textStyle,
    this.autofocus = false,
    this.obscureText = false,
    this.onSaved,
    this.controller,
    this.fontSize,
    this.counterWidget,
    this.hintText,
    this.needLTR,
    this.hintStyle,
    this.buildCounter,
    this.isLtr,
    super.key,
    this.title,
    this.subtitle,
    this.inputFormatters,
    this.onChanged,
  });

  double getCalculatedBorderRadius() {
    return (maxLines == null || maxLines! <= 2) ? 48.0 : 12.0;
  }

  @override
  Widget build(BuildContext context) {
    if (title != null || subtitle != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeaderAndSubHeaderTextWidget(
            headerText: title,
            subHeaderText: subtitle,
          ),
          _textFormField(context),
        ],
      );
    } else {
      return _textFormField(context);
    }
  }

  TextFormField _textFormField(BuildContext context) {
    return TextFormField(
      textDirection: (isLtr ?? false) ? ui.TextDirection.ltr : null,
      buildCounter: buildCounter,

      /// This widget handles the keyboard dismissal when tapping outside of the text form field.
      ///
      /// It is useful in scenarios where you want to improve the user experience by automatically
      /// dismissing the keyboard when the user taps outside the input field.
      //  add this to handle keyboard dismiss when tap outside
      onTapOutside: (event) => FocusScope.of(context).unfocus(),
      onSaved: onSaved,
      onChanged: onChanged,
      autofocus: autofocus ?? false,
      keyboardType: keyboardType,

      /// If the field is a password field, `maxLines` should be set to 1,
      /// ensuring that the password field is a single line input.
      /// if password field, maxLines should be 1, because password field should be single line
      maxLines:
          textFormFieldTypes == TextFormFieldTypes.password ? 1 : maxLines,
      maxLength: maxLength,
      obscuringCharacter: "●",
      obscureText: obscureText!,
      scrollPadding: scrollPadding!,
      initialValue: initialValue,
      controller: controller,
      inputFormatters: textFormFieldTypes == TextFormFieldTypes.password ||
              textFormFieldTypes == TextFormFieldTypes.requiredText
          ? [
              FilteringTextInputFormatter.allow(
                RegExp(r'[\p{L}\p{N}!@#\$&%%%%*~+ ]', unicode: true),
              ),
            ]
          : inputFormatters,
      decoration: InputDecoration(
        // contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        contentPadding:
            const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
        counterStyle: Theme.of(context).textTheme.titleSmall,
        hintTextDirection: needLTR ?? false
            ? ui.TextDirection.ltr
            : ((context.locale.countryCode == kUS)
                ? ui.TextDirection.ltr
                : ui.TextDirection.rtl),
        prefix: prefix,
        prefixStyle: prefixStyle,
        hintStyle: hintStyle ??
            Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
        suffixIcon: suffixIcon,
        hintText: hintText?.tr() ?? "default_hint_text_for_form_fields".tr(),
        prefixIcon: prefixIcon != null
            ? Padding(
                padding: const EdgeInsets.all(12.0),
                child: prefixIcon,
              )
            : null,
        counter: counterWidget,
        errorMaxLines: 1,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            // color: Theme.of(context).dividerColor,
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(getCalculatedBorderRadius()),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.outlineVariant,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Theme.of(context).primaryColor,
            width: 1.5,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.error,
            width: 1.5,
          ),
        ),
      ),
      style: Theme.of(context).textTheme.labelMedium,
      validator: (value) {
        switch (textFormFieldTypes) {
          case TextFormFieldTypes.email:
            if (value == null || value.trim().isEmpty) {
              return "validation_this_field_is_required".tr();
            }
            if (!isEmailValid(value)) {
              return "validation_invalid_email".tr();
            }
            return null;

          case TextFormFieldTypes.simpleEmail:
            if (value?.trim().isEmpty ?? true) {
              return "validation_this_field_is_required".tr();
            } else {
              return isSimpleEmailValid(value?.trim() ?? "")
                  ? null
                  : errorMessage?.tr();
            }

          case TextFormFieldTypes.password:
            if (value?.trim().isEmpty ?? true) {
              return "validation_this_field_is_required".tr();
            } else {
              return isPasswordValid(value!) ?? errorMessage;
            }

          case TextFormFieldTypes.nonRequiredPhone:
            if (value?.trim() == null || (value?.trim().isEmpty ?? true)) {
              return null;
            } else {
              return isPhoneValid(value?.trim() ?? "")
                  ? null
                  : errorMessage?.tr();
            }

          case TextFormFieldTypes.phone:
            if ((value?.trim().isEmpty ?? true) || value?.trim() == null) {
              return "Please_enter_your_phone_number".tr();
            } else {
              return isPhoneValid(value?.trim() ?? "")
                  ? null
                  : errorMessage
                      ?.tr(); // if validated return null, (not) return error message
            }

          case TextFormFieldTypes.requiredText:
            if ((value?.trim().isEmpty ?? true) || value?.trim() == null) {
              return "validation_this_field_is_required".tr();
            }

          case TextFormFieldTypes.text:
            if (minLength == null) {
              return null;
            } else {
              return ((value?.trim() ?? "").length > (minLength ?? 1))
                  ? null
                  : errorMessage?.tr();
            }

          default:
            return null;
        }
        return null;
      },
    );
  }
}
