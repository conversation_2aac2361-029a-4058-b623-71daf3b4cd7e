import 'package:flutter/material.dart';

import 'app_space_widget.dart';
import 'text_widgets/text_view.dart';

class BottomSheetView extends StatefulWidget {
  final Widget content;
  final String? title;
  final ScrollController? scrollController;
  final Color backgroundColor;
  final Color? barrierColor;

  const BottomSheetView({
    super.key,
    required this.content,
    this.title,
    this.scrollController,
    required this.backgroundColor,
    this.barrierColor,
  });

  static Future show(
    BuildContext context, {
    required Widget content,
    String? title,
    bool isScrollControlled = true,
    double initialChildSize = 0.5,
    double minChildSize = 0.25,
    double maxChildSize = 1.0,
    bool useDraggableScrollableSheet = false,
    bool isCloseButtonVisible = false,
    Color backgroundColor = Colors.white,
    Color? barrierColor,
  }) async {
    if (useDraggableScrollableSheet) {
      return showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        enableDrag: !isCloseButtonVisible,
        isDismissible: true,
        isScrollControlled: isScrollControlled,
        barrierColor: barrierColor,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: initialChildSize,
          maxChildSize: maxChildSize,
          minChildSize: minChildSize,
          expand: false,
          builder: (BuildContext context, ScrollController scrollController) {
            return BottomSheetView(
              content: content,
              title: title,
              scrollController: scrollController,
              backgroundColor: backgroundColor,
              barrierColor: barrierColor,
            );
          },
        ),
      );
    }

    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: !isCloseButtonVisible,
      isDismissible: true,
      isScrollControlled: isScrollControlled,
      barrierColor: barrierColor,
      builder: (context) => BottomSheetView(
        content: content,
        title: title,
        backgroundColor: backgroundColor,
        barrierColor: barrierColor,
      ),
    );
  }

  @override
  State<BottomSheetView> createState() => _BottomSheetViewState();
}

class _BottomSheetViewState extends State<BottomSheetView> {
  ValueNotifier<Size> footerSizeNotifier = ValueNotifier(Size.zero);

  @override
  void initState() {
    super.initState();
    widget.scrollController?.addListener(() {
      if (widget.scrollController!.position.pixels ==
          widget.scrollController!.position.maxScrollExtent) {}
    });
  }

  @override
  Widget build(BuildContext context) {
    final content = Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(18),
          topRight: Radius.circular(18),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppSpacer.p16(),
          if (widget.title != null) ...[
            TextView(
              text: widget.title ?? '',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(),
            ),
            AppSpacer.p16(),
          ],
          Flexible(
            child: SingleChildScrollView(
              controller: widget.scrollController,
              reverse: widget.scrollController == null,
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              // physics: const BouncingScrollPhysics(),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  widget.content,
                  // if (widget.footerWidget != null) widget.footerWidget!,
                  // if (MediaQuery.of(context).padding.bottom <= 0) AppSpacer.p16(),
                  ValueListenableBuilder(
                      valueListenable: footerSizeNotifier,
                      builder: (context, Size size, child) {
                        return SizedBox(
                          height: size != Size.zero
                              ? size.height -
                                  MediaQuery.of(context).padding.bottom
                              : 0,
                        );
                      }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
    return PopScope(
      child: widget.scrollController == null
          ? content
          : IntrinsicHeight(child: content),
      onPopInvokedWithResult: (context, result) async {},
    );
  }
}
