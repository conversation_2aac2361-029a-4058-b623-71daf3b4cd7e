import 'package:carousel_slider/carousel_slider.dart' show CarouselSlider, CarouselOptions;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show HapticFeedback;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/news_card/news_card_view.dart';
import 'package:kurdsat/src/features/common/main/presentation/widgets/shimmer_view.dart' show ShimmerView;

class ArticleCardCarosel extends StatefulWidget {
  final List<Widget> items;
  final double carouselHeight;

  const ArticleCardCarosel({
    super.key,
    required this.items,
    required this.carouselHeight,
  });

  @override
  State<ArticleCardCarosel> createState() => _ArticleCardCaroselState();
}

class _ArticleCardCaroselState extends State<ArticleCardCarosel> {
  int _currentIndex = 0;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            height: widget.carouselHeight,
            viewportFraction: 0.9,
            // viewportFraction: 1,
            pageSnapping: true,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) {
              HapticFeedback.mediumImpact();
              setState(() {
                _currentIndex = index;
              });
            },
          ),
          items: widget.items,
        ),
        16.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.items.length,
            (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.fastOutSlowIn,

              width: 8.r,
              height: 8.r,
              margin: EdgeInsets.symmetric(horizontal: 4.r),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex ? Color(0xff5194E1) : Color(0xff28323E),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ArticleCardCaroselShimmer extends StatelessWidget {
  const ArticleCardCaroselShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerView(
      child: Column(
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: 300.r,
              viewportFraction: 0.92,
              pageSnapping: true,
              enableInfiniteScroll: false,
              onPageChanged: (index, reason) {
                HapticFeedback.mediumImpact();
              },
            ),

            items: [
              for (int i = 0; i < 3; i++) ...[NewsCardShimmer()],
            ],
          ),
          16.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.fastOutSlowIn,

                width: 8.r,
                height: 8.r,
                margin: EdgeInsets.symmetric(horizontal: 4.r),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xff28323E),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/*
// archived contect menu functionality

CarouselSlider(
  options: CarouselOptions(
    height: 300.r,
    viewportFraction: 0.92,
    pageSnapping: true,
    enableInfiniteScroll: false,
    onPageChanged: (index, reason) {
      HapticFeedback.mediumImpact();
      setState(() {
        _currentIndex = index;
      });
    },
  ),
  items: data.map(
    (LatestNewsDataModel article) {
      return CustomContextMenu(
        previewBuilder: (context, animation, child) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: NewsCardViewPreview(
                  imageUrl: "$kBaseAssetUrl${article.imageUrl}",
                  dutation: 50,
                  title: article.title ?? "",
                ),
              ),
            ],
          );
        },

        isSelected: true,
        onPress: () {
          context.pop();
        },
        actions: [
          Text(
            "data",
            style: TextStyle(
              color: Colors.red,
              fontSize: 16.sp,
            ),
          ),
        ],

        child: NewsCardView(
          imageUrl: "$kBaseAssetUrl${article.imageUrl}",
          dutation: article.date,
          title: article.title ?? "",
          articleId: article.id,
        ),
      );
    },
  ).toList(),
),


*/
