import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';

class CategoryTitleSection extends StatelessWidget {
  final String title;
  final Function()? onPressed;

  const CategoryTitleSection({
    super.key,
    required this.title,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CupertinoButton(
          // TODO: remove on hardcoded function
          onPressed: onPressed,
          padding: EdgeInsets.zero,
          minimumSize: Size(0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextView(
                text: title,
                style: TextStyle(
                  color: Color(0xffEFF2F5).withValues(alpha: 0.7),
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Icon(
                CupertinoIcons.arrow_up_right,
                size: 24.r,
                color: Color(0xff3693FF).withValues(alpha: 0.7),
              ),
            ],
          ),
        ),
        12.verticalSpace,
        Divider(color: const Color(0xff28323E), height: 1, thickness: 1),
      ],
    );
  }
}
