import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class LoadingView extends StatelessWidget {
  final double loadingSize;
  final Color? loadingColor;
  final bool? dontNeedWidth;
  const LoadingView({
    this.loadingSize = 30,
    this.loadingColor,
    this.dontNeedWidth,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: (dontNeedWidth ?? false) ? 0 : double.infinity,
      child: LoadingAnimationWidget.fourRotatingDots(
        color: Colors.white,
        size: loadingSize,
      ),
    );
  }

  Color getColor(BuildContext context) {
    if (loadingColor != null) return loadingColor!;

    return Colors.black;
  }
}
