import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show ConsumerWidget, WidgetRef;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/features/common/settings/presentation/logic/text_size_provider.dart' show textSizeProvider;

class TextSizeSlider extends ConsumerWidget {
  const TextSizeSlider({super.key});

  Widget _buildAnimatedLabel(String text, int index, double currentTextSize) {
    return AnimatedDefaultTextStyle(
      key: ValueKey('text_size_label_$index'), // Unique key for better performance
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      style: TextStyle(
        color: Colors.white,
        fontSize: currentTextSize == index ? 16.sp : 14.sp,
        fontWeight: currentTextSize == index ? FontWeight.bold : FontWeight.normal,
      ),
      child: Text(text),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textSize = ref.watch(textSizeProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "A",
                style: TextStyle(
                  color: Colors.white,
                ),
              ),
              4.horizontalSpace,
              Expanded(
                child: Slider(
                  value: textSize,
                  onChanged: (value) {
                    ref.read(textSizeProvider.notifier).setTextSize(value);
                    if (value == 0) {
                      HapticFeedback.lightImpact();
                    } else if (value == 1) {
                      HapticFeedback.mediumImpact();
                    } else if (value == 2) {
                      HapticFeedback.heavyImpact();
                    }
                  },
                  min: 0,
                  max: 2,
                  divisions: 2,
                  label: null,
                  thumbColor: Colors.white,
                  activeColor: Colors.white,
                  inactiveColor: Colors.white,
                  overlayColor: WidgetStateProperty.all<Color>(Colors.white10),
                ),
              ),
              Text(
                "A",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24.sp,
                ),
              ),
            ],
          ),
          // Labels row
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              children: [
                Opacity(opacity: 0, child: Text("A")),
                _buildAnimatedLabel("Normal", 0, textSize),
                Spacer(),
                _buildAnimatedLabel("Medium", 1, textSize),
                Spacer(),
                _buildAnimatedLabel("Large", 2, textSize),
                Opacity(opacity: 0, child: Text("A")),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
