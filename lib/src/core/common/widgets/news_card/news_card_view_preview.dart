import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat/src/core/utils/helpers/size.dart';

class NewsCardViewPreview extends StatelessWidget {
  final String imageUrl;
  final String title;
  final int dutation;

  const NewsCardViewPreview({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.dutation,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Color(0xff14191f),
            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Image.network(
            width: double.infinity,
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return AspectRatio(
                aspectRatio: 1.6,
                child: Container(
                  width: screenWidth(context),
                  color: Colors.grey[700],
                  child: const Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 40,
                  ),
                ),
              );
            },
          ),
        ),
        12.verticalSpace,

        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12).r,
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Color(0xff14191f),
            shape: ContinuousRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: Color(0xff1F2A3A),
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,

            children: [
              TextView(
                text: title,
                style: TextStyle(
                  color: Color(0xffEFF2F5),
                  fontSize: 16.r,
                ),
              ),
              10.verticalSpace,
              TextView(
                text:
                    "kurdsatnews"
                    " - "
                    "$dutation minuties",
                style: TextStyle(
                  color: Color(0xff576C87),
                  fontSize: 12.r,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}


/*
  child: AspectRatio(
    aspectRatio: 1.5,
    child: Image.network(
      width: double.infinity,
      imageUrl,
      fit: BoxFit.cover,
    ),
*/ 