import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat/src/core/extensions/date_formatter.dart';

class NewsCardView extends StatelessWidget {
  final String imageUrl;
  final String title;
  final DateTime? dutation;
  final VoidCallback? onTap;
  final String? author;
  final int? articleId;

  const NewsCardView({
    super.key,
    required this.imageUrl,
    required this.title,
    this.author,
    this.dutation,
    this.onTap,
    this.articleId,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      minimumSize: Size(0, 0),
      onPressed: () {
        log(articleId.toString());
        if (articleId != null) {
          context.push('/article/$articleId');
        } else if (onTap != null) {
          onTap!();
        }
      },
      child: Container(
        margin: EdgeInsets.only(right: 12).r,
        // clipBehavior: Clip.hardEdge,
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: Color(0xff14191f),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Image.network(
                width: double.infinity,
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    color: Colors.grey[700],
                    child: const Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
            12.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12).r,
              child: DefaultTextStyle(
                style: TextStyle(
                  color: Color(0xffEFF2F5),
                  fontSize: 16.r,
                ),
                child: TextView(
                  text: title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),

            if (dutation != null) ...[
              10.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12).r,
                child: DefaultTextStyle(
                  style: TextStyle(
                    color: Color(0xff576C87),
                    fontSize: 12.r,
                  ),
                  child: TextView(
                    text:
                        author ??
                        "KurdsatNews"
                            " - "
                            "${dutation!.getSimpleRelativeTime()}",
                  ),
                ),
              ),
            ],
            12.verticalSpace,
          ],
        ),
      ),
    );
  }
}

class NewsCardShimmer extends StatelessWidget {
  const NewsCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 12).r,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Color(0xff14191f),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.grey[700],
            ),
          ),
          12.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12).r,
            child: DefaultTextStyle(
              style: TextStyle(
                color: Color(0xffEFF2F5),
                fontSize: 16.r,
              ),
              child: TextView(
                text: "Title",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          10.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12).r,
            child: DefaultTextStyle(
              style: TextStyle(
                color: Color(0xff576C87),
                fontSize: 12.r,
              ),
              child: TextView(
                text: "kurdsatnews - 10 minuties",
              ),
            ),
          ),
          12.verticalSpace,
        ],
      ),
    );
  }
}
