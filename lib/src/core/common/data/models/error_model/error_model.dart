import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'error_model.g.dart';

@JsonSerializable()
class ErrorModel extends Equatable {
  const ErrorModel({
    this.error,
  });

  final Error? error;

  factory ErrorModel.fromJson(Map<String, dynamic> json) =>
      _$ErrorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorModelToJson(this);

  factory ErrorModel.fromException(dynamic exception) {
    return ErrorModel(
      error: Error(
        message: exception.toString(),
        originalError: exception.toString(),
      ),
    );
  }

  /// Creates a copy of this ErrorModel with the given fields replaced with the new values
  ErrorModel copyWith({
    Error? error,
  }) {
    return ErrorModel(
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        error,
      ];
}

@JsonSerializable()
class Error extends Equatable {
  final String? code;
  final String? title;
  final String? message;
  final String? path;
  @JsonKey(name: 'original_error')
  final String? originalError;

  const Error({
    this.code,
    this.title,
    this.message,
    this.path,
    this.originalError,
  });

  factory Error.fromJson(Map<String, dynamic> json) => _$ErrorFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorToJson(this);

  /// Creates a copy of this Error with the given fields replaced with the new values
  Error copyWith({
    String? code,
    String? title,
    String? message,
    String? path,
    String? originalError,
  }) {
    return Error(
      code: code ?? this.code,
      title: title ?? this.title,
      message: message ?? this.message,
      path: path ?? this.path,
      originalError: originalError ?? this.originalError,
    );
  }

  @override
  List<Object?> get props => [
        code,
        title,
        message,
        path,
        originalError,
      ];
}
