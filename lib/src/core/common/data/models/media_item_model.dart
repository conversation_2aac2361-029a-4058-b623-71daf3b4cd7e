class MediaItemResponseModel {
  final List<MediaItemDataModel> data;

  MediaItemResponseModel({required this.data});

  factory MediaItemResponseModel.fromJson(Map<String, dynamic> json) {
    return MediaItemResponseModel(
      data: (json['data'] as List<dynamic>).map((e) {
        return MediaItemDataModel.fromJson(e as Map<String, dynamic>);
      }).toList(),
    );
  }
}

class MediaItemDataModel {
  final int? id;
  final DateTime? date;
  final String? title;
  final DateTime? createdAt;
  final String? locale;
  final String? imageUrl;

  MediaItemDataModel({
    this.id,
    this.date,
    this.title,
    this.createdAt,
    this.locale,
    this.imageUrl,
  });

  factory MediaItemDataModel.fromJson(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>?;
    return MediaItemDataModel(
      id: json['id'] as int?,
      date: attributes?['date'] != null ? DateTime.tryParse(attributes!['date']) : null,
      title: attributes?['title'] as String?,
      createdAt: attributes?['createdAt'] != null ? DateTime.tryParse(attributes!['createdAt']) : null,
      locale: attributes?['locale'] as String?,
      imageUrl: attributes?['image']?['image']?['data']?['attributes']?['url'] ?? "",
    );
  }
}
