// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StrapiResponse<T> _$StrapiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => StrapiResponse<T>(
  data: StrapiItem<T>.fromJson(
    json['data'] as Map<String, dynamic>,
    (value) => fromJsonT(value),
  ),
  meta: json['meta'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$StrapiResponseToJson<T>(
  StrapiResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'data': instance.data.toJson((value) => toJsonT(value)),
  'meta': instance.meta,
};

StrapiListResponse<T> _$StrapiListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => StrapiListResponse<T>(
  data:
      (json['data'] as List<dynamic>)
          .map(
            (e) => StrapiItem<T>.fromJson(
              e as Map<String, dynamic>,
              (value) => fromJsonT(value),
            ),
          )
          .toList(),
  meta: json['meta'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$StrapiListResponseToJson<T>(
  StrapiListResponse<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'data':
      instance.data.map((e) => e.toJson((value) => toJsonT(value))).toList(),
  'meta': instance.meta,
};

StrapiItem<T> _$StrapiItemFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => StrapiItem<T>(
  id: (json['id'] as num).toInt(),
  attributes: fromJsonT(json['attributes']),
);

Map<String, dynamic> _$StrapiItemToJson<T>(
  StrapiItem<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'id': instance.id,
  'attributes': toJsonT(instance.attributes),
};
