import 'package:json_annotation/json_annotation.dart';

part 'general_model.g.dart';

/// Generic response [single resource]
@JsonSerializable(genericArgumentFactories: true)
class StrapiResponse<T> {
  final StrapiItem<T> data;
  final Map<String, dynamic>? meta;

  const StrapiResponse({
    required this.data,
    this.meta,
  });

  factory StrapiResponse.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) => _$StrapiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) => _$StrapiResponseToJson(this, toJsonT);
}

/// Generic response [list of resources]
@JsonSerializable(genericArgumentFactories: true)
class StrapiListResponse<T> {
  final List<StrapiItem<T>> data;
  final Map<String, dynamic>? meta;

  const StrapiListResponse({
    required this.data,
    this.meta,
  });

  factory StrapiListResponse.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) => _$StrapiListResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) => _$StrapiListResponseToJson(this, toJsonT);
}

/// Generic item that will be inside a [data] key [with id and attributes]
@JsonSerializable(genericArgumentFactories: true)
class StrapiItem<T> {
  final int id;
  final T attributes;

  const StrapiItem({
    required this.id,
    required this.attributes,
  });

  factory StrapiItem.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) => _$StrapiItemFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) => _$StrapiItemToJson(this, toJsonT);
}
