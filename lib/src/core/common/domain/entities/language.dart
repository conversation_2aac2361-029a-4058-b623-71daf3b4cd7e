import 'package:flutter/material.dart';

class Languages {
  late List<Language> languagesData = [];

  Languages() {
    final Language ku = Language(
      id: 1,
      backendLangCode: 'ku',
      shortDisplayLabel: 'Ku',
      fullDisplayLabel: 'کوردی',
      local: const Locale('ar', 'AE'),
      fcmTopicName: 'Kurdish_Channel',
    );

    final Language ar = Language(
      id: 2,
      backendLangCode: 'ar',
      shortDisplayLabel: 'Ar',
      fullDisplayLabel: 'العربية',
      local: const Locale('ar', 'IQ'),
      fcmTopicName: 'Arabic_Channel',
    );

    final Language en = Language(
      id: 3,
      backendLangCode: 'en',
      shortDisplayLabel: 'En',
      fullDisplayLabel: 'English',
      local: const Locale('en', 'US'),
      fcmTopicName: 'English_Channel',
    );

    languagesData.addAll([ku, ar, en]);
  }
}

class Language {
  final int id;
  final String backendLangCode;
  final String shortDisplayLabel;
  final String fullDisplayLabel;
  final Locale local;
  final String fcmTopicName;

  Language({
    required this.id,
    required this.backendLangCode,
    required this.shortDisplayLabel,
    required this.fullDisplayLabel,
    required this.local,
    required this.fcmTopicName,
  });
}
