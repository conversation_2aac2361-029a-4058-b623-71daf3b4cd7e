import 'package:equatable/equatable.dart';

import '../../../../app/logic/app_settings.dart';
import '../../../../injection.dart';

class CustomHeaders extends Equatable {
  late final Map<String, dynamic> value;
  CustomHeaders() {
    value = {
      "Accept-Language":
          serviceLocator<AppSettings>().selectedLanguage.backendLangCode,
      "X-Channel": serviceLocator<AppSettings>().xChannel,
      "channel": "mobile",
      "lang": "en",
    };
  }

  @override
  List<Object> get props => [value];
}
