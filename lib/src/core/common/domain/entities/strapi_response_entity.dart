// Generic Strapi API Response Pattern

import 'package:equatable/equatable.dart';

class StrapiResponseEntity<T extends Equatable> extends Equatable {
  final StrapiItemEntity<T> data;
  final Map<String, dynamic>? meta;

  const StrapiResponseEntity({
    required this.data,
    this.meta,
  });

  @override
  List<Object?> get props => [data, meta];
}

// Generic response entity for resource list
class StrapiListResponseEntity<T extends Equatable> extends Equatable {
  final List<StrapiItemEntity<T>> data;
  final Map<String, dynamic>? meta;

  const StrapiListResponseEntity({
    required this.data,
    this.meta,
  });

  @override
  List<Object?> get props => [data, meta];
}

// The item entity wrapper with id and attributes
class StrapiItemEntity<T extends Equatable> extends Equatable {
  final int id;
  final T attributes;

  const StrapiItemEntity({
    required this.id,
    required this.attributes,
  });

  @override
  List<Object?> get props => [id, attributes];
}
