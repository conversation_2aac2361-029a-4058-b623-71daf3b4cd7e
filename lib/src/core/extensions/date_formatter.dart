// Extension for easy DateTime formatting
import 'package:easy_localization/easy_localization.dart' show DateFormat;

extension DateTimeFormatting on DateTime {
  String getSimpleRelativeTime() {
    final now = DateTime.now();
    final diff = now.difference(this);

    if (diff.inMinutes < 60) {
      return '${diff.inMinutes} minutes ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours} hours ago';
    } else if (diff.inDays < 30) {
      return '${diff.inDays} days ago';
    } else if (diff.inDays < 365) {
      final months = (diff.inDays / 30).floor();
      return months == 1 ? 'a month ago' : '$months months ago';
    } else {
      final years = (diff.inDays / 365).floor();
      return years == 1 ? 'a year ago' : '$years years ago';
    }
  }

  String toTimeAgo() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, y').format(this);
    }
  }

  String toReadableFormat() {
    return DateFormat('MMM d, y • h:mm a').format(this);
  }

  String toChatFormat() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(year, month, day);

    if (messageDate == today) {
      return DateFormat('h:mm a').format(this);
    } else if (today.difference(messageDate).inDays == 1) {
      return 'Yesterday';
    } else if (today.difference(messageDate).inDays < 7) {
      return DateFormat('EEEE').format(this);
    } else {
      return DateFormat('M/d/yy').format(this);
    }
  }

  // Smart relative time that adapts based on how long ago
  String getSmartRelativeTime() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.isNegative) {
      // Future dates
      final futureDiff = this.difference(now);
      if (futureDiff.inMinutes < 60) {
        return 'in ${futureDiff.inMinutes} minutes';
      } else if (futureDiff.inHours < 24) {
        return 'in ${futureDiff.inHours} hours';
      } else if (futureDiff.inDays < 7) {
        return 'in ${futureDiff.inDays} days';
      } else {
        return DateFormat('MMM d, y').format(this);
      }
    }

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}w ago';
    } else {
      return DateFormat('MMM d, y').format(this);
    }
  }

  // ignore: unused_element
  String getNewsStyle() {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inHours < 24) {
      return getSmartRelativeTime();
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE h:mm a').format(this);
    } else {
      return DateFormat('MMM d • h:mm a').format(this);
    }
  }

  // ignore: unused_element
  String getChatStyle() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final messageDate = DateTime(year, month, day);

    if (messageDate == today) {
      return DateFormat('h:mm a').format(this); // "3:45 PM"
    } else if (today.difference(messageDate).inDays == 1) {
      return 'Yesterday ${DateFormat('h:mm a').format(this)}';
    } else if (today.difference(messageDate).inDays < 7) {
      return DateFormat('EEEE h:mm a').format(this); // "Monday 3:45 PM"
    } else {
      return DateFormat('M/d/yy').format(this); // "1/15/24"
    }
  }

  // ignore: unused_element
  String getEventStyle() {
    final now = DateTime.now();
    final difference = this.difference(now);

    if (difference.isNegative) {
      return 'Event ended';
    } else if (difference.inDays == 0) {
      return 'Today at ${DateFormat('h:mm a').format(this)}';
    } else if (difference.inDays == 1) {
      return 'Tomorrow at ${DateFormat('h:mm a').format(this)}';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE \'at\' h:mm a').format(this);
    } else {
      return DateFormat('MMM d \'at\' h:mm a').format(this);
    }
  }

  // ignore: unused_element
  String getLogStyle() {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(this);
  }
}
