/// A collection of available dictionaries in the application
final List<Dictionary> dictionaries = [
  Dictionary(
    id: '0',
    title: LocalizedText(
      en: 'Select Dictionary',
      ku: 'هەڵبژاردنی فەرهەنگ',
      ar: 'اختيار القاموس',
    ),
    languages: null,
    supportedLanguages: [],
  ),
  Dictionary(
    id: '10',
    title: LocalizedText(
      en: 'Kurdistanica',
      ku: 'کوردستانیکا',
      ar: 'کردستانيکا',
    ),
    languages: LocalizedText(
      en: 'English to Central Kurdish and Persian',
      ku: 'ئینگلیزی بۆ کوردیی ناوەندی و فارسی',
      ar: 'الإنجليزية إلى الكردية المركزية والفارسية',
    ),
    supportedLanguages: ['en', 'fa', 'ck'],
  ),
  Dictionary(
    id: '2',
    title: LocalizedText(
      en: 'Henbane Borîne',
      ku: 'هەنبانە بۆرینە',
      ar: 'هنبانه بورينه',
    ),
    languages: LocalizedText(
      en: 'Kurdish to Central Kurdish and Persian',
      ku: 'کوردی بۆ فارسی',
      ar: 'الكردية إلى الفارسية',
    ),
    supportedLanguages: ['fa', 'ck'],
  ),
  Dictionary(
    id: '5',
    title: LocalizedText(
      en: 'Wişename Dictionary',
      ku: 'فەرهەنگی وشەنامە',
      ar: 'قاموس وشنامة',
    ),
    languages: LocalizedText(
      en: 'Hawrami Kurdish to Central Kurdish',
      ku: 'هەورامی بۆ کوردیی ناوەندی',
      ar: 'الهورامية - الكردية المركزية',
    ),
    supportedLanguages: ['ck'],
  ),
  Dictionary(
    id: '1',
    title: LocalizedText(
      en: 'Xall Dictionary',
      ku: 'فەرهەنگی خاڵ',
      ar: 'قاموس خال',
    ),
    languages: LocalizedText(
      en: 'Kurdish to Central Kurdish',
      ku: 'کوردی بۆ کوردیی ناوەندی',
      ar: 'الكردية إلى الكردية المركزية',
    ),
    supportedLanguages: ['ck'],
  ),
  Dictionary(
    id: '20',
    title: LocalizedText(
      en: 'Destî Dictionary',
      ku: 'فەرهەنگا دەستی',
      ar: 'قاموس دستي',
    ),
    languages: LocalizedText(
      en: 'Northern Kurdish to Northern Kurdish',
      ku: 'کوردی کورمانجی بۆ کوردی کورمانجی ',
      ar: 'الكردية الشمالية إلى الكردية الشمالية',
    ),
    supportedLanguages: ['nk'],
  ),
  Dictionary(
    id: '23',
    title: LocalizedText(
      en: 'Zhyan',
      ku: 'ژیان',
      ar: 'جيان',
    ),
    languages: LocalizedText(
      en: 'English to Central Kurdish and Persian',
      ku: 'ئینگلیزی بۆ کوردیی ناوەندی و فارسی',
      ar: 'الإنجليزية إلى الكردية المركزية والفارسية',
    ),
    supportedLanguages: ['en', 'ck', 'fa'],
  ),
  Dictionary(
    id: '15',
    title: LocalizedText(
      en: 'Şêxanî Dictionary',
      ku: 'فەرهەنگی شێخانی',
      ar: 'قاموس شيخاني',
    ),
    languages: LocalizedText(
      en: 'Kurdish to Central Kurdish',
      ku: 'فەرهەنگی شێخانی',
      ar: 'قاموس شيخاني',
    ),
    supportedLanguages: ['ck'],
  ),
  Dictionary(
    id: '4',
    title: LocalizedText(
      en: 'Kanî Dictionary',
      ku: 'فەرهەنگا کانی',
      ar: 'قاموس کاني',
    ),
    languages: LocalizedText(
      en: 'Northern Kurdish to Central Kurdish',
      ku: 'کوردی کورمانجی بۆ کوردیی ناوەندی',
      ar: 'الكردية الشمالية إلى الكردية المركزية',
    ),
    supportedLanguages: ['nk', 'ck'],
  ),
  Dictionary(
    id: '22',
    title: LocalizedText(
      en: 'Newekan Dictionary',
      ku: 'فەرهەنگی نەوەکان',
      ar: 'قاموس نواکان',
    ),
    languages: LocalizedText(
      en: 'Arabic to Central Kurdish',
      ku: 'عەرەبی بۆ کوردیی ناوەندی',
      ar: 'العربية إلى الكردية المركزية',
    ),
    supportedLanguages: ['ck'],
  ),
  Dictionary(
    id: '17',
    title: LocalizedText(
      en: 'Koma Kurdiya Kurmancî Dictionary',
      ku: 'فەرهەنگا کۆما کوردییا کورمانجی',
      ar: 'قاموس کوما کردیا کرمانجي',
    ),
    languages: LocalizedText(
      en: 'Northern Kurdish to Persian',
      ku: 'کوردی کورمانجی بۆ فارسی',
      ar: 'الكردية الشمالية إلى الفارسية',
    ),
    supportedLanguages: ['nk', 'fa'],
  ),
  Dictionary(
    id: '3',
    title: LocalizedText(
      en: 'A Kurdish-English Dictionary',
      ku: 'فەرهەنگی کوردی - ئینگلیزی',
      ar: 'قاموس كردي - إنجليزي',
    ),
    languages: LocalizedText(
      en: 'Central Kurdish to English',
      ku: 'کوردیی ناوەندی بۆ ئینگلیزی',
      ar: 'الكردية المركزية إلى الإنجليزية',
    ),
    supportedLanguages: ['ck', 'en'],
  ),
  Dictionary(
    id: '21',
    title: LocalizedText(
      en: 'Rêjge Dictionary',
      ku: 'فەرهەنگی ڕێژگە',
      ar: 'قاموس ريجكة',
    ),
    languages: LocalizedText(
      en: 'Kurdish to Central Kurdish and Persian',
      ku: 'کوردی بۆ کوردیی ناوەندی و فارسی',
      ar: 'الكردية إلى الكردية المركزية والفارسية',
    ),
    supportedLanguages: ['ck', 'fa'],
  ),
  Dictionary(
    id: '6',
    title: LocalizedText(
      en: 'Andeek Dictionary',
      ku: 'فەرهەنگی ئەندێک',
      ar: 'قاموس أنديك',
    ),
    languages: LocalizedText(
      en: 'English to Central Kurdish',
      ku: 'ئینگلیزی بۆ کوردیی ناوەندی',
      ar: 'الإنجليزية إلى الكردية المركزية',
    ),
    supportedLanguages: ['en', 'ck'],
  ),
  Dictionary(
    id: '19',
    title: LocalizedText(
      en: 'IT Dictionary',
      ku: 'فەرهەنگی تەکنۆلۆژیای زانیاری',
      ar: 'قاموس تكنولوجيا المعلومات',
    ),
    languages: LocalizedText(
      en: 'English to Central Kurdish',
      ku: 'ئینگلیزی بۆ کوردیی ناوەندی',
      ar: 'الإنجليزية إلى الكردية المركزية',
    ),
    supportedLanguages: ['en', 'ck'],
  ),
  Dictionary(
    id: '18',
    title: LocalizedText(
      en: 'Pîşk ên Kurdî Dictionary',
      ku: 'فەرهەنگێ پیشکێن کوردی',
      ar: 'قاموس پشکين کردي',
    ),
    languages: LocalizedText(
      en: 'Northern Kurdish to Northern Kurdish and English and Arabic',
      ku: 'کوردی کورمانجی بۆ کوردی کورمانجی  و ئینگلیزی و عەرەبی',
      ar: 'الكردية الشمالية إلى الكردية الشمالية والإنجليزية والعربية',
    ),
    supportedLanguages: ['nk', 'en'],
  ),
  Dictionary(
    id: '14',
    title: LocalizedText(
      en: 'Xişil û Poşakî Jinaney Mukiryan',
      ku: 'خشڵ و پۆشاکی ژنانەی موکریان',
      ar: 'خشل وبوشاکي جناني مکریان',
    ),
    languages: LocalizedText(
      en: 'Central Kurdish to Central Kurdish',
      ku: 'کوردیی ناوەندی بۆ کوردی ناوەندی',
      ar: 'الكردية المركزية إلى الكردية المركزية',
    ),
    supportedLanguages: ['ck'],
  ),
];

/// Represents a dictionary with multilingual information
class Dictionary {
  final String id;
  final LocalizedText title;
  final LocalizedText? languages;
  final List<String> supportedLanguages;

  const Dictionary({
    required this.id,
    required this.title,
    this.languages,
    required this.supportedLanguages,
  });

  /// Creates a Dictionary from a JSON map
  factory Dictionary.fromJson(Map<String, dynamic> json) {
    return Dictionary(
      id: json['id'] as String,
      title: LocalizedText.fromJson(json['title'] as Map<String, dynamic>),
      languages: json['languages'] != null ? LocalizedText.fromJson(json['languages'] as Map<String, dynamic>) : null,
      supportedLanguages: (json['supportedLanguages'] as List<dynamic>).map((e) => e as String).toList(),
    );
  }

  /// Converts the Dictionary to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title.toJson(),
      'languages': languages?.toJson(),
      'supportedLanguages': supportedLanguages,
    };
  }
}

/// Represents text in multiple languages
class LocalizedText {
  final String en;
  final String ku;
  final String ar;

  const LocalizedText({
    required this.en,
    required this.ku,
    required this.ar,
  });

  /// Creates a LocalizedText from a JSON map
  factory LocalizedText.fromJson(Map<String, dynamic> json) {
    return LocalizedText(
      en: json['en'] as String,
      ku: json['ku'] as String,
      ar: json['ar'] as String,
    );
  }

  /// Converts the LocalizedText to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'en': en,
      'ku': ku,
      'ar': ar,
    };
  }

  /// Gets the text for the specified locale
  String getLocalizedText(String locale) {
    switch (locale) {
      case 'en':
        return en;
      case 'ku':
        return ku;
      case 'ar':
        return ar;
      default:
        return en;
    }
  }
}

/// Constants related to the dictionary feature
class LanguageCodes {
  const LanguageCodes._();

  /// English language code
  static const String english = 'en';

  /// Central Kurdish language code
  static const String centralKurdish = 'ckb';

  /// Northern Kurdish language code
  static const String northernKurdish = 'kmr';

  /// Arabic language code
  static const String arabic = 'ar';

  /// Persian/Farsi language code
  static const String persian = 'fa';

  /// Turkish language code
  static const String turkish = 'tr';

  /// Kurdish Sorani dialect language code
  static const String sorani = 'ckb-so';

  /// Kurdish Badini dialect language code
  static const String badini = 'ckb-ba';

  /// Returns a language name based on language code
  static String getLanguageName(String code, {String locale = 'en'}) {
    final names = {
      'en': {
        english: 'English',
        centralKurdish: 'Central Kurdish',
        northernKurdish: 'Northern Kurdish',
        persian: 'Persian',
        arabic: 'Arabic',
      },
      'ku': {
        english: 'ئینگلیزی',
        centralKurdish: 'کوردیی ناوەندی',
        northernKurdish: 'کوردیی باکوور',
        persian: 'فارسی',
        arabic: 'عەرەبی',
      },
      'ar': {
        english: 'الإنجليزية',
        centralKurdish: 'الكردية المركزية',
        northernKurdish: 'الكردية الشمالية',
        persian: 'الفارسية',
        arabic: 'العربية',
      },
    };

    return names[locale]?[code] ?? code;
  }
}

/// Dictionary IDs for various dictionaries
class DictionaryIds {
  const DictionaryIds._();

  /// English to Kurdish dictionary
  static const String englishToKurdish = '10';

  /// Kurdish to English dictionary
  static const String kurdishToEnglish = '11';

  /// Arabic to Kurdish dictionary
  static const String arabicToKurdish = '20';

  /// Kurdish to Arabic dictionary
  static const String kurdishToArabic = '21';

  /// Turkish to Kurdish dictionary
  static const String turkishToKurdish = '30';

  /// Kurdish to Turkish dictionary
  static const String kurdishToTurkish = '31';

  /// Persian to Kurdish dictionary
  static const String persianToKurdish = '40';

  /// Kurdish to Persian dictionary
  static const String kurdishToPersian = '41';
}

/// Language pairs available for translation
class LanguagePairs {
  const LanguagePairs._();

  /// List of available language pairs with their corresponding labels and codes
  static const List<Map<String, dynamic>> pairs = [
    {
      'label': 'English - Kurdish',
      'sourceCode': LanguageCodes.english,
      'targetCode': LanguageCodes.centralKurdish,
      'dictionaryId': DictionaryIds.englishToKurdish,
    },
    {
      'label': 'Kurdish - English',
      'sourceCode': LanguageCodes.centralKurdish,
      'targetCode': LanguageCodes.english,
      'dictionaryId': DictionaryIds.kurdishToEnglish,
    },
    {
      'label': 'Arabic - Kurdish',
      'sourceCode': LanguageCodes.arabic,
      'targetCode': LanguageCodes.centralKurdish,
      'dictionaryId': DictionaryIds.arabicToKurdish,
    },
    {
      'label': 'Kurdish - Arabic',
      'sourceCode': LanguageCodes.centralKurdish,
      'targetCode': LanguageCodes.arabic,
      'dictionaryId': DictionaryIds.kurdishToArabic,
    },
    {
      'label': 'Turkish - Kurdish',
      'sourceCode': LanguageCodes.turkish,
      'targetCode': LanguageCodes.centralKurdish,
      'dictionaryId': DictionaryIds.turkishToKurdish,
    },
    {
      'label': 'Kurdish - Turkish',
      'sourceCode': LanguageCodes.centralKurdish,
      'targetCode': LanguageCodes.turkish,
      'dictionaryId': DictionaryIds.kurdishToTurkish,
    },
    {
      'label': 'Persian - Kurdish',
      'sourceCode': LanguageCodes.persian,
      'targetCode': LanguageCodes.centralKurdish,
      'dictionaryId': DictionaryIds.persianToKurdish,
    },
    {
      'label': 'Kurdish - Persian',
      'sourceCode': LanguageCodes.centralKurdish,
      'targetCode': LanguageCodes.persian,
      'dictionaryId': DictionaryIds.kurdishToPersian,
    },
  ];

  /// Get the dictionary ID for a given source and target language
  static String getDictionaryId(String sourceCode, String targetCode) {
    final pair = pairs.firstWhere(
      (pair) => pair['sourceCode'] == sourceCode && pair['targetCode'] == targetCode,
      orElse: () => {'dictionaryId': DictionaryIds.englishToKurdish},
    );

    return pair['dictionaryId'] as String;
  }
}
