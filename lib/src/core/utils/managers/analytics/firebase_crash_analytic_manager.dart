// import 'package:firebase_crashlytics/firebase_crashlytics.dart';

abstract class FirebaseCrashAnalyticManager {
  Future<void> init();

  Future<void> logCrashAnalyticsError(String errorMessage);

  Future<void> recordError(String error, StackTrace stackTrace);
}

class FirebaseCrashAnalyticManagerImpl implements FirebaseCrashAnalyticManager {
  // FirebaseCrashlytics? _firebaseCrashlytics;

  @override
  Future<void> init() async {
    // _firebaseCrashlytics = FirebaseCrashlytics.instance;
  }

  @override
  Future<void> logCrashAnalyticsError(String errorMessage) async {
    // await _firebaseCrashlytics?.log(errorMessage);
  }

  @override
  Future<void> recordError(String error, StackTrace stackTrace) async {
    // await _firebaseCrashlytics?.recordError(error, stackTrace);
  }
}
