import 'package:firebase_analytics/firebase_analytics.dart';

abstract class FirebaseAnalyticsManager {
  Future<void> init();

  Future<void> setUserPropertyItem(String name, String value);

  Future<void> setTrackingScreen(String screenName);

  Future<void> logAnalyticEvent(
      String logName, Map<String, Object>? parameters);

  Future<void> logAppOpen();
}

class FirebaseAnalyticsManagerImpl implements FirebaseAnalyticsManager {
  FirebaseAnalytics? _analytics;

  @override
  Future<void> init() async {
    _analytics = FirebaseAnalytics.instance;
  }

  @override
  Future<void> setUserPropertyItem(String name, String value) async {
    await _analytics?.setUserProperty(name: name, value: value);
  }

  @override
  Future<void> setTrackingScreen(String screenName) async {
    await _analytics?.logScreenView(
      screenName: screenName,
    );
  }

  @override
  Future<void> logAnalyticEvent(
    String logName,
    Map<String, Object>? parameters,
  ) async {
    await _analytics?.logEvent(name: logName, parameters: parameters);
  }

  @override
  Future<void> logAppOpen() async {
    await _analytics?.logAppOpen();
  }
}
