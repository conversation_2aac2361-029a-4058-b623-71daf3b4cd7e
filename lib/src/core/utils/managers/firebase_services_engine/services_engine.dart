import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import '../analytics/firebase_crash_analytic_manager.dart';
import '../../../../../firebase_options.dart';

import '../../../../app/logic/app_settings.dart';
import '../../../../injection.dart';
import '../../helpers/gms_hms_checker.dart';
import '../analytics/firebase_analytics_manager.dart';
import '../http/check_endpoint_reachability.dart';
import '../http/http_manager.dart';
import '../http/http_methods.dart';
import '../notification/gms_notification_manager.dart';
import '../notification/notifications_manager.dart';

abstract class ServicesEngine {
  Future<void> init();

  Future<String> getToken();

  Future<void> topicSubscribe(String topic);

  Future<void> topicUnSubscribe(String topic);

  Future<void> setUserPropertyItemFbAnalytics(String name, String value);

  Future<void> setTrackingScreenFbAnalytics(String screenName);

  Future<void> logEventFbAnalytics(String logName, Map<String, Object>? parameters);

  Future<void> logAppOpenFbAnalytics();

  Future<void> logErrorFbCrashAnalytics(String errorMessage);

  Future<void> recordErrorFbCrashAnalytics(String error, StackTrace stackTrace);
}

class ServicesEngineImpl implements ServicesEngine {
  final GmsAndHmsChecker gmsAndHmsChecker;
  final HttpManager httpManager;
  // final RSAEncryptionManager rsaEncryptionManager;
  final FirebaseAnalyticsManager firebaseAnalyticsManager;
  final FirebaseCrashAnalyticManager firebaseCrashAnalyticManager;

  late NotificationsManager platformNotificationsManager;

  ServicesEngineImpl({
    required this.gmsAndHmsChecker,
    required this.httpManager,
    // required this.rsaEncryptionManager,
    required this.firebaseAnalyticsManager,
    required this.firebaseCrashAnalyticManager,
  });

  bool isNotificationInit = false;

  @override
  Future<void> init() async {
    try {
      if (isNotificationInit) return;
      isNotificationInit = true;

      /************* Check internet *************/
      final ReachabilityStatus googleReachabilityStatus = await serviceLocator<CheckEndpointReachability>().check("kGoogleCheckEndpoint");

      final bool isInternetAvailable = googleReachabilityStatus == ReachabilityStatus.reachable;

      // serviceLocator<AppSettings>().isInternetAvailable = isInternetAvailable;

      /************* Check Gms and Hms *************/

      final GmsAndHmsCheckerStatus gmsAndHmsCheckerStatus = await gmsAndHmsChecker.check();
      bool isGms = gmsAndHmsCheckerStatus == GmsAndHmsCheckerStatus.gms;
      bool isHms = gmsAndHmsCheckerStatus == GmsAndHmsCheckerStatus.hms;
      serviceLocator<AppSettings>().isHcm = isHms;

      /************* init fb *************/
      if (isInternetAvailable) {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );

        /************* init notifications manager *************/

        if (isGms) {
          platformNotificationsManager = serviceLocator<GmsNotificationsManager>();
        } else if (isHms) {
          // platformNotificationsManager =
          //     serviceLocator<HmsNotificationsManager>();
        }

        await platformNotificationsManager.init();

        topicSubscribe(serviceLocator<AppSettings>().selectedLanguage.fcmTopicName);

        /************* init notifications manager *************/

        final bool isFBActive = isGms && isInternetAvailable;

        if (isFBActive) {
          // await FirebaseCrashlytics.instance
          //     .setCrashlyticsCollectionEnabled(true);
          httpManager.runPerformanceLog();
          // await firebaseAnalyticsManager.init();
          // await firebaseCrashAnalyticManager.init();
        }
      }

      await _submitToken(isHms);
    } catch (error) {
      debugPrint(error.toString());
    }
  }

  @override
  Future<String> getToken() async {
    return "";
  }

  @override
  Future<void> topicSubscribe(String topic) async {
    try {
      await platformNotificationsManager.topicSubscribe(topic);
    } catch (e) {
      debugPrint("error $e");
    }
  }

  @override
  Future<void> topicUnSubscribe(String topic) async {
    try {
      await platformNotificationsManager.topicUnSubscribe(topic);
    } catch (e) {
      debugPrint("error $e");
    }
  }

  @override
  Future<void> setUserPropertyItemFbAnalytics(String name, String value) async {
    await firebaseAnalyticsManager.setUserPropertyItem(name, value);
  }

  @override
  Future<void> setTrackingScreenFbAnalytics(String screenName) async {
    await firebaseAnalyticsManager.setTrackingScreen(screenName);
  }

  @override
  Future<void> logEventFbAnalytics(String logName, Map<String, Object>? parameters) async {
    await firebaseAnalyticsManager.logAnalyticEvent(logName, parameters);
  }

  @override
  Future<void> logAppOpenFbAnalytics() async {
    await firebaseAnalyticsManager.logAppOpen();
  }

  @override
  Future<void> logErrorFbCrashAnalytics(String errorMessage) async {
    await firebaseCrashAnalyticManager.logCrashAnalyticsError(errorMessage);
  }

  @override
  Future<void> recordErrorFbCrashAnalytics(String error, StackTrace stackTrace) async {
    await firebaseCrashAnalyticManager.recordError(error, stackTrace);
  }

  Future<void> _submitToken(bool isHmsActive) async {
    String token = await getToken();

    // final bool internetAvailable =
    //     serviceLocator<AppSettings>().isInternetAvailable ?? true;

    if (token.isEmpty) return;
    // if (!internetAvailable) return;

    final Map<String, dynamic> header = {
      "Accept-Language": serviceLocator<AppSettings>().selectedLanguage.backendLangCode,
      "X-Channel": serviceLocator<AppSettings>().xChannel,
    };

    // final Map<String, dynamic> params = {
    //   "token": token,
    //   "is_hcm": isHmsActive,
    // };

    // final String encryptedParams =
    //     await rsaEncryptionManager.encryptedPayload(params);

    try {
      await httpManager.request(
        path: "/fcm",
        method: HttpMethods.put,
        headers: header,
        payload: "encryptedParams",
      );
    } catch (error) {
      debugPrint(error.toString());
    }
  }
}
