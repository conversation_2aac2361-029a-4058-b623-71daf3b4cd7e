import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

abstract class NotificationMessageHandler {
  void onMessageReceived(message);

  void onMessageOpenedAppGMS(message);

  void onNotificationOpenAppHMS(initialNotification);

  void onMessageReceiveError(error);
}

class NotificationMessageHandlerImpl implements NotificationMessageHandler {
  @override
  void onMessageReceived(message) {
    if (message == null) return;
    final String title = message.notification?.title ?? "";
    final String body = message.notification?.body ?? "";

    bool isIphone = Platform.isIOS;

    final Map<String, dynamic> notificationData = message.data;
    final String? notificationAction = notificationData['action'];

    bool isImageAvilable = (message.notification?.apple?.imageUrl != null ||
        message.notification?.android?.imageUrl != null);
    _showNotificationDialog(
      title,
      body,
      notificationAction,
      isImageAvilable
          ? isIphone
              ? "${message.notification?.apple?.imageUrl}"
              : "${message.notification?.android?.imageUrl}"
          : "",
    );
  }

  @override
  void onMessageOpenedAppGMS(message) {
    if (message == null) return;
  }

  @override
  void onNotificationOpenAppHMS(initialNotification) {
    if (initialNotification == null) return;
    final Map<String, dynamic>? initialNotificationData =
        initialNotification as Map<String, dynamic>?;

    if (initialNotificationData == null) return;
  }

  @override
  void onMessageReceiveError(error) {
    final PlatformException e = error as PlatformException;
    debugPrint("_onMessageReceiveError ${e.message ?? ""}");
  }

  void _showNotificationDialog(
    String title,
    String body,
    String? notificationAction,
    String? image,
  ) async {}
}
