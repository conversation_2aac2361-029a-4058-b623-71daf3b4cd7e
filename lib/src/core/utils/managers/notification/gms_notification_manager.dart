import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';

import '../../../../app/logic/app_settings.dart';
import '../../../../injection.dart';
import 'notification_message_handler.dart';
import 'notifications_manager.dart';

class GmsNotificationsManager implements NotificationsManager {
  final NotificationMessageHandler notificationMessageHandler;
  FirebaseMessaging? _firebaseInstance;

  GmsNotificationsManager({
    required this.notificationMessageHandler,
  });

  @override
  Future<void> init() async {
    _firebaseInstance = FirebaseMessaging.instance;

    topicSubscribe(serviceLocator<AppSettings>().selectedLanguage.fcmTopicName);

    await _firebaseInstance?.requestPermission();

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    notificationMessageHandler.onMessageOpenedAppGMS(initialMessage);

    FirebaseMessaging.onMessage
        .listen(notificationMessageHandler.onMessageReceived);
    FirebaseMessaging.onMessageOpenedApp
        .listen(notificationMessageHandler.onMessageOpenedAppGMS);
  }

  @override
  Future<String?> getToken() async {
    return _firebaseInstance?.getToken();
  }

  @override
  Future<void> topicSubscribe(String topic) async {
    await _firebaseInstance?.subscribeToTopic(topic);
  }

  @override
  Future<void> topicUnSubscribe(String topic) async {
    await _firebaseInstance?.unsubscribeFromTopic(topic);
  }
}
