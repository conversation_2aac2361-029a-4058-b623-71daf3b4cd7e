// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:huawei_push/huawei_push.dart';

// import '../../../../app/logic/app_settings.dart';
// import '../../../../injection.dart';
// import 'notification_message_handler.dart';
// import 'notifications_manager.dart';

// class HmsNotificationsManager implements NotificationsManager {
//   final NotificationMessageHandler notificationMessageHandler;
//   String? _token;

//   HmsNotificationsManager({required this.notificationMessageHandler});

//   @override
//   Future<void> init() async {
//     Push.getToken('');
//     Push.getTokenStream.listen(_onTokenEvent, onError: _onTokenError);
//     final initialNotification = await Push.getInitialNotification();
//     topicSubscribe(serviceLocator<AppSettings>().selectedLanguage.fcmTopicName);
//     Push.onNotificationOpenedApp
//         .listen(notificationMessageHandler.onNotificationOpenAppHMS);
//     notificationMessageHandler.onNotificationOpenAppHMS(initialNotification);
//     Push.onMessageReceivedStream.listen(
//         notificationMessageHandler.onMessageReceived,
//         onError: notificationMessageHandler.onMessageReceiveError);
//   }

//   @override
//   Future<String?> getToken() async {
//     return _token ?? await Push.getTokenStream.first;
//   }

//   void _onTokenEvent(String? event) {
//     _token = event;
//     debugPrint("TokenEvent $_token");
//   }

//   void _onTokenError(Object error) {
//     final PlatformException e = error as PlatformException;
//     debugPrint("TokenErrorEvent ${e.message ?? ""}");
//   }

//   @override
//   Future<void> topicSubscribe(String topic) async {
//     try {
//       final String result = await Push.subscribe(topic);
//       debugPrint("subscribe  $result");
//     } catch (e) {
//       debugPrint("error $e");
//     }
//   }

//   @override
//   Future<void> topicUnSubscribe(String topic) async {
//     try {
//       final String result = await Push.unsubscribe(topic);
//       debugPrint("unsubscribe $result");
//     } catch (e) {
//       debugPrint("error $e");
//     }
//   }
// }
