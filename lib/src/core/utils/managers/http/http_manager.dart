import 'dart:developer' show log;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../app/logic/app_settings.dart';
import '../../helpers/colored_dio_logger.dart';
import '../database/database_manager.dart';
import 'http_methods.dart';

/// HTTP Manager for handling API requests and responses
///
/// This file contains the implementation of HTTP client functionality using Dio.
/// It handles authentication, token refresh, request/response logging, and error handling.
///
/// Abstract class defining the contract for HTTP operations
/// This interface defines the methods that any HTTP manager implementation must provide.
abstract class HttpManager {
  /// Makes an HTTP request to the specified endpoint
  ///
  /// Parameters:
  /// - [path]: The API endpoint path
  /// - [method]: HTTP method (GET, POST, PUT, DELETE)
  /// - [params]: Optional query parameters
  /// - [headers]: Optional HTTP headers
  /// - [payload]: Optional request body data
  /// - [customBaseUrl]: Optional custom base URL (overrides default)
  /// - [timeOut]: Optional custom timeout duration
  Future<Response> request({required String path, required HttpMethods method, Map<String, dynamic>? params, Map<String, dynamic>? headers, Object? payload, String? customBaseUrl, Duration? timeOut});

  /// Enables performance logging for HTTP requests
  void runPerformanceLog();

  /// Enables or disables HTTP request/response logging
  ///
  /// Parameters:
  /// - [enabled]: Whether logging should be enabled or disabled
  void setLoggingEnabled(bool enabled);
}

/// Implementation of the HttpManager interface
///
/// Handles all HTTP communication in the app with support for:
/// - Token-based authentication
/// - Automatic token refresh
/// - Request/response logging
/// - Error handling
class HttpManagerImpl implements HttpManager {
  /// Main Dio instance for regular API requests
  late final Dio _dio;

  /// Separate Dio instance for authentication-related requests
  late final Dio _authDio;

  final AppSettings appSettings;

  // final FirebasePerformanceManager _dioFirebasePerformanceInterceptor =
  //     FirebasePerformanceManager();

  /// Flag to track if performance logging is enabled
  bool isPerformanceLogActive = false;

  /// Flag to track if logging is enabled
  bool isLoggingEnabled = false;

  /// Creates a new HttpManagerImpl instance
  ///
  /// Parameters:
  /// - [baseOptions]: Dio configuration options
  /// - [databaseManager]: Database manager for token storage
  HttpManagerImpl({required BaseOptions baseOptions, required DatabaseManager databaseManager, required this.appSettings}) {
    initHttpManager(baseOptions: baseOptions, databaseManager: databaseManager);
  }

  /// Initializes the HTTP manager with Dio instances and interceptors
  ///
  /// Sets up two Dio instances:
  /// - _dio: For regular API requests with token authentication
  /// - _authDio: For authentication-specific requests
  Future<void> initHttpManager({required BaseOptions baseOptions, required DatabaseManager databaseManager}) async {
    _dio = Dio(baseOptions);

    _authDio = Dio(baseOptions);

    _authDio.interceptors.addAll([_appAuthInterceptor()]);

    _dio.interceptors.addAll([_appInterceptor()]);

    if (isLoggingEnabled) {
      // Create a custom colored logger
      ColoredDioLogger coloredLogger = ColoredDioLogger(requestHeader: true, requestBody: true, responseBody: true, responseHeader: false, error: true, compact: true, maxWidth: 120, enabled: kDebugMode);

      // Add the logger to both Dio instances
      _dio.interceptors.add(coloredLogger);
      _authDio.interceptors.add(coloredLogger);
    }
  }

  /// Enables or disables HTTP request/response logging
  ///
  /// Use this method to toggle logging on or off at runtime
  @override
  void setLoggingEnabled(bool enabled) {
    isLoggingEnabled = enabled;

    if (!enabled) {
      // Remove any ColoredDioLogger instances from both Dio instances
      _dio.interceptors.removeWhere((interceptor) => interceptor is ColoredDioLogger);
      _authDio.interceptors.removeWhere((interceptor) => interceptor is ColoredDioLogger);
    } else {
      // Re-add loggers if they're not already present
      if (!_dio.interceptors.any((i) => i is ColoredDioLogger)) {
        _dio.interceptors.add(ColoredDioLogger(requestHeader: true, requestBody: true, responseBody: true, responseHeader: false, error: true, compact: true, maxWidth: 120, enabled: kDebugMode));
      }

      if (!_authDio.interceptors.any((i) => i is ColoredDioLogger)) {
        _authDio.interceptors.add(ColoredDioLogger(requestHeader: true, requestBody: true, responseBody: true, responseHeader: false, error: true, compact: true, maxWidth: 120, enabled: kDebugMode));
      }
    }
  }

  /// Enables performance logging for HTTP requests
  ///
  /// Currently has Firebase performance monitoring code commented out.
  /// Only sets the flag to true when called.
  @override
  void runPerformanceLog() {
    try {
      if (!isPerformanceLogActive) {
        // Firebase performance monitoring (commented out)
        // _authDio.interceptors.add(_dioFirebasePerformanceInterceptor);
        // _dio.interceptors.add(_dioFirebasePerformanceInterceptor);

        // Make sure colored logger is added if logging is enabled
        if (isLoggingEnabled) {
          if (!_dio.interceptors.any((i) => i is ColoredDioLogger)) {
            _dio.interceptors.add(ColoredDioLogger(requestHeader: true, requestBody: true, responseBody: true, responseHeader: false, error: true, compact: true, maxWidth: 120, enabled: kDebugMode));
          }

          if (!_authDio.interceptors.any((i) => i is ColoredDioLogger)) {
            _authDio.interceptors.add(ColoredDioLogger(requestHeader: true, requestBody: true, responseBody: true, responseHeader: false, error: true, compact: true, maxWidth: 120, enabled: kDebugMode));
          }
        }

        isPerformanceLogActive = true;
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  /// Makes an HTTP request to the specified endpoint
  ///
  /// Core method for making all types of HTTP requests (GET, POST, PUT, DELETE).
  /// Configures the request with appropriate timeout, headers, parameters, and base URL.
  ///
  /// Parameters:
  /// - [path]: The API endpoint path
  /// - [method]: HTTP method (GET, POST, PUT, DELETE)
  /// - [params]: Optional query parameters
  /// - [headers]: Optional HTTP headers
  /// - [payload]: Optional request body data
  /// - [customBaseUrl]: Optional custom base URL (overrides default)
  /// - [timeOut]: Optional custom timeout duration
  @override
  Future<Response> request({required String path, required HttpMethods method, Map<String, dynamic>? params, Map<String, dynamic>? headers, Object? payload, String? customBaseUrl, Duration? timeOut}) async {
    if (timeOut != null) {
      _dio.options.connectTimeout = timeOut;
      _dio.options.receiveTimeout = timeOut;
      _dio.options.sendTimeout = timeOut;
    } else {
      _dio.options.connectTimeout = const Duration(seconds: 15);
      _dio.options.receiveTimeout = const Duration(seconds: 15);
      _dio.options.sendTimeout = const Duration(seconds: 15);
    }

    // Add custom headers if provided
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }

    _dio.options.queryParameters.clear();
    // Configure query parameters
    if (params != null) {
      _dio.options.queryParameters.addAll(params);
    }

    // Set base URL (custom or from app settings)
    if (customBaseUrl != null) {
      _dio.options.baseUrl = customBaseUrl;
    } else {
      _dio.options.baseUrl = appSettings.apiBaseUrl ?? '';
    }

    // Execute the appropriate HTTP method
    switch (method) {
      case HttpMethods.get:
        return _dio.get(path);
      case HttpMethods.post:
        return _dio.post(path, data: payload);
      case HttpMethods.put:
        return _dio.put(path, data: payload);
      case HttpMethods.delete:
        return _dio.delete(path);
    }
  }
}

/// Extension methods for HttpManagerImpl to handle interceptors and token refresh
extension HttpManagerImplHelpers on HttpManagerImpl {
  /// Creates an interceptor for authentication-related requests
  ///
  /// This interceptor is simpler than the main interceptor and doesn't handle token refresh.
  /// It's used by the _authDio instance for authentication-specific requests.
  InterceptorsWrapper _appAuthInterceptor() {
    return InterceptorsWrapper(
      // Configure request before sending
      onRequest: (options, handler) {
        options.baseUrl = appSettings.apiBaseUrl ?? "";

        // debugPrint(
        //   'Call: => BASE: ${options.baseUrl}',
        // );

        return handler.next(options);
      },
      // Log successful responses
      onResponse: (response, handler) {
        // debugPrint(
        //   'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
        // );
        return handler.next(response);
      },
      // Log and handle errors
      onError: (DioException err, handler) async {
        // debugPrint(
        //   'ERROR[${err.response?.statusCode ?? 0}] => PATH: ${err.requestOptions.path}',
        // );
        return handler.next(err);
      },
    );
  }

  /// Creates the main interceptor for regular API requests
  ///
  /// This interceptor:
  /// 1. Adds authentication token to request headers
  /// 2. Logs request/response details
  /// 3. Handles 401 (Unauthorized) errors by attempting to refresh the token
  InterceptorsWrapper _appInterceptor() {
    return InterceptorsWrapper(
      // Configure request before sending
      onRequest: (options, handler) async {
        // Set base URL if not already set
        if (options.baseUrl.isEmpty) {
          options.baseUrl = appSettings.apiBaseUrl ?? "";
        }

        // debugPrint(
        //   'Call: => BASE: ${options.baseUrl}',
        // );

        // Get authentication token from secure storage
        // final String? token = await _databaseManager.getSecureData(SecureStorageKey.token);

        // Add authentication token and other headers
        // options.headers.addAll({
        // 'Authorization': 'Bearer $token',
        // "Accept": "application/json",
        // "Content-Type": "application/json",
        // });

        // TODO: logs

        // log("PATH ${options.path}");
        // log("HEADERS: ${options.headers}");

        return handler.next(options);
      },
      // Log successful responses
      onResponse: (response, handler) {
        // log('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');

        // log(' PATH: ${response.requestOptions.path} response  : [RESPONSE]=> ${response.data}');
        return handler.next(response);
      },
      // Handle errors, with special handling for 401 (Unauthorized)
      onError: (DioException err, handler) async {
        return handler.next(err);
      },
    );
  }
}

final baseOptionsProvider = Provider<BaseOptions>((ref) {
  return BaseOptions(
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      "charset": "utf-8",
      "Accept-Charset": "utf-8",
    },
    responseType: ResponseType.plain,
    receiveDataWhenStatusError: true,
    connectTimeout: const Duration(seconds: 15),
    receiveTimeout: const Duration(seconds: 15),
    sendTimeout: const Duration(seconds: 15),
  );
});

final httpManagerProvider = Provider<HttpManager>((ref) {
  return HttpManagerImpl(
    baseOptions: ref.read(baseOptionsProvider),
    databaseManager: ref.read(databaseManagerProvider),
    appSettings: ref.read(appSettingsProvider),
  );
});
