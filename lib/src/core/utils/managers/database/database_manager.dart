import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;

import '../../../enums/secure_storage_key.dart';

abstract class DatabaseManager {
  Future<void> openBox();
  void saveData(String key, dynamic value);
  dynamic getData(String key);
  Future<void> deleteData(String key);
  // Secure data (API keys, tokens, refresh tokens)
  // void saveSecureData(SecureStorageKey secureStorageKey, dynamic value);
  Future<void> saveSecureData(SecureStorageKey secureStorageKey, String value);
  Future<String?> getSecureData(SecureStorageKey secureStorageKey);
  Future<void> deleteSecureData(SecureStorageKey secureStorageKey);
  Future<bool> isValueEmpty(SecureStorageKey secureStorageKey);
}

extension SecureStorageKeyExtension on SecureStorageKey {
  String get key => toString().split('.').last;
}

// extension SecureStorageKeyExtension on SecureStorageKey {
//   String get key {
//     switch (this) {
//       case SecureStorageKey.apiKey:
//         return 'apiKey';
//       case SecureStorageKey.token:
//         return 'token';
//       case SecureStorageKey.refreshToken:
//         return 'refreshToken';
//       default:
//         return '';
//     }
//   }
// }

class DatabaseManagerImpl implements DatabaseManager {
  // late Box<dynamic> _box;

  // final FlutterSecureStorage _secureStorage;

  // DatabaseManagerImpl() : _secureStorage = const FlutterSecureStorage();

  @override
  void saveData(String key, dynamic value) {
    // _box.put(key, value);
  }

  @override
  dynamic getData(String key) {
    // return _box.get(key);
  }

  @override
  Future<void> deleteData(String key) async {
    // return _box.delete(key);
  }

  @override
  Future<void> saveSecureData(SecureStorageKey secureStorageKey, String value) async {
    try {
      // await _secureStorage.write(key: secureStorageKey.key, value: value);
    } catch (e) {
      throw Exception('Failed to save secure data: $e');
    }
  }

  @override
  Future<String?> getSecureData(SecureStorageKey secureStorageKey) async {
    try {
      return Future.value("");
      // return await _secureStorage.read(key: secureStorageKey.key);
    } catch (e) {
      throw Exception('Failed to retrieve secure data: $e');
    }
  }

  @override
  Future<void> deleteSecureData(SecureStorageKey secureStorageKey) async {
    try {
      // await _secureStorage.delete(key: secureStorageKey.key);
    } catch (e) {
      throw Exception('Failed to delete secure data: $e');
    }
  }

  @override
  Future<bool> isValueEmpty(SecureStorageKey secureStorageKey) async {
    try {
      // final value = await _secureStorage.read(key: secureStorageKey.key);
      // return value == null;
      return false;
    } catch (e) {
      throw Exception('Failed to retrieve secure data: $e');
    }
  }

  @override
  Future<void> openBox() {
    // TODO: implement openBox
    throw UnimplementedError();
  }
}

final databaseManagerProvider = Provider<DatabaseManager>((ref) {
  return DatabaseManagerImpl();
});
