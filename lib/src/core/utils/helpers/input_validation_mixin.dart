mixin InputValidationMixin {
  String? isPasswordValid(String password) {
    if (password.isEmpty) return "empty";
    if (password.length < 8) return "less then 8";
    if (!RegExp(r'[A-Z]').hasMatch(password)) return "must have capital letter";
    if (!RegExp(r'[a-z]').hasMatch(password)) return "must have small letter";
    if (!RegExp(r'[0-9]').hasMatch(password)) return "must have number";
    if (!RegExp(r'[!@#\$&*~]').hasMatch(password)) {
      return "must have special character";
    }
    return null;
  }

  bool isPhoneValid(String phone) {
    return RegExp(r'^[0-9۰-۹٠-٩]{6}').hasMatch(phone);
  }

  // bool isEmailValid(String email) {
  //   if (email.isEmpty) {
  //     return false;
  //   }
  //   var pattern = r'^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
  //   RegExp regex = RegExp(pattern);
  //   return regex.hasMatch(email);
  // }

  bool isEmailValid(String email) {
    final emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email.trim());
  }

  bool isSimpleEmailValid(String email) {
    if (email.toLowerCase().contains("@")) {
      return true;
    } else {
      return false;
    }
  }

  bool isDateValid(String text) {
    final regex =
        RegExp(r'^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(19|20)\d{2}');
    return regex.hasMatch(text);
  }
}
