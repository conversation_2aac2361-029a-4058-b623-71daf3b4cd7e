import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

/// A colored logger for Dio that extends PrettyDioLogger
///
/// This logger adds ANSI color codes to the output of PrettyDioLogger
/// to make it easier to read and distinguish different parts of the logs.
class ColoredDioLogger extends PrettyDioLogger {
  // ANSI color codes
  static const String _reset = '\x1B[0m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';
  // static const String _bold = '\x1B[1m';
  static const String _gray = '\x1B[90m';
  static const String _brightBlue = '\x1B[94m';
  // static const String _brightGreen = '\x1B[92m';
  static const String _brightYellow = '\x1B[93m';
  static const String _brightMagenta = '\x1B[95m';

  /// Constructor that passes parameters to the parent class
  ColoredDioLogger({
    super.request,
    super.requestHeader = true,
    super.requestBody = true,
    super.responseHeader,
    super.responseBody,
    super.error,
    super.maxWidth,
    super.compact,
    super.enabled,
    super.filter,
  }) : super(
         logPrint: (Object object) {
           if (kDebugMode) {
             final String text = object.toString();

             // Apply colors based on content
             if (text.startsWith('╔╣ Request')) {
               debugPrint('$_cyan$text$_reset');
             } else if (text.startsWith('╔╣ Response')) {
               debugPrint('$_green$text$_reset');
             } else if (text.contains('DioError') || text.contains('Error')) {
               debugPrint('$_red$text$_reset');
             } else if (text.startsWith('╔ Body') || text.startsWith('╔ Headers') || text.startsWith('╔ Query Parameters')) {
               debugPrint('$_yellow$text$_reset');
             } else if (text.startsWith('╟')) {
               // Key-value pairs
               debugPrint('$_magenta$text$_reset');
             } else if (text.contains('═')) {
               // Lines
               debugPrint('$_white$text$_reset');
             } else if (text.startsWith('║')) {
               // Check if this line contains JSON content
               if (_containsJson(text)) {
                 // This is likely JSON content - let's style it
                 _printStyledJson(text);
               } else {
                 // Regular line
                 debugPrint('$_white$text$_reset');
               }
             } else {
               // Default color
               debugPrint('$_white$text$_reset');
             }
           }
         },
       );

  /// Prints a JSON string with syntax highlighting
  static void _printStyledJson(String text) {
    // Extract the JSON part from the line
    final jsonStartIndex = text.indexOf('{');
    final jsonEndIndex = text.lastIndexOf('}') + 1;
    final jsonArrayStartIndex = text.indexOf('[');
    final jsonArrayEndIndex = text.lastIndexOf(']') + 1;

    String prefix = '';
    String jsonStr = '';
    String suffix = '';

    if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
      // Object JSON
      prefix = text.substring(0, jsonStartIndex);
      jsonStr = text.substring(jsonStartIndex, jsonEndIndex);
      suffix = text.substring(jsonEndIndex);
    } else if (jsonArrayStartIndex >= 0 && jsonArrayEndIndex > jsonArrayStartIndex) {
      // Array JSON
      prefix = text.substring(0, jsonArrayStartIndex);
      jsonStr = text.substring(jsonArrayStartIndex, jsonArrayEndIndex);
      suffix = text.substring(jsonArrayEndIndex);
    } else {
      // Not valid JSON, print as is
      debugPrint('$_white$text$_reset');
      return;
    }

    try {
      // Try to parse and pretty print the JSON
      final dynamic jsonObj = json.decode(jsonStr);
      final prettyJson = _formatJson(jsonObj, 0);

      // Print the prefix (usually the ║ character)
      debugPrint('$_white$prefix$_reset');

      // Print each line of the formatted JSON with proper indentation and prefix
      final lines = prettyJson.split('\n');
      for (final line in lines) {
        if (line.isNotEmpty) {
          debugPrint('$_white$prefix$line$_reset');
        }
      }

      // Print suffix if any
      if (suffix.isNotEmpty) {
        debugPrint('$_white$prefix$suffix$_reset');
      }
    } catch (e) {
      // If JSON parsing fails, print as is
      debugPrint('$_white$text$_reset');
    }
  }

  /// Formats a JSON object with syntax highlighting
  static String _formatJson(dynamic json, int indent) {
    final indentStr = '  ' * indent;
    final nextIndent = '  ' * (indent + 1);

    if (json is Map) {
      if (json.isEmpty) return '$_gray{}$_reset';

      final buffer = StringBuffer();
      buffer.write('$_gray{$_reset\n');

      var i = 0;
      json.forEach((key, value) {
        if (i > 0) buffer.write(',\n');
        buffer.write('$nextIndent$_brightYellow"$key"$_reset: ');
        buffer.write(_formatJsonValue(value, indent + 1));
        i++;
      });

      buffer.write('\n$indentStr$_gray}$_reset');
      return buffer.toString();
    } else if (json is List) {
      if (json.isEmpty) return '$_gray[]$_reset';

      final buffer = StringBuffer();
      buffer.write('$_gray[$_reset\n');

      for (var i = 0; i < json.length; i++) {
        if (i > 0) buffer.write(',\n');
        buffer.write('$nextIndent${_formatJsonValue(json[i], indent + 1)}');
      }

      buffer.write('\n$indentStr$_gray]$_reset');
      return buffer.toString();
    } else {
      return _formatJsonValue(json, indent);
    }
  }

  /// Formats a JSON value with appropriate color
  static String _formatJsonValue(dynamic value, int indent) {
    if (value == null) {
      return '$_blue$value$_reset';
    } else if (value is String) {
      return '$_green"$value"$_reset';
    } else if (value is num) {
      return '$_brightMagenta$value$_reset';
    } else if (value is bool) {
      return '$_brightBlue$value$_reset';
    } else if (value is Map || value is List) {
      return _formatJson(value, indent);
    } else {
      return value.toString();
    }
  }

  /// Checks if a string contains valid JSON
  static bool _containsJson(String text) {
    // Check for JSON object or array patterns
    bool hasJsonSyntax = (text.contains('{') && text.contains('}')) || (text.contains('[') && text.contains(']'));

    if (!hasJsonSyntax) return false;

    // Try to extract and parse potential JSON
    try {
      // Look for JSON object
      if (text.contains('{') && text.contains('}')) {
        final start = text.indexOf('{');
        final end = text.lastIndexOf('}') + 1;
        if (start < end) {
          final jsonStr = text.substring(start, end);
          // Try to parse it
          json.decode(jsonStr);
          return true;
        }
      }

      // Look for JSON array
      if (text.contains('[') && text.contains(']')) {
        final start = text.indexOf('[');
        final end = text.lastIndexOf(']') + 1;
        if (start < end) {
          final jsonStr = text.substring(start, end);
          // Try to parse it
          json.decode(jsonStr);
          return true;
        }
      }

      return false;
    } catch (e) {
      // Not valid JSON
      return false;
    }
  }
}
