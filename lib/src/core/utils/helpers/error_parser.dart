import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../managers/firebase_services_engine/services_engine.dart';
import '../../../injection.dart';

import '../../common/data/models/error_model/error_model.dart';

ErrorModel errorParser(error, stackTrace) {
  inspect(error);

  if (error is DioException) {
    if (error.type == DioExceptionType.connectionError || error.type == DioExceptionType.connectionTimeout) {
      final Error noInternetError = Error(message: "no_internet_connection".tr());
      final ErrorModel noInternetErrorData = ErrorModel(error: noInternetError);
      return noInternetErrorData;
    }

    final int statusCode = error.response?.statusCode ?? 0;
    if (statusCode == 500) {
      final Error localError = Error(message: "something_went_wrong".tr());
      final ErrorModel localErrorData = ErrorModel(error: localError);
      return localErrorData;
    } else if (statusCode == 429) {
      final Error localError = Error(message: "too_many_request".tr());
      final ErrorModel localErrorData = ErrorModel(error: localError);
      return localErrorData;
    } else {
      serviceLocator<ServicesEngine>().logErrorFbCrashAnalytics(error.message ?? "");
      serviceLocator<ServicesEngine>().recordErrorFbCrashAnalytics(error.message ?? "", stackTrace);
      try {
        // Parse the response data
        final dynamic responseData = error.response?.data;
        final Map<String, dynamic> errorResponse;

        if (responseData is String) {
          errorResponse = json.decode(responseData) as Map<String, dynamic>;
          log(errorResponse.toString());
        } else if (responseData is Map<String, dynamic>) {
          errorResponse = responseData;
        } else {
          errorResponse = {};
        }

        // Check if the response follows the new format with success, message, and data fields
        if (errorResponse.containsKey('success') && errorResponse.containsKey('message') && errorResponse.containsKey('data')) {
          // Handle the new error format
          // We don't need to check success as we're already in the error handler
          final String message = errorResponse['message'] as String? ?? '';
          final Map<String, dynamic> data = errorResponse['data'] is Map<String, dynamic> ? errorResponse['data'] as Map<String, dynamic> : {};

          final String errorMessage = data.containsKey('error') ? data['error'] as String? ?? message : message;

          return ErrorModel(
            error: Error(
              code: '$statusCode',
              message: errorMessage,
              originalError: message,
            ),
          );
        } else {
          // Handle the original error format
          final ErrorModel backendErrorData = ErrorModel.fromJson(errorResponse);
          // Create a new Error object with the updated code
          if (backendErrorData.error != null) {
            final updatedError = backendErrorData.error!.copyWith(code: '$statusCode');
            return backendErrorData.copyWith(error: updatedError);
          }
          return backendErrorData;
        }
      } catch (error) {
        debugPrint(error.toString());
        final Error localError = Error(message: "error_communication".tr());
        final ErrorModel localErrorData = ErrorModel(error: localError);

        return localErrorData;
      }
    }
  } else {
    final Error localError = Error(message: "something_went_wrong".tr());
    final ErrorModel localErrorData = ErrorModel(error: localError);
    return localErrorData;
  }
}
