import 'package:url_launcher/url_launcher.dart';

class UrlLauncher {
  UrlLauncher._internal();

  static final UrlLauncher _instance = UrlLauncher._internal();
  static UrlLauncher get instance => _instance;

  /// LAUNCH URL
  Future<void> launchURL(String url) async {
    try {
      final uri = Uri.parse(url);
      final canLaunch = await canLaunchUrl(uri);
      if (canLaunch) {
        await launchUrl(
          uri,
          mode: LaunchMode.inAppBrowserView,
        );
      }
    } catch (e) {
      // removedLog if (kDebugMode) log(e.toString());

      if (e is FormatException) {
        // removedLog if (kDebugMode) log("Invalid URL format: $url");
      } else {
        // removedLog if (kDebugMode) log("Error launching URL: $e");
      }
    }
  }
}
