import 'dart:io';

import 'package:flutter/foundation.dart';
// import 'package:flutter_hms_gms_availability/flutter_hms_gms_availability.dart';  // Temporarily disabled

enum GmsAndHmsCheckerStatus { gms, hms, other }

class GmsAndHmsChecker {
  Future<GmsAndHmsCheckerStatus> check() async {
    if (kIsWeb) {
      return GmsAndHmsCheckerStatus.other;
    }

    if (Platform.isIOS) {
      return GmsAndHmsCheckerStatus.other;
    }

    // Temporarily return GMS as default for Android devices
    return GmsAndHmsCheckerStatus.gms;

    // final bool isGmsAvailable = await FlutterHmsGmsAvailability.isGmsAvailable;
    // if (isGmsAvailable) {
    //   return GmsAndHmsCheckerStatus.gms;
    // }
    // return GmsAndHmsCheckerStatus.hms;
  }
}
