import 'package:flutter/material.dart';

double screenWidth(BuildContext context) => MediaQuery.sizeOf(context).width;

double screenHeight(BuildContext context) => MediaQuery.sizeOf(context).height;

double bottomNotchHeight(BuildContext context) =>
    MediaQuery.of(context).padding.bottom;
bool isMobile() {
  final data = MediaQueryData.fromView(
      WidgetsBinding.instance.platformDispatcher.views.first);
  return data.size.shortestSide < 600;
}

///APP PADDING
EdgeInsetsGeometry sidePadding = getSidePadding();

EdgeInsetsGeometry getSidePadding() {
  if (isMobile()) {
    return const EdgeInsets.symmetric(horizontal: 16);
  } else {
    return const EdgeInsets.symmetric(horizontal: 62);
  }
}
