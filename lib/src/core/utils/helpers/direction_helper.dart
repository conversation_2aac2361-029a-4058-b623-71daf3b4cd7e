import 'dart:developer';

import 'package:flutter/material.dart';

/// Provides utility functions for handling text direction based on locale
class DirectionHelper {
  DirectionHelper._();

  static const List<String> _rtlLanguages = ['ar', 'ku'];

  static TextDirection getDirectionFromLocale(Locale locale) {
    log("locale.languageCode: ${locale.languageCode}");
    return _rtlLanguages.contains(locale.languageCode) ? TextDirection.rtl : TextDirection.ltr;
  }

  static bool isRtl(Locale locale) {
    return _rtlLanguages.contains(locale.languageCode);
  }
}
