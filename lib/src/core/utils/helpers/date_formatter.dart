import 'package:intl/intl.dart';

String formatDate(DateTime? expiration) {
  var formatter = DateFormat('dd/MM/yyyy', 'en');
  return formatter.format(expiration ?? DateTime.now());
}

String worldCupDateFormatter(DateTime? expiration) {
  var formatter = DateFormat('dd/mm/yyyy', 'en');
  return formatter.format(expiration ?? DateTime.now());
}

String formatTimeFromString(String date) {
  final DateFormat timeFormatter = DateFormat("yyyy-mm-dd hh:mm", 'en');
  DateTime timeFormattedMatchGame = timeFormatter.parse(date);
  var formatter = DateFormat('jm', 'en');
  return formatter.format(timeFormattedMatchGame);
}

DateTime? parseDateTime(String? dateString) {
  if (dateString == null || dateString.isEmpty) return null;
  try {
    return DateTime.parse(dateString);
  } catch (e) {
    return null;
  }
}
