/// Enum representing different types of login methods.
///
/// This enum includes various login options such as email, phone, and social media platforms.
enum LoginType {
  /// Login using email.
  email,

  /// Login using phone number.
  phone,

  /// Login using Google account.
  google,

  /// Login using Apple account.
  apple,
}

extension LoginTypeExtension on LoginType {
  String get value => name;
}

extension StringLoginTypeExtension on String {
  LoginType toLoginType() => LoginType.values.firstWhere((e) => e.value == this);
}
