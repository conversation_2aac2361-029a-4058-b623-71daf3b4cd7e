enum ImageType {
  png,
  jpeg,
  jpg,
  webp,
  gif,
  // svg,
  none
}

extension ImageTypeExtension on String {
  ImageType get getImageType {
    switch (this) {
      case '.png':
        return ImageType.png;
      case '.jpeg':
        return ImageType.jpeg;
      case '.jpg':
        return ImageType.jpg;
      case '.webp':
        return ImageType.webp;
      case '.gif':
        return ImageType.gif;
      // case '.svg':
      //   return ImageType.svg;
      default:
        return ImageType.none;
    }
  }
}
