import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

import '/src/app/logic/app_settings.dart';
import '/src/core/utils/helpers/gms_hms_checker.dart';
import '/src/core/utils/managers/analytics/firebase_analytics_manager.dart';
import '/src/core/utils/managers/analytics/firebase_crash_analytic_manager.dart';
import '/src/core/utils/managers/database/database_manager.dart';
import '/src/core/utils/managers/firebase_services_engine/services_engine.dart';
import '/src/core/utils/managers/http/check_endpoint_reachability.dart';
import '/src/core/utils/managers/http/http_manager.dart';
import '/src/core/utils/managers/notification/gms_notification_manager.dart';
import '/src/core/utils/managers/notification/notification_message_handler.dart';
import 'features/common/splash/data/datasources/local/first_time_launched_data_source.dart';
import 'features/common/splash/data/datasources/remote/app_status_data_source.dart';
import 'features/common/splash/data/repositories/app_status_repository_impl.dart';
import 'features/common/splash/data/repositories/check_first_launch_repository_impl.dart';
import 'features/common/splash/domain/repositories/app_status_repository.dart';
import 'features/common/splash/domain/usecases/check_first_launch.dart';

import 'features/common/splash/domain/repositories/check_first_launch_repository.dart';
import 'features/common/splash/domain/usecases/get_app_status.dart';

final serviceLocator = GetIt.instance;

Future<void> init() async {
  initInjections(serviceLocator);
  serviceLocator.allowReassignment = true;
}

void initInjections(GetIt serviceLocator) {
  // Utils

  serviceLocator.registerLazySingleton<CheckEndpointReachability>(() => CheckEndpointReachabilityImpl());

  serviceLocator.registerLazySingleton<AppSettings>(() => AppSettings(databaseManager: serviceLocator()));

  //* permission manager

  serviceLocator.registerFactory<GmsAndHmsChecker>(() => GmsAndHmsChecker());

  //! App

  //* Logic

  //* Router
  // serviceLocator.registerLazySingleton<AppRouter>(() => AppRouter());

  //! core

  //* Database
  serviceLocator.registerLazySingleton<DatabaseManager>(() => DatabaseManagerImpl());

  //* Security
  // serviceLocator.registerLazySingleton<AESEncryptionManager>(() => AESEncryptionManagerImpl());

  // RecaptchaService removed - using WebView implementation instead

  //* Network
  serviceLocator.registerLazySingleton<BaseOptions>(() => BaseOptions(headers: {'Content-Type': 'application/json', 'Accept': 'application/json', "charset": "utf-8", "Accept-Charset": "utf-8"}, responseType: ResponseType.plain, receiveDataWhenStatusError: true, connectTimeout: const Duration(seconds: 15), receiveTimeout: const Duration(seconds: 15), sendTimeout: const Duration(seconds: 15)));

  serviceLocator.registerLazySingleton<HttpManager>(() => HttpManagerImpl(baseOptions: serviceLocator(), databaseManager: serviceLocator(), appSettings: serviceLocator()));

  //* Analytics service
  serviceLocator.registerLazySingleton<FirebaseAnalyticsManager>(() => FirebaseAnalyticsManagerImpl());
  serviceLocator.registerLazySingleton<FirebaseCrashAnalyticManager>(() => FirebaseCrashAnalyticManagerImpl());

  //* Push notification
  serviceLocator.registerLazySingleton<NotificationMessageHandler>(() => NotificationMessageHandlerImpl());

  serviceLocator.registerLazySingleton<GmsNotificationsManager>(() => GmsNotificationsManager(notificationMessageHandler: serviceLocator()));

  serviceLocator.registerLazySingleton<ServicesEngine>(() => ServicesEngineImpl(gmsAndHmsChecker: serviceLocator(), httpManager: serviceLocator(), firebaseAnalyticsManager: serviceLocator(), firebaseCrashAnalyticManager: serviceLocator()));

  //* data sources
  serviceLocator.registerFactory<AppStatusDataSource>(() => AppStatusDataSourceImpl(httpManager: serviceLocator()));

  serviceLocator.registerFactory<FirstTimeAppLaunchedDataSource>(() => FirstTimeAppLaunchedDataSourceImpl(databaseManager: serviceLocator()));

  //* repositories
  serviceLocator.registerFactory<AppStatusRepository>(() => AppStatusRepositoryImpl(appStatusDataSource: serviceLocator(), gmsAndHmsChecker: serviceLocator()));

  serviceLocator.registerFactory<CheckFirstLaunchRepository>(() => CheckFirstLaunchRepositoryImpl(firstTimeAppLaunchedDataSource: serviceLocator()));

  //* use cases
  serviceLocator.registerFactory<GetAppStatus>(() => GetAppStatus(repository: serviceLocator()));
  serviceLocator.registerFactory<CheckFirstLaunch>(() => CheckFirstLaunch(repository: serviceLocator()));
}
