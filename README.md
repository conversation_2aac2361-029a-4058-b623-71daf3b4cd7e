# Kurdsat Application

## Generating Features with Mason CLI

This project uses [<PERSON>](https://pub.dev/packages/mason_cli) to generate feature boilerplate code. To generate a new feature, use the following command:

```
mason make feature_boilerplate --feature_name your_feature_name
```


Make sure you have <PERSON> installed and the required bricks set up. For more information, see the [Mason documentation](https://pub.dev/packages/mason_cli).

# re create project structure
    flutter create --project-name app --org tv.kurdsat .
