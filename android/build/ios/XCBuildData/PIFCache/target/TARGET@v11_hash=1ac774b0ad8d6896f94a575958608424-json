{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98047ae0b0dfa0bf07b43fbb8c33be797a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2306391936207ff3a77bf532265ba12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980bedb8662e481ed785382cd3f6926b54", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98487ed0955f6ee94f3b149ed57f321128", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980bedb8662e481ed785382cd3f6926b54", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f8dc2559c8635ba7bcf52e3f977ac5f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e0f4bc97db6519f655baea4d7dcf35d", "guid": "bfdfe7dc352907fc980b868725387e98e2bd21820c90b1f41815129bf4bf9405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2ea16754542e357b5c0d3b8bfe74c7", "guid": "bfdfe7dc352907fc980b868725387e986c17ed9ca53d14296e8c0881f6afcb5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986872b63f04537dcd6a45671ce45b0557", "guid": "bfdfe7dc352907fc980b868725387e98694a813cd054c22c482e4599329ff844", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec17d9dd66ad069137e330b1f9fc72b7", "guid": "bfdfe7dc352907fc980b868725387e985b3a8c4e58a7df6bd23638c5a4ca721d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c8c3b91eb640a6051faf40192beb02", "guid": "bfdfe7dc352907fc980b868725387e982585d2560ae64ea2b0ac16f50295bd34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c788484def359fadbe2b59a2a7e2c68f", "guid": "bfdfe7dc352907fc980b868725387e982ae6295bcac026ff9845dd0c5ab9542a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881cd8e8a0cb3d4ffc395c81422c67139", "guid": "bfdfe7dc352907fc980b868725387e98ae96daec96ffbfc25a026c67ba591604", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e82eb6abe9cbf45ba9491af704f535", "guid": "bfdfe7dc352907fc980b868725387e982073dd99eb233240c8ba8f146f65e712", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503fbdf22fcaa70002bc25eef64f644c", "guid": "bfdfe7dc352907fc980b868725387e98167f03da8c3de9caa0f3bf63ff28a24b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5c91a58737f687f8ea6f6ab5edfc97", "guid": "bfdfe7dc352907fc980b868725387e98ff8bbdaff575c2d33fc4b4a877fb12e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f9790182b58ad6904b345267fb0382", "guid": "bfdfe7dc352907fc980b868725387e983070cc21ebc803eab062c67de7dff795", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799d55db8bd4414d4dc46536788f04a3", "guid": "bfdfe7dc352907fc980b868725387e98178f798afeb40b54ea8662c68ddfeb10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854560f65ca172914b299691090a4a7be", "guid": "bfdfe7dc352907fc980b868725387e9816a68341387b4a6baf52d3ae5ff5bea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5d01218ae741f5f08c27352eace5ad", "guid": "bfdfe7dc352907fc980b868725387e98814d833ece647414ba02ec6ef4696976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f8b248bf0a7ceee23b0a9b91148c79", "guid": "bfdfe7dc352907fc980b868725387e982f1b6d6c3e214bcf71d983f14c3d74bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989daa79ba156b851ae14af46906b3a0a9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e65ec77530e654e6e8558bec1c03c3de", "guid": "bfdfe7dc352907fc980b868725387e980f17e817e499929885dee4313deb21d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bbd66dd795d57845f06f6bfcf07783a", "guid": "bfdfe7dc352907fc980b868725387e98951d9613b201b8f7eddb290425ee8e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053deb63c06c4d12c575412841c1a9b1", "guid": "bfdfe7dc352907fc980b868725387e982fa6d7fed276a67d593a8153d22c33db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45aca0122d71c15895d7b34c62e66c8", "guid": "bfdfe7dc352907fc980b868725387e982428b98c63c39fa7782792d997812199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694d61a716a2f3dfbbffccf0ae069fcd", "guid": "bfdfe7dc352907fc980b868725387e98e93c0029f238365dee57e1a90c3e6a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9dac3e11c5254c7b0334a463a3755da", "guid": "bfdfe7dc352907fc980b868725387e986d40de42c560db1b8fa41d0761cd7dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897265bc3cf109d62c8284293cc69a4d4", "guid": "bfdfe7dc352907fc980b868725387e983f779cf18eadc28e77c18d3ea3c8cdbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e916bffe5f92d0398da2da7e7e2e91a", "guid": "bfdfe7dc352907fc980b868725387e984d2884e377032fd71cd7e3939400ac20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea60bc0e707decf6cdbdf0433d4a0cbf", "guid": "bfdfe7dc352907fc980b868725387e98e1955fc3138908e466c830c3aaf88c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988665355c9488381660dcf2c943ce6d3c", "guid": "bfdfe7dc352907fc980b868725387e98183e12f37254189a854de2e26e5ef3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98743a002ea6034cababacf66e4ee90db7", "guid": "bfdfe7dc352907fc980b868725387e98c67f7ccd4f0daa66adcf204f9f79f507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b957a719133ea08184ed941222473d13", "guid": "bfdfe7dc352907fc980b868725387e984a1a2774f6b2777dc237bb18ccd2b00f"}], "guid": "bfdfe7dc352907fc980b868725387e98badc07441c3d3f800ea824e69bb5beff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98e3f6bfa9b742e772ecd92d7503f55e33"}], "guid": "bfdfe7dc352907fc980b868725387e98d7f82e88a905b7e353a4e6f9718eb7f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cc8ac38168987ce84f85d8beb6728369", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98558074b4aea693c85351e88b3b5ba2ae", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}