{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983cc4fdd344afab9d5e9ebdf046076280", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d850e90f7e310da46d36c7708a225ffe", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6ba3dc1a38a96341123fba81d452f98", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984adcf997c8012ef41c541c97077d2a90", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6ba3dc1a38a96341123fba81d452f98", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885b9783f6cdfcc4b750417f3ee6a38e6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98783046be0d0212017829aed58bde7d4b", "guid": "bfdfe7dc352907fc980b868725387e98b220cfadff356562d91e69efe4c13c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df79e0f7153a358d390be67030ccd3ab", "guid": "bfdfe7dc352907fc980b868725387e98afe04dd2ec9bbbffb7ed6afa44d02e93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c4325c25d01bd0c18be4e8166c44d6", "guid": "bfdfe7dc352907fc980b868725387e98bed6e7c6fc4df1c2bd3b0f84e1ba2cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d1fbf164b65fe6ed720bf67be3f8b9", "guid": "bfdfe7dc352907fc980b868725387e985730bae5dc66f3c5d15126ed035cc154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868666a8db999eca7ea69fc5f27c19964", "guid": "bfdfe7dc352907fc980b868725387e98a2fc66c3e13206c56efc01032233bdd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987645e3e5fb99e860178cd5cae181f888", "guid": "bfdfe7dc352907fc980b868725387e98992fba17ce56548c707e8e10bc2807fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984022cce362ab146ad57e8dae5ebf291a", "guid": "bfdfe7dc352907fc980b868725387e989c3f15751ff02b5a38f5ebb9f0cc6a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f59b0b0aad99ddc1e086ae2b99d0d27e", "guid": "bfdfe7dc352907fc980b868725387e98c051ed5b28595874077b75d6329557c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e5b7bb2bbcac6c7f888b4007917e79", "guid": "bfdfe7dc352907fc980b868725387e98e72c5914dcf62d4ec45688dc235c4fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8703d4e4ad5c114e1a37425926d4333", "guid": "bfdfe7dc352907fc980b868725387e98d5310b8c3a2dbdd2c4d37350bce634ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833257b9b45f2b757d76f5cbe8ec43a69", "guid": "bfdfe7dc352907fc980b868725387e98e96fefa83360354fc37405c72625dade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b5a374c9c66d63ee1126c5139fae97", "guid": "bfdfe7dc352907fc980b868725387e98a92953dae974539c93924f415c27df9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d671d3b22ece002ac79a357114b3a2b1", "guid": "bfdfe7dc352907fc980b868725387e982f94f9ea36aa9ac56f47c9c45ce9de7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a1faee65422be3c46354ae740f9d777", "guid": "bfdfe7dc352907fc980b868725387e989cfd22c89dee4e29fbdd91e5fdccecd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b945e16873a992d92e61ae70f83dda11", "guid": "bfdfe7dc352907fc980b868725387e98e8744efaba48bf9ddb54793976ddd7c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c3fc9da1c0ad80ec4b706c08c7d3794", "guid": "bfdfe7dc352907fc980b868725387e98a876cfcafedc2958298fe07b528fa5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d81296eac30da1f24becd2b7d81bc97", "guid": "bfdfe7dc352907fc980b868725387e98b3279ccc4324233be955fa0be430a7f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4b10ae31166c56217fce18ec6bd4de3", "guid": "bfdfe7dc352907fc980b868725387e9801c26a8367d94236e632e1abe399d23d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827fde649d5588ff381625d77cd74d640", "guid": "bfdfe7dc352907fc980b868725387e98fa2657433d2fe1a913c9f4ddbc6a2742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fb564c5e1de75a64170372aa478391", "guid": "bfdfe7dc352907fc980b868725387e98e8d46ab0dadd67b806631627bee6af24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c85e0993d7acd3cb9eda54aadad9a87", "guid": "bfdfe7dc352907fc980b868725387e984455fa01a2437c196ef2603b79886fde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825738ff6345f20c6017647d69606cbab", "guid": "bfdfe7dc352907fc980b868725387e980199eea4a468cb5bf91b976b9d8f3c84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2f7636b46321f38c38a47e35f16352", "guid": "bfdfe7dc352907fc980b868725387e9800941f569a3cf2275d9a7a1681be938d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989947dd8d9de51788e98dc2d5a9347b47", "guid": "bfdfe7dc352907fc980b868725387e98a8c58bbab168741d752c8da1f95bc6f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554b4aa6643922101c46b0939bf67713", "guid": "bfdfe7dc352907fc980b868725387e98c7d31a13977cf79698f2f711e7986cb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc135239bbccffe78e72b7ed726dea5d", "guid": "bfdfe7dc352907fc980b868725387e9841bc22b25e74ccf6735cb6c73dc296e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abbbef4b7f6af291dd7b6e19a463c631", "guid": "bfdfe7dc352907fc980b868725387e9837842366c2f1e0632c49d8efde966911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989944d269a3315a5c859912fc4b6364ba", "guid": "bfdfe7dc352907fc980b868725387e98c5e8ea040d10c4d1841242c75fc23fe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989081a6fe22b1815d09d97ddef60e6c70", "guid": "bfdfe7dc352907fc980b868725387e983afd464937a61bf1791d63e0496a16b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b04eda430d2982877e0bdb27353b905d", "guid": "bfdfe7dc352907fc980b868725387e98e5c73611e628b6bf4615fd40de952d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ce893294ea18d3b427326c08520aed", "guid": "bfdfe7dc352907fc980b868725387e984855433411ac43b33c7d3f5162d4cc8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d709d87efb7eac4759c4feb528aa9d59", "guid": "bfdfe7dc352907fc980b868725387e98661d12c8050e3621f6954313c09fa107", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b2746a8e64bb58daf5d756b82a9561", "guid": "bfdfe7dc352907fc980b868725387e98f1b1e348fca1c5de01667b85750315ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98868ae79b7fe5ac684cf531b8bfa705da", "guid": "bfdfe7dc352907fc980b868725387e9831c6f5a27f4bd9d4fb5de15e15a418c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efb71ad9c568b716458a7eafffe261ae", "guid": "bfdfe7dc352907fc980b868725387e9849e78266696286db657d0c4c2e3fae3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19f46fb5984b647192b1b38c7627e67", "guid": "bfdfe7dc352907fc980b868725387e982559194267729b067793c995b4352358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98381facab423b7fcad7ee327a7680a90f", "guid": "bfdfe7dc352907fc980b868725387e98824c561358b23a97c9358fad8bfed79e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b5fc67c94dd8ace4ad8eaeec9e24a3", "guid": "bfdfe7dc352907fc980b868725387e98cb05ca65b07d87770f52eb1267547fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf019925280ee42b2265d73b076cfa87", "guid": "bfdfe7dc352907fc980b868725387e98b47abe4fbaaa3bc7aa576acd3349565a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986e450fb2dc57478536a407bff741d5", "guid": "bfdfe7dc352907fc980b868725387e98e6216bc773e1a76b6383eed184a754e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe6ba26b30f5957b8841504b18970c6b", "guid": "bfdfe7dc352907fc980b868725387e985fdb7a4665f4d527298cd69c340ac0a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3efaaafff3eb904d8890319e8bbb919", "guid": "bfdfe7dc352907fc980b868725387e9844305d21766ca265d906c6c76893b719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828632e244b3dbf0d7720985f640dd106", "guid": "bfdfe7dc352907fc980b868725387e9842baaad545f110f9e933a214e2c05416"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854fcf1360554147c0b4477a6ba3ab328", "guid": "bfdfe7dc352907fc980b868725387e980fb5bd6891b35cd58bcd4ba43eb39f3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857c1e921b333eedd88ab5e871a7a55fc", "guid": "bfdfe7dc352907fc980b868725387e98b8d808cd818e763dce642395abd5fe81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885dc6ad7a40c2b2c17ef8019c98167b8", "guid": "bfdfe7dc352907fc980b868725387e98aec2a791c570157edbee53f2670b9048"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eacf6f271734b341b5c3857b81f4366", "guid": "bfdfe7dc352907fc980b868725387e98f3bd00de3803bd306c8e14fa527a7d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ba590e4e1ada42ee870b820e918a1c", "guid": "bfdfe7dc352907fc980b868725387e98952a373ec8e13f703d8d88ec99a3ee46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857714a3c7941aab055892be23ff240c6", "guid": "bfdfe7dc352907fc980b868725387e989b69ddf7b038ff22e8a60e162ee8c22a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5cba36ef19269626346e5901dcdb1f1", "guid": "bfdfe7dc352907fc980b868725387e988765c982ebe263d99e60c787374c8657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828778057ae180eb6372f228bbd326821", "guid": "bfdfe7dc352907fc980b868725387e98597de461406de6825289ec856b8b5012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818881ea3f160a2eada1cc726bf9e6f4e", "guid": "bfdfe7dc352907fc980b868725387e98d8531ebcb7573f764b568bd188013472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c42eb80831526ce66c03bd048abdf6", "guid": "bfdfe7dc352907fc980b868725387e98c9065c606bb4de14e6b46aa62a1186d4"}], "guid": "bfdfe7dc352907fc980b868725387e989c7de5c4a39284947e7693b4d53d99bc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8610622c34a5ff6d1df1ae4571dde6c", "guid": "bfdfe7dc352907fc980b868725387e98bd0d0252e1e0ec1346fcb0087f8acdfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a33170fccf3eaf43c684a6d845369e0d", "guid": "bfdfe7dc352907fc980b868725387e985df13f9a56a7d0a3b35ea4b59308cfc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5da3c12873ef269e328566fdcbd484", "guid": "bfdfe7dc352907fc980b868725387e98a130f29ba48ee46836cf50814b2f5c86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e66d6391e9b363bb6f0d3b25d4a189d", "guid": "bfdfe7dc352907fc980b868725387e983fe05c4ff03ba71044e5041c07f2debb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef28ae7235a152cc3f9f9be919b72999", "guid": "bfdfe7dc352907fc980b868725387e98ffa6a40ed8de1fccf0c9e0cf42a35282"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc278e7f8b130014ac47441ff163af65", "guid": "bfdfe7dc352907fc980b868725387e9874a62578a71618a9a9578ad190c20599"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6bf9d23fbccd28b45604823f9e0e38d", "guid": "bfdfe7dc352907fc980b868725387e98e181f53fad6086299101abe1ee303975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e4e53b5925d5bcfb663d2bd80cd0546", "guid": "bfdfe7dc352907fc980b868725387e983b80bb9edd67a5c977d5391e42b11cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98662f0cb6f454d7de91cbd88dcfeace5b", "guid": "bfdfe7dc352907fc980b868725387e98ad6bc2fd12e466f72d472cf3858bf8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f8cb02cc9cc1a7835fb1ba7023df9d1", "guid": "bfdfe7dc352907fc980b868725387e983be54e4a2ddce3ae5325ce49f262d7a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889cb594a12338435a1ea265984163b20", "guid": "bfdfe7dc352907fc980b868725387e9896c468fe67095eaacc4fbc7ff7ef6403"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd1cf5f6baad21b8470595d5ab5098b1", "guid": "bfdfe7dc352907fc980b868725387e983a921c839afbaaa19afe3215cd74032e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f8371bab65acfe83ac6b0e350b87dc", "guid": "bfdfe7dc352907fc980b868725387e98ab6e238ab1c4703dddaa9721dbddab8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e097dabcb7786a654dfea11a4ff0c64", "guid": "bfdfe7dc352907fc980b868725387e982b21825a482d6ec39e7e9839aa7bdb65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0bbe9e4e57334e6e8872661a582c8d1", "guid": "bfdfe7dc352907fc980b868725387e9865bbf049adb698eda948e057c25d755c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ca9aac5f197081cfca06bb3aee8fa13", "guid": "bfdfe7dc352907fc980b868725387e982bea07ea957993c6b728ebf041b2bdca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ef1e93917d4308f9b134341b909055", "guid": "bfdfe7dc352907fc980b868725387e987c699e57dd6806c66dc8de686b04bf01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7939b928da2f4cc1f5c103a0d8541aa", "guid": "bfdfe7dc352907fc980b868725387e9857d46b65e11df4ee5e69408d789cf8aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0f07d6fa2a584bb970b6bc923d73e66", "guid": "bfdfe7dc352907fc980b868725387e9854e5eca1b94aa33f1b4a55e359716f35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860667e3ce3a50d7c02b914c1f9295db4", "guid": "bfdfe7dc352907fc980b868725387e987461a427cbd57b3ad389944c9e8cf48f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ac67095ee9f3c3fe6b4681858b6531", "guid": "bfdfe7dc352907fc980b868725387e985e61348b5e3508d701073aab4b5da9d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa96a72399e84feaa0901d9cacc84cb", "guid": "bfdfe7dc352907fc980b868725387e986327f084798fbce0244db019ce1136b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862c39aa293f8e4e7a8015f2ef881cc1d", "guid": "bfdfe7dc352907fc980b868725387e9802e1ece3e794b115bb2e6fce602b220c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f3f37199e5919a2c0f38ab9f703505d", "guid": "bfdfe7dc352907fc980b868725387e98545600426620d1fb027cead94cb9ca75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f71711bfcca7a2e1dafe123f87586607", "guid": "bfdfe7dc352907fc980b868725387e98907621eeb1b36a40e1a788519e8e695f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9e6bcce8087444e65195881ff1a2a9", "guid": "bfdfe7dc352907fc980b868725387e98825d0924f4a9ddd8fe2d6e4817de3aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25af638122eed33ce7caf1cb4e3f61d", "guid": "bfdfe7dc352907fc980b868725387e989234cac5fab083d3dd6fa764d0b535f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd04d927117ac3194abbdcb2db84aa04", "guid": "bfdfe7dc352907fc980b868725387e98ecff687f3bf9a307e37cc8d7ee9754dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983a4165a9914cfc5c513afbe994d175", "guid": "bfdfe7dc352907fc980b868725387e983ee04fb5532979fd9bf19b971cef71b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fca34a67765d3e600ce70d48322919b", "guid": "bfdfe7dc352907fc980b868725387e982bdbaaddf8fe5dae3276be7927f905e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65830eb0d7e54c6e16ae8b5b19cd6d6", "guid": "bfdfe7dc352907fc980b868725387e98ab88ad9cf7ef73b17db767320a0cd748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827edd5d55fc76586d49097449004977c", "guid": "bfdfe7dc352907fc980b868725387e98e79212826debf83e65ef5647e1f6258d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790c2c8e3b9518b8877d4d8dff3f3dee", "guid": "bfdfe7dc352907fc980b868725387e98d84ea49a7c1cd65c053805e7424a74ec"}], "guid": "bfdfe7dc352907fc980b868725387e98a8ca74b4306a713deadfea4d81b480e6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e981a775ca87ed8b848817bc50f93e6547f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e98719dbb91eec44fdcf2054557646629a3"}], "guid": "bfdfe7dc352907fc980b868725387e983f6d970c238675529dda42214d763e2e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98945875a8fd867efd7095f6ed91e8c627", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98c40cee59d41b6e44b2f41a3a65d14e62", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}