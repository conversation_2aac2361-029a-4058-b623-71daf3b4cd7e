{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851f386aef4b8f2f74f6ad33396ab48ec", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ff298dafa2868ff3f8b98ee0b2a8685", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8f1abf063a616057cbec78fd1629448", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8bd59e7e3f9f8baf7b93e5c36316ffc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8f1abf063a616057cbec78fd1629448", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc0fb5766e2cf81924d8b5a109abb9d0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fd65410574ea8d8fbb8b6e6fcc0eaa6c", "guid": "bfdfe7dc352907fc980b868725387e98cc7e843f29dc7cbf7261304d3e4b9be2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ae6a44f4994fbb3407987b0c54a878", "guid": "bfdfe7dc352907fc980b868725387e98ef3349b4cb1cbc28fef2f31dc3e08a3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a183ad16eeb7352f30b84c39861bd975", "guid": "bfdfe7dc352907fc980b868725387e98df4374eb107dfb52d2a25231f534b28a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e1a486992e10c9cbfa1650e1b17d0bb", "guid": "bfdfe7dc352907fc980b868725387e98e26ae66fda3d7adb23c811acf07b454a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862eb4273452b15ffd6d25deb1603fa20", "guid": "bfdfe7dc352907fc980b868725387e980d73dff470f8c6c1586f7fe36925c070", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245fe133d457c44d3029dfbbb8f6f1b9", "guid": "bfdfe7dc352907fc980b868725387e98510911395522e049fd59acd5d138e773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a4bf15f98e6758cd8441f9ba6855bd", "guid": "bfdfe7dc352907fc980b868725387e98e6f74894e2e77c1d96701161b0ac42e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d44e4c238f7d00fe48020e7de89d74d", "guid": "bfdfe7dc352907fc980b868725387e98d8bb2e75f65de71346496a4960a21d28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed029d659b0a7d7f69b5757299f477c1", "guid": "bfdfe7dc352907fc980b868725387e984043beaffaaa8c149c521dae2f5ef204", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9a5b1d584a6a0973bfe4a5fc9449b2a", "guid": "bfdfe7dc352907fc980b868725387e9802e650a67abea7d85d88a8209712d915", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98510d5964e12c09895f5a2b6bf45dbf3f", "guid": "bfdfe7dc352907fc980b868725387e982673adb6b2d1486bc798336f815b833e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98736d9b2232305e3f2d944b5429533c7a", "guid": "bfdfe7dc352907fc980b868725387e98e51e86df4b7354dbbbe51c470c070e2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98409f8525cce6f661b40dd4a39875192c", "guid": "bfdfe7dc352907fc980b868725387e988d5f59371fb3bdff75c8277fe2962683", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9c4791d1c7d3f8b65058afac3a6bd6e", "guid": "bfdfe7dc352907fc980b868725387e98b6bf5fdb4cdbb05a496f29123411810a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98254bfab266a4a8da2528cca6874a9e55", "guid": "bfdfe7dc352907fc980b868725387e98fd485ab6aaed6c960a5fbdc91cecf4c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834034f2ea05cb6e0ef03eed695c0bf2e", "guid": "bfdfe7dc352907fc980b868725387e986753a84c31372a9ff5d257ac5fa298be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e3dce6529f8d147fb2428f927be4aab", "guid": "bfdfe7dc352907fc980b868725387e9894dfcf71587c332e7226e899b359c184", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf072e298e81440f144e4545ac4006b", "guid": "bfdfe7dc352907fc980b868725387e98000480fae9e63917f17637ed4cb4a219", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1f7ddfc47c4f8f69cff05226f71f77", "guid": "bfdfe7dc352907fc980b868725387e983dbc51ceb9af8b9974cec5da4fdbafaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7aeb7f22132c622eb6bdf0e77e09376", "guid": "bfdfe7dc352907fc980b868725387e98992d659b3c4c4ccec31224ebeaede796", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0e27196ccc0211ce05e8f9a7baf318", "guid": "bfdfe7dc352907fc980b868725387e9810aa4b55df0460cc08cf6181ee933ccb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de059c40b93c2a201d4a05a51067430", "guid": "bfdfe7dc352907fc980b868725387e98e8b99a216ab5d63b58ed875308e97d66", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988ddb9999579eb9fcf0c03ee95a951756", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e10b412e1a30f799adbd734a7401b7f4", "guid": "bfdfe7dc352907fc980b868725387e982940d570cccc7c719db21f52bef7fddc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f604a8331e8c4deb01ab24b69bdc8e3", "guid": "bfdfe7dc352907fc980b868725387e98afcc74262518ce6aaa3a053024dc0c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f128e942d128644a3defc8dbcff9334", "guid": "bfdfe7dc352907fc980b868725387e9868285b05cf20659e2b2ca0374c10948a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eeed23013aab14cabad1a9aade2f16", "guid": "bfdfe7dc352907fc980b868725387e987c7db2d00adb85c58475e153fe3d06ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866785c92f2ae923009b9104d8bef528f", "guid": "bfdfe7dc352907fc980b868725387e9836ff47b27a11636739b2044ad6fccb9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a0e0faa6bd27677b3179b5e525845b", "guid": "bfdfe7dc352907fc980b868725387e98adb450cfd49cf1e4f1e42a654d03f7bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f79da5bb38a7428d53806c8e32e44d", "guid": "bfdfe7dc352907fc980b868725387e98cd461bd177af4057cbc83534d1ed18b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4c77b403253f9c70a10b36731f7229b", "guid": "bfdfe7dc352907fc980b868725387e98e31a921ff3cda28ed321da7e6d1e036f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98104c4853bc1c00f290d1983d76465e01", "guid": "bfdfe7dc352907fc980b868725387e98f0d0164b649502a713199c7ad5228fff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829fd1861e98e4147fba120c1c1e3ebeb", "guid": "bfdfe7dc352907fc980b868725387e98ca1d5d4388708a12e5f7bbf817417079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd0012361dd2667011221b3d711da43", "guid": "bfdfe7dc352907fc980b868725387e98562433eccca29e82231c1307607dd324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850290c67e4e2454c083bcdba48aff1d5", "guid": "bfdfe7dc352907fc980b868725387e9841084e6284716fc1f9c9704ac449e9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089e447440cf4d9766014fdc3ffa4bde", "guid": "bfdfe7dc352907fc980b868725387e98bdfbb2ec63af9d96b220972d46188d2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a1d2185e5cf67e568e2f0ea514dc6b", "guid": "bfdfe7dc352907fc980b868725387e98a805002bd0dff05cb42b57c0f1ab9b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc1505fe442de99d6bb3e4e29c23dba3", "guid": "bfdfe7dc352907fc980b868725387e98fecc5a75d531fada53740a4c04417c00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6bd5c4c63fc1654ad6d9b54f27d7b7", "guid": "bfdfe7dc352907fc980b868725387e98a3327c1d4e97795b73744676ca4b3f19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98223cae5eb75ad46a0e0373ef5cd3652c", "guid": "bfdfe7dc352907fc980b868725387e98478aaf286486e646626e69d62bf4683d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab034ad8fcceb8cd2c04ccdfef7d751", "guid": "bfdfe7dc352907fc980b868725387e9867d1512ce8b7513234ea33e1e65222ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c590a357bfa74e4a8a597aece72e0d", "guid": "bfdfe7dc352907fc980b868725387e98773441b25fd062f21f900d9b1b95fdbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985840e9d93198b82a264d5d284f147606", "guid": "bfdfe7dc352907fc980b868725387e98c59ff7736b7a3a795b5f7f64fae06d39"}], "guid": "bfdfe7dc352907fc980b868725387e98876785492608a0585d93dca580cbbed1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98f3ec48e95bfd1728a01a93c5baa8e26c"}], "guid": "bfdfe7dc352907fc980b868725387e98e6f59680ab9f4f61a50cef694070cd76", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9851354cd9d0159a366aef4093bce5cc9e", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98ccead53cffa7bc60ec7f2a7bb284f1d3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}