{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982658434c4092eaaba320c1985f459d9d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b3ab494227ce16c3ef67e7640ad6f07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b3ab494227ce16c3ef67e7640ad6f07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98881242e15d07651a23c0bed63a154446", "guid": "bfdfe7dc352907fc980b868725387e98f796ae3b296b723f56e05baba122c560", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69c3d9b8d59650f465a653a0543db6d", "guid": "bfdfe7dc352907fc980b868725387e980db98772e6111672a1951845b9fe4498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815015128dfb86f5e40f94332c15054d5", "guid": "bfdfe7dc352907fc980b868725387e98d995cd42abf9537adeb9ef2b0074d4a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ff63fedde88a9d93834b9b9afbbb0a", "guid": "bfdfe7dc352907fc980b868725387e98d3a81096cb1a49bc36d5521745724604", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98248b0f97c5b790cf7e2ba8094ad06dec", "guid": "bfdfe7dc352907fc980b868725387e985ef063b1ef9ee9c4ca6a668e59fa1f98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890d30f8c5b154b24f9c10988ed3ab749", "guid": "bfdfe7dc352907fc980b868725387e9842b8c36aac900f2b53ce2f2427f15694", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f5dfeee1b2da8a652515b959ccf6181", "guid": "bfdfe7dc352907fc980b868725387e98053e6ee45256d0913044f0b250d63f20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af5fe04d79fc591a5ce195457bbb340a", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e699f80ee732268a4f45efc20f9dc3c", "guid": "bfdfe7dc352907fc980b868725387e98ea20a8b7ee82b71c8c6e77a920789ce9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c949f2c34c8c192c73c8f45023c2fd", "guid": "bfdfe7dc352907fc980b868725387e9890f87a76cd0165ba7fb47409116b7bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a390c16c1b7866a26a610176be2e14f", "guid": "bfdfe7dc352907fc980b868725387e98076c4bb8b39664db5729643eeeffd3a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a59e6897706426835faedf2748b662a6", "guid": "bfdfe7dc352907fc980b868725387e98d3f60b57b9b70acd8be55663558637c6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a306925ac323501e7355d737e8c98dc", "guid": "bfdfe7dc352907fc980b868725387e98a86c0926d2b2e8154aeb7b9a54324b37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc70a4bb9372640688086bba6cf42feb", "guid": "bfdfe7dc352907fc980b868725387e98dc94e8e9e41b5cd9ce1ef745a5d3f6aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a7dc2a3a23be0e52d362d0884aa29f", "guid": "bfdfe7dc352907fc980b868725387e980148653b3c31ab69678aa137ee2bb129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e8d1c956a0f2d654445950bc9d61bbe", "guid": "bfdfe7dc352907fc980b868725387e984b8a1494c80ebe33f2f9896061700fd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bac8c645cfeba2a99cd937ee4854453d", "guid": "bfdfe7dc352907fc980b868725387e9888152ab1364126b74b5d2906f7b0ae7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a545a1ae2ecbab592f5d1a5088e1c1", "guid": "bfdfe7dc352907fc980b868725387e9823f761480bdf14780e4773ac9a5d8370"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c12b27bc25c119ad4cdfa19934d16558", "guid": "bfdfe7dc352907fc980b868725387e98d51170c14d43a831e9310a9d05e69517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b654673adc39e8d4c8bf6d81c378324", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9c2f7c65f995017e03a7c0a9327f845", "guid": "bfdfe7dc352907fc980b868725387e98ca57b60f855bf59e9205fca17ec155a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce34575df36e46561433fa0258080dc", "guid": "bfdfe7dc352907fc980b868725387e982a8d5b85200b39246535b2cf0682ec06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7dc615f15b382aa59c85e1f49167836", "guid": "bfdfe7dc352907fc980b868725387e98f482f32cf56037032a9c2aa35ab8610c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1007b62c2519565014c64910c1abcb5", "guid": "bfdfe7dc352907fc980b868725387e98f8838aeb4d8d9908a58c1bfb7603c9c9"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}