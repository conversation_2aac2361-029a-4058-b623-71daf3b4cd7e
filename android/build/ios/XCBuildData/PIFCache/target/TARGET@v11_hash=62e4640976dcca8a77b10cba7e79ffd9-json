{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986490200a88a196670092d35f89cf094a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985735b6798bb3a3e8b4161ddaa9af5118", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985735b6798bb3a3e8b4161ddaa9af5118", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9884ab7d1d262d4b5dc30dd9cef8161dbd", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d55dfd60ce697661af77574f24b0ee08", "guid": "bfdfe7dc352907fc980b868725387e98a71d1a2f7b767dd23906f901df9e8268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd67c112fdb8d7bc08174311bffd8374", "guid": "bfdfe7dc352907fc980b868725387e98619792324492fce23660d9d8552ebcab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c808edec4ff416da1e3dad5eaad3feac", "guid": "bfdfe7dc352907fc980b868725387e98902f6c56c8f4428ef49a9d83920333be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb58f8f4ff3a632fe3dc361770b574d0", "guid": "bfdfe7dc352907fc980b868725387e98357573da34d18c2f5fd1221d2451ddc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2270127046d060b3f1ca1257f4bbf8a", "guid": "bfdfe7dc352907fc980b868725387e98d62e26292dfccf6941ee8574752a2b8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e7211f8797fb151889960a53c349806", "guid": "bfdfe7dc352907fc980b868725387e98a42ee5602354b7670ed37d6041a1d564"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c4ed6facb1442fa6e082a35500df6a", "guid": "bfdfe7dc352907fc980b868725387e9896d76e9707eda6ea60fbd8e75a0d3428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff8f5b082624b8e3bcc01e5bc9dc4d2c", "guid": "bfdfe7dc352907fc980b868725387e98a3b4f933c6839d00ba7d716243f8a67e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ac98609cfd9e06573ff854e91292705", "guid": "bfdfe7dc352907fc980b868725387e98ec403c7e5edccc9f9cd217791629245b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b584cc4fb010ceadfc4c1f507dcabe", "guid": "bfdfe7dc352907fc980b868725387e98118232985ef947aec9075696a5387d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed0c500ce0007cbf80298480add7fce", "guid": "bfdfe7dc352907fc980b868725387e9837eef6e7f6c7e48ef5b725bcbfefe84e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bf65d862984a2f286dcf15953f36eae", "guid": "bfdfe7dc352907fc980b868725387e98fb8ff043f9b4b446bc4b3ee583bc0e16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b225741bd1f4890e94217dbaea5243", "guid": "bfdfe7dc352907fc980b868725387e98749bf49649f179db4d1b643cd4738826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989074416c02563ad3c278cbc73d1c7a16", "guid": "bfdfe7dc352907fc980b868725387e9854502c4fa595cab6305724b76c3f3e08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a68c28dc56a6f6f9a1d728dad834d8", "guid": "bfdfe7dc352907fc980b868725387e98204deba9313a8a1c7e3786af8985bfdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a8888943b0b6488df244b5b6c5e33e", "guid": "bfdfe7dc352907fc980b868725387e98ee9c242a76d9c63c2ec835a99ee1c97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98504952aa7c8ab1db87f957e97f039c9f", "guid": "bfdfe7dc352907fc980b868725387e98816d71ddcfbca30f76cf4b7092e6bcbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812d1b2826a88e18ce113420aa6372efc", "guid": "bfdfe7dc352907fc980b868725387e98be6305e06aade60629399a539f39f667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a25194fcd8e066bbcd9f2ca5c4d210", "guid": "bfdfe7dc352907fc980b868725387e98a1a3eee0d8522f46cbd9003c3b40df39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988adfade714238ab432959947ca1f7c03", "guid": "bfdfe7dc352907fc980b868725387e98977299160f05da3562016994122309d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec45e0a55c4597f3b3fc2719b633b2ef", "guid": "bfdfe7dc352907fc980b868725387e98036a99936dd017d18b9375b6073b7457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fdb8a212830d5f95114c467757dbcd3", "guid": "bfdfe7dc352907fc980b868725387e9814d7fab8a7b79118e8fee324512b600c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd83a5c06d280b43c7e56319213b7e68", "guid": "bfdfe7dc352907fc980b868725387e98fc54fa3fc46032a3c89a83f68c7e215a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98142ab50870d2f23baed63b6ca8e7f2a5", "guid": "bfdfe7dc352907fc980b868725387e988013cbc4b9487d1525b34cbd76a234d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e92a7d06d2007159268e318346438ec7", "guid": "bfdfe7dc352907fc980b868725387e983b34e582b976a2fd3b1a229270fae061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6bc535ba80d13c6f86557f9f149012c", "guid": "bfdfe7dc352907fc980b868725387e9843d3640b971ad565f18c1e5fd2ae5e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d30b239a17901d71f68511242a3a5d6", "guid": "bfdfe7dc352907fc980b868725387e983b1111d72213936c94d477644f0d65b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98907463861676d161b492cecc0f473ffe", "guid": "bfdfe7dc352907fc980b868725387e983598f9983d7ec9a549bd3501a2f37d92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b6aec0352b23ed53b7b4a3d1b9997a", "guid": "bfdfe7dc352907fc980b868725387e984f1e9bf6659c65a70fdb2efde03ddf6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fddc136dce8034ecb76c1ad2c667f64", "guid": "bfdfe7dc352907fc980b868725387e98f2701d2a239dedda65bb470f9db27fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c652328f0217bcc976729e1c6f8a4a06", "guid": "bfdfe7dc352907fc980b868725387e98f204e41ce63b8d215c2bf2acdd60bfbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b7ca641218d5f56da3b80465af329b", "guid": "bfdfe7dc352907fc980b868725387e9814e28ce9eca2392e1395a3995018a79d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c466e77c6f6f827b2d761278a3850c47", "guid": "bfdfe7dc352907fc980b868725387e9868d024b43ee6a1e3fae0fbf854757963"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5e6d83079eb483d5a7c0236b8cd74ac", "guid": "bfdfe7dc352907fc980b868725387e9895e849215c9324fbc0f878f4a3bf730f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982502ebd767db2f9d2cf09bd0e2855704", "guid": "bfdfe7dc352907fc980b868725387e9894c008071738f497ef31cccffc0c5e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9c642f6b4d0a66d1dccc23a85f1f9a", "guid": "bfdfe7dc352907fc980b868725387e982a74506684c19d2271522db06eb2a0b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23755527d90a93c8de4612c53379096", "guid": "bfdfe7dc352907fc980b868725387e981974877f2ad78f164f3c95c43aadab1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e59a368c6f141a59484a8bc1f8c420", "guid": "bfdfe7dc352907fc980b868725387e989c9c0425d81eccc8d251265cafca844e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8e1a33eb6641e762925ff29d2528cbe", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808213cd42f1b2437d103c3e39faad88a", "guid": "bfdfe7dc352907fc980b868725387e9858327d2c3474bd66cb5bacb51ee67ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db1ac1c7dcc69edefca8c9d92a0e995", "guid": "bfdfe7dc352907fc980b868725387e98cb4511323bb13282c02e9506e18620b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a1dd6a31962d4f50f18216ffdf77d5", "guid": "bfdfe7dc352907fc980b868725387e98d3a650f6ef1085a1f64c43fc042df8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ede1aab0064a641f6b5b77fea20e1c", "guid": "bfdfe7dc352907fc980b868725387e98f15c8af608b540adaad6bd20ddf2f25d"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}