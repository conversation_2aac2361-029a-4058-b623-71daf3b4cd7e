{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872c989aade3785df9b4dc0c24003e130", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8a617b56d9ea58b02ffdd4e4b0f89cd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806d8a0f47420da752838b77841805316", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989a9b182e3e4b10ac5806d713b1354a93", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9806d8a0f47420da752838b77841805316", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98092e52b97430ce711575f802dd65e501", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98689091d86adf6dfea4c7f5afb8db07ea", "guid": "bfdfe7dc352907fc980b868725387e98dadc7db4b16c374159e847876b5a5c5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579d5ce02787ecff5835f76b38f0645b", "guid": "bfdfe7dc352907fc980b868725387e981e4dc1995be018844127f42d95b09ef9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd628e442050da7f597b648f466afb6", "guid": "bfdfe7dc352907fc980b868725387e9842f0bc853c91eea35345cfb06ff5fc71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897d69c0658ff22048f94b5d527750b94", "guid": "bfdfe7dc352907fc980b868725387e98b53af216a9fd4c6cd5347ab58e67b43f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6e6f17e32bc5534372ee287ac6d5e2", "guid": "bfdfe7dc352907fc980b868725387e982ccb3f04f55ebf8af09719f7a400a059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be6c88199ea618b6c8f97bffc0fade03", "guid": "bfdfe7dc352907fc980b868725387e98f5abc16eb66739a2a527231aacc344c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98524afbd226f990c7e78a695a3048a4df", "guid": "bfdfe7dc352907fc980b868725387e98af84c2d31c85f7028fbb400a32a6d25d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc078cb83df0c50b444fef2bc029e650", "guid": "bfdfe7dc352907fc980b868725387e986b930eeb2821580e7052d1284e304565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833da6c4c15f338a93ecb3004c706ff7c", "guid": "bfdfe7dc352907fc980b868725387e98ddaddf722f877b55662497f3fdf3781b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d5a732768f017116f333b7efe18c32", "guid": "bfdfe7dc352907fc980b868725387e9852ecdc6f4408f7d014ed59747315bb87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ff41a1d04a48fda3fbe7e721cf1110b", "guid": "bfdfe7dc352907fc980b868725387e980697f2a69cee8646ddbc093e5f20b18d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c40231f633dfc3229d089dc3d8ed8ed", "guid": "bfdfe7dc352907fc980b868725387e9850352de990541538f1c7a13e258dd56b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4460ed521ede0e7e0445f814abee7a4", "guid": "bfdfe7dc352907fc980b868725387e98cf66a9c103be09e43a382a572b4f7e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0158406c5ed4efa8e8c74bc165d8e90", "guid": "bfdfe7dc352907fc980b868725387e9880c21fde3d2167fc9d12d27cc33ed046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edefa8f15a2ec8aa532a3ebcca7d6d5a", "guid": "bfdfe7dc352907fc980b868725387e9836a4a1d9b1a1e5081798adaf7f4dc724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff92c67ee843c205694ef6e14e63e36a", "guid": "bfdfe7dc352907fc980b868725387e9853bb06f621b446dc4cb1a742a6a56bb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca90dd011a6f0bd798b1b2b8a42c3edc", "guid": "bfdfe7dc352907fc980b868725387e98db7276486d0cc17338d85a55f42d9b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55751c5498da1cd5505d5f614d79504", "guid": "bfdfe7dc352907fc980b868725387e98982d3e0f53a8c492754b607e97821e80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1024bf57f34635db95203fbfc9a979c", "guid": "bfdfe7dc352907fc980b868725387e989953718936234c8b91fd870bf7f0662e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85c635a039d4bf7f682a74a47d37ba9", "guid": "bfdfe7dc352907fc980b868725387e9891141585577e39862b31b3b16a249e83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc620e72d5bcb5be8b6ab7352cbfd95", "guid": "bfdfe7dc352907fc980b868725387e98037ef5ce2d7f403f3ef0e26b62ec0e57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb00fe9b5ba830e0223e3037325a56fc", "guid": "bfdfe7dc352907fc980b868725387e988242601e9ab359a5610dde2e917029ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1b8393221ade8ceea074f354a4def3", "guid": "bfdfe7dc352907fc980b868725387e9873030f6b933d4f2bd5bc59249f211151", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9842caf81b3e5a0de8009123b1ca2a9cdc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982ab39f2318a015fc87323f843be6c8e7", "guid": "bfdfe7dc352907fc980b868725387e98240427bd63b48f6529835a0da7f23fb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55308792618361cf185246dfef6661c", "guid": "bfdfe7dc352907fc980b868725387e9854ed1fbced40f6e1187b1671896a7629"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc4a5260a48fa79f3043497626cfaf6d", "guid": "bfdfe7dc352907fc980b868725387e98d50c20bdd9a458573194c9cccb26b26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def2268704f637623cd892aa654ea865", "guid": "bfdfe7dc352907fc980b868725387e98bef85a63874f29d0844e929352ff3408"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98265aaffcd13b6688f0b513c17330ccf2", "guid": "bfdfe7dc352907fc980b868725387e98a7b5c5f19bea3fa555a23ee44a660355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818817f2d3dc5addd8601e5b3b9eb6b7a", "guid": "bfdfe7dc352907fc980b868725387e9888bd456deb553f7b0a5ee08e0e08d481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838169d916ec93ffa26994c6923372213", "guid": "bfdfe7dc352907fc980b868725387e9856f2b8664b58b1a534966f387b408dbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985865449cb5061870ff3959b3a2418091", "guid": "bfdfe7dc352907fc980b868725387e9822f5175cff6f629fe40cfe98dcb1dcd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f8a93975a89518d05dfeae21d8bad7", "guid": "bfdfe7dc352907fc980b868725387e989161f476f44d094752efd72eb12a5971"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7458062f90b9425b1b0d88fe514b1a", "guid": "bfdfe7dc352907fc980b868725387e98d036796a54f180fdb09f6bb65ef68296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815e6dd1e8391ca5c0c81375d57cb197b", "guid": "bfdfe7dc352907fc980b868725387e980936d44602e0b386ef445271208c2f34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a91d86fc18c050c691b466c21ad1a6ae", "guid": "bfdfe7dc352907fc980b868725387e9836b5bf894ab3a68ef36bda44bf7b5204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b974f2dbc41d064aef733d23d161d1", "guid": "bfdfe7dc352907fc980b868725387e989147ccfcdc05c5dd9743de910c323a5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baeddc9dc685df859ddb6c3673333862", "guid": "bfdfe7dc352907fc980b868725387e98886a7201825bec1d28a091013fc3282b"}], "guid": "bfdfe7dc352907fc980b868725387e983794cfe6efe23115e6243d362f42cc49", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9888109f60e825f14adde841c7e0050803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5192e7597d49b2508d02a0ca39a2058", "guid": "bfdfe7dc352907fc980b868725387e98a64b247ad7930571bf2deebce9632163"}], "guid": "bfdfe7dc352907fc980b868725387e986a2fede2ca525d93d029ef4142026598", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9834fc7ceb17b003dd8106028d50024a8c", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98a9c1da8e16c6e5bcaeab9380f3ee9e56", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}