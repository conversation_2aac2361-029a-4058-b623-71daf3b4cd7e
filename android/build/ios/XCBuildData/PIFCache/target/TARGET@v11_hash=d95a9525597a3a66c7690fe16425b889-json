{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b49d168d1615d413419a42af37b8875c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd886fc3841ccdc079abf050f7662fb3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0182fd82760b366b5e0f8a2406ce2fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9884d2083aec11f40d259a61c1b73e5af2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0182fd82760b366b5e0f8a2406ce2fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988759751b9b9e4d0eea6a0d8b93635d0c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a3af5cc5b282347142edcee9d444678f", "guid": "bfdfe7dc352907fc980b868725387e98bc6206d3c1343b989cc62cc728c1255a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220ece81a4a3e600b14d0a803df76d33", "guid": "bfdfe7dc352907fc980b868725387e984bf31973225ba90881a85496ad7f7c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98914f25d6ec3f96477b2e0557330746fa", "guid": "bfdfe7dc352907fc980b868725387e988933c2c9e16802d4a150791c540e6597"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985477fa786551fd6306255ecfac80dcf4", "guid": "bfdfe7dc352907fc980b868725387e9852284dd1bd1c0f8e4e550ed2bc122216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989717b0c9b332e24934ac24c9705fa814", "guid": "bfdfe7dc352907fc980b868725387e9857e92eafcf095d017ff6f15065a3405c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873439a2c7ee77f0c5fddd55f3a39aa4a", "guid": "bfdfe7dc352907fc980b868725387e9857ee05b09d67e874f58e18a439d42e22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d00b951a22a4a68ddedfedd9be195c", "guid": "bfdfe7dc352907fc980b868725387e98aa5b6e1046ae43b7172362de4da1db1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e4b4d1a86e2743322d9b09dde6bf5e2", "guid": "bfdfe7dc352907fc980b868725387e98b1aaa8e408c5cc96fed0785ab22aac74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb7567cea01c6e27f27b89d62eb5879", "guid": "bfdfe7dc352907fc980b868725387e98ba9d6afbf8e0db038a51b9123e7dd75d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98741d9edad827f779f730beecbf7d5b86", "guid": "bfdfe7dc352907fc980b868725387e98208624eef60eb53202f8bb07b22ad3e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2d6d7e10702acf3e24c2aa8b7a408f9", "guid": "bfdfe7dc352907fc980b868725387e987cf8092be674d41264d5b4aa190b19b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0653d0562983bbb5a61b6f6870acce", "guid": "bfdfe7dc352907fc980b868725387e98bda8d5f0b18a98b88a801ae971d9db34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d04604bed53b5f13aba3a1b661e491b", "guid": "bfdfe7dc352907fc980b868725387e98696a930142b51ae08b3324c6dad88e36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851218bfe2a0239914fe07b9bc83d33ef", "guid": "bfdfe7dc352907fc980b868725387e98817499648310e12f4e470e7282f37d6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea807cb81734f420ba73f927f840b3af", "guid": "bfdfe7dc352907fc980b868725387e980050e7d795e2ca91fdfda43dd76e3f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b76c8290018f5804696957cdb1897174", "guid": "bfdfe7dc352907fc980b868725387e98b4db26483c21313683f69f3fafddef02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932b78bbe5b6775cecd910f5ddb3b27b", "guid": "bfdfe7dc352907fc980b868725387e987bcbbe1c83867b7eed9e11434936d8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e686acb4a646ba6773dc51e624c4e135", "guid": "bfdfe7dc352907fc980b868725387e987c9084b7487dcfa29c562142052cede9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6c0531d64a1f355599587dc0ea32ad", "guid": "bfdfe7dc352907fc980b868725387e98992a89eb4167803457035b4e6fb649ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987606301f9e86f00f290c2a59f857ba3f", "guid": "bfdfe7dc352907fc980b868725387e9847eae54adfd047d13c53c7393803a61e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e664dda0cfeea50a2738e3b0ead11f0", "guid": "bfdfe7dc352907fc980b868725387e9804676c51ef0b86fca74c7a77e1bf1dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f9736f0224688270326606deffe475", "guid": "bfdfe7dc352907fc980b868725387e98fad68ac7e460289fab603558a12088f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986378a235e7e7a9bdb1e1bba7acf77399", "guid": "bfdfe7dc352907fc980b868725387e9857ad763350891d8e9f8405e145946b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870150aebd244ab7cc1a8dd0b1a5af77c", "guid": "bfdfe7dc352907fc980b868725387e9886c2cecd3ed3e6c1fc9e40f27710762e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570be468b578eaa699803dd8107d0be1", "guid": "bfdfe7dc352907fc980b868725387e981fa20c26c5e20b61433b08b9589919f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828dea9660c63204577b18497049cec11", "guid": "bfdfe7dc352907fc980b868725387e980b736df6e6688327542c79b1fdf811b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa385fb1e2de4d0faddc0f4031cf516", "guid": "bfdfe7dc352907fc980b868725387e989986ffb42e1bbe70c33c9f1300c540b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dee822587ee358b8e5beed6e2d3a8f58", "guid": "bfdfe7dc352907fc980b868725387e98cfe95b4f8470ddd548dcdacfcd372c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f318ca6cf4ff2716b9df070f0aaadcc8", "guid": "bfdfe7dc352907fc980b868725387e98f99ac6b6300b5cefcb4e58ab1244a263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc63fd88521ae24b5353aaa01fd1ba1", "guid": "bfdfe7dc352907fc980b868725387e981e1ed08e36cdbd1bcf6c472ce59eb6c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c65391c2e8d445f3e149e13c181605", "guid": "bfdfe7dc352907fc980b868725387e98826579becc354252d0900158670f3620"}], "guid": "bfdfe7dc352907fc980b868725387e9846d773ab4e673541646b331a3987b417", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980868d8e335c4eac6fdad9d72117882c5", "guid": "bfdfe7dc352907fc980b868725387e9868f1e7ccc06cdfd28ea536adb7ba6f56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5f5b41a470d4cd3921c3163cd465364", "guid": "bfdfe7dc352907fc980b868725387e981f89d8ff95e6bad34dcfa7eb925ddce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98715a4db33f85960692dce19dd4dcd234", "guid": "bfdfe7dc352907fc980b868725387e98158872a488dc71b240675186048e0a09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429ab420c98f7adfdebfda93351ddf00", "guid": "bfdfe7dc352907fc980b868725387e98e85311238c4a56dbb48154b17d6577bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980695a6cb8f3d3f564bd264e05d1d3be0", "guid": "bfdfe7dc352907fc980b868725387e989b9303b716f7cb9714e8e4c9135adc26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860b81bb732024882928d0339938a8ca7", "guid": "bfdfe7dc352907fc980b868725387e985f9dda0fda808024f8d38a1857f66ca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f591f5a887e84d315da866535417a2c3", "guid": "bfdfe7dc352907fc980b868725387e9813b6f66a788eea0aba24ac37fd27472d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21134f35f13dc1e510578c87b1eb323", "guid": "bfdfe7dc352907fc980b868725387e98d5f381414b1f9871279c7b941e5bf562"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990542f10bbba7594ba195820cb631de", "guid": "bfdfe7dc352907fc980b868725387e98e3cf883ab719ba744d60ed95f50692b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842ffa31097c58b3c143ba8cdf6e03e31", "guid": "bfdfe7dc352907fc980b868725387e9892ad4c56d53c89128e6c12468106e44a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2e61af5e7ba0c54270dece7517e89b", "guid": "bfdfe7dc352907fc980b868725387e989315dc21163abac4d9199d69892bfc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345791c8f03f1493cd418632dfe4d6f8", "guid": "bfdfe7dc352907fc980b868725387e9822f5b214be4659d85b9ba0ff0987f171"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de140061e01349a597ec1bd17ae1ee55", "guid": "bfdfe7dc352907fc980b868725387e98d6e71e3b447b9a4f829654cc456ec5ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d869d876bf5a7416129330b43e29d88e", "guid": "bfdfe7dc352907fc980b868725387e984e0cd8245bc9578cd561aaec8d459a33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a95c864a7934c3f17a2e76db994a4d60", "guid": "bfdfe7dc352907fc980b868725387e984f683ddd04da02bfe470b22f20523dfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b48d0475434d3762dd0ec2edbc54af9e", "guid": "bfdfe7dc352907fc980b868725387e98f997ce620b75b920ee4ecd51acfee589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c3e7277268b0f8c1053a5044d598c8", "guid": "bfdfe7dc352907fc980b868725387e98164cb89c3c25d56386b508ffea2460aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d59ef1603ca96b0c5a6553cd792a6c57", "guid": "bfdfe7dc352907fc980b868725387e98889da919acbb6d42bc72fdf3589e1ab9"}], "guid": "bfdfe7dc352907fc980b868725387e98a93b283093954595c7e2613bd8e20e4e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98a9e781e16d64228c36cabbaf2cd081ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e984f2735b2577cc9750c421ea213b17d35"}], "guid": "bfdfe7dc352907fc980b868725387e9848095c202e3cefbc19792c4e74a4ffa6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987895dc5936d8f305d832521d1686df0c", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9845cf5bd59e033a86ec76757463da3aba", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}