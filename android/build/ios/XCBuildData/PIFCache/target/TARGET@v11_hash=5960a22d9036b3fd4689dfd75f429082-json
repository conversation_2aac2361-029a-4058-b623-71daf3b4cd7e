{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ff331543223ddd83fb129d72d7cc59b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fd1e6d701cee2019c4ffcc8be24662e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809874df2737a9700735fecb0df05af7a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbbcd9f52ffc8d7270fd0d309918d48", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809874df2737a9700735fecb0df05af7a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f18034db5e36d2954d13efe24565cae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855ef9e877de66a8c45a7f148f55d773f", "guid": "bfdfe7dc352907fc980b868725387e9871d91a42be601b71d307300b8355ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988365bb772315037cea6473f11605eab1", "guid": "bfdfe7dc352907fc980b868725387e983b665e94a4cfaa5b6a0aca002e7e59fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988857f71429e1b603a8c303c23dcf1bb2", "guid": "bfdfe7dc352907fc980b868725387e9806e4e0f07e830dcf1c064d4fde7372c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d17101c97bf4d81b533ac0397051f4f", "guid": "bfdfe7dc352907fc980b868725387e98396b98354e97c6b1358117f08a4e306a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a08b0e2c486942f741d034af2dd9ac5", "guid": "bfdfe7dc352907fc980b868725387e987ac25a11fa40f178db5d50bff061db77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987735f6d7350f805d52938a3c547db934", "guid": "bfdfe7dc352907fc980b868725387e98f3b1973278086302771042df4e00ea41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a872ea5e8168fd8af4653428476581d3", "guid": "bfdfe7dc352907fc980b868725387e98756f5b397ad053077de8f46d7da8fab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989db63ad0e96fc62d862608e481f650ab", "guid": "bfdfe7dc352907fc980b868725387e98b1c7b574f15f7fc9aa11534e7d2b7562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066f9153d4346b1bbf54bc3afc63762f", "guid": "bfdfe7dc352907fc980b868725387e9834540f115b38beb81f79aaec362acee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cf0fccbc836585df201906bce00ed79", "guid": "bfdfe7dc352907fc980b868725387e987738e43885705e2ca3d3f952a852d1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985400d057beef65329a2443c70cf25791", "guid": "bfdfe7dc352907fc980b868725387e98039ec9cf2443d2dfcaa6bfd00745a082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98860df6d9f2a7b1699b8489c24f75ee86", "guid": "bfdfe7dc352907fc980b868725387e980b651aa249a332add6784e1a51326269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce9c66b96f4a3dac5c14ca5d52d6dac", "guid": "bfdfe7dc352907fc980b868725387e988badb0d31d2706b6e4f9747d22135a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e1f5ef48b3b72683dd9065984b3128", "guid": "bfdfe7dc352907fc980b868725387e983155fd9f6a3b8bca94c3c79ab7d6f172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8f49286cbf3fa872094f5c9a509b72", "guid": "bfdfe7dc352907fc980b868725387e98900d260dc13755009dc5e9a87b094ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a4fdf41c3c6d9fce024ec3880c1b74", "guid": "bfdfe7dc352907fc980b868725387e9828b6a38764c56d071ba48fdbb255f533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98389a2703b2c7423d841fb455aebdcc98", "guid": "bfdfe7dc352907fc980b868725387e98f05d93bb93fdddbb0e4e7929359c57b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b529e6c1fb4b7cee3b65b4b57ba99023", "guid": "bfdfe7dc352907fc980b868725387e983a4581eb617a5ad7d83892d698d594b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa7b7841b418014c9cd7dffd98df005b", "guid": "bfdfe7dc352907fc980b868725387e9849ccab35060d3b04350d5b2ec96a6be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98596e548ce813f12f110ed2020deb4dca", "guid": "bfdfe7dc352907fc980b868725387e98ee4dfe3e5170a4d901e0ea6c5751579f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a5449840f248084e76cea7b80bf6cc", "guid": "bfdfe7dc352907fc980b868725387e9885d6d8d78881bf3885d75b32e70b1277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4db145d7c2e55a239d4b45c730ada8d", "guid": "bfdfe7dc352907fc980b868725387e98764cfede42317a5d2c6a5172733c7e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d97f406a21a67d1127389e47ae12cfc", "guid": "bfdfe7dc352907fc980b868725387e98ce2868d00d05d2d2211051232eb2df1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c40f85d19388bed9723c2b5c07af9be1", "guid": "bfdfe7dc352907fc980b868725387e98cd6526b0c21c31085bd5878a26ea1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7d371fb91413b7df514bbbcf2afd5b", "guid": "bfdfe7dc352907fc980b868725387e9889255affc0e671e70bb08e4e4f3f6e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b1bd5a354db5e4d87511dc9a674998", "guid": "bfdfe7dc352907fc980b868725387e9815dca42c43fccc27a7b13ec4db07705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98524465bc20f373c4246cd9e6986f6853", "guid": "bfdfe7dc352907fc980b868725387e9879c2596fd3a27cc64d264f803ac04a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50e6498ec2891a33702ebf4fdc2e00d", "guid": "bfdfe7dc352907fc980b868725387e98f42c7113b7322f4073dcac9b6e96d7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da5da1013b1bb0e05b7523be4b8ff8a5", "guid": "bfdfe7dc352907fc980b868725387e9812f35353a4585fb67a3e91a122e4961f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98737200aa4abce76ad595a31769e5252b", "guid": "bfdfe7dc352907fc980b868725387e98ede8ff609a807a6a6f7f3501653a1530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a399acce9e2dbf00783340cd7265ebba", "guid": "bfdfe7dc352907fc980b868725387e981f72ce5bfe0f0920fcf3a20705e8519e"}], "guid": "bfdfe7dc352907fc980b868725387e989a8bdd7db64c936feeb9fdccde76a124", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f7d7c3f0acbe317028b17cbf35885e5a", "guid": "bfdfe7dc352907fc980b868725387e98f2d8328aa3716bcf58165cb6aabdd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ddbd2f8136b464d309723c2a44e7ea2", "guid": "bfdfe7dc352907fc980b868725387e980a6842fc579249ca27117522fcb32520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579a5c0328b2fb77802b10736006a8c4", "guid": "bfdfe7dc352907fc980b868725387e98c5bcc8cfdebaa2ea4c37d0e8cf801bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867be8a42ad55271dc85d16dad88121b4", "guid": "bfdfe7dc352907fc980b868725387e980d69a15c5090c41cfe80991e6b959490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b26205d7513d06517b731d5abe520f", "guid": "bfdfe7dc352907fc980b868725387e987c40da3cb9a8bee11591bb9fcfbf9747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611d2a0a8c512df70764d858c37d1f18", "guid": "bfdfe7dc352907fc980b868725387e981e211ce9b5a82c38a89df7432af5ae11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98158a932544fa960b592fe2b18b44f602", "guid": "bfdfe7dc352907fc980b868725387e983e0ead0ddd47065d3ee3316a6a456558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f58902379fc7d06cff8c2ec3d746bf", "guid": "bfdfe7dc352907fc980b868725387e98a10b53c251fead24e2bc80ffabaca7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988922023835af105948806bd9a1877434", "guid": "bfdfe7dc352907fc980b868725387e98908b25b6de0d398e2db2ab88c7de1b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98154e276205b06176bdd9829090654615", "guid": "bfdfe7dc352907fc980b868725387e981532a1ac14391b4b492680fe682b0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a080e557f533a440ca45b2372c877a3", "guid": "bfdfe7dc352907fc980b868725387e9818ac9bb25ea11222d5e6d5d5016eeec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863eaa89fefa2407f25987c1c0b6e8a17", "guid": "bfdfe7dc352907fc980b868725387e98aace04b8dc34a35748654fa1fa5d33a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de99df6f1df4adcbf632d23f0ad507ca", "guid": "bfdfe7dc352907fc980b868725387e980435a41cdea476bf7b11f4bfcd411ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb69efb11e49518fac1cb61d72dd1ca", "guid": "bfdfe7dc352907fc980b868725387e984695a5858a5c3c95172aebe1fe52346f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872600f374898fe2c126c41d9f6f207ad", "guid": "bfdfe7dc352907fc980b868725387e98fad322e08034ceb08d597725afeba077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af389e66ab271d1145a25a9b18ea6af", "guid": "bfdfe7dc352907fc980b868725387e98b739033624abbc444cdbd9c09bdf3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4d639a7027a295f55950c1099ed74e", "guid": "bfdfe7dc352907fc980b868725387e9840a011651d32a8a7d65ff37efb37da99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119e74f982cba042c80f5aa1401c4586", "guid": "bfdfe7dc352907fc980b868725387e98a2eb62cb148ada087de53721959663a9"}], "guid": "bfdfe7dc352907fc980b868725387e980f6ad4bb86e2126a469e3678db6ab533", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e989ad225b4241890eee92fd5876f740106"}], "guid": "bfdfe7dc352907fc980b868725387e980c126ec4e291ef8d9fbbbb38989292f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808b47889de662cd4865776eda02c0c2b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9857de7acecfe5aa305e96dd28add8de37", "name": "FirebaseAppCheck.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}