{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be4a7343f729b06a5177d69af9c81b55", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ddfcdda3dd12910e93f9d744193afa0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd1ca1e0e2105fccf58c84140b20b572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1577409079433f6bd3ad2eaf4d4c1ea", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd1ca1e0e2105fccf58c84140b20b572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff5ca4e78674b59d5fcd0e292dc036ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d15bee0cb8bad29918eff40960bb9087", "guid": "bfdfe7dc352907fc980b868725387e98d72e8899182fbe88adc63320ac791adf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982329a696c03333d72d4b78cc82dbe06d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f93ed7455d508e6805196fe38eeb85c5", "guid": "bfdfe7dc352907fc980b868725387e989ee8bb9ea4a8076be5f18ba969bfa025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff0794fd7bb9512911bcceb2aba1339b", "guid": "bfdfe7dc352907fc980b868725387e983d8e90f4dc1c8d304fc5f9b1c934782f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986322663c91d9482c1ffda4ec1a6e17c9", "guid": "bfdfe7dc352907fc980b868725387e98db9b4b94d8028b268b0c012dd5f3a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a075d5fa20e06b0d29cce6969f53eaad", "guid": "bfdfe7dc352907fc980b868725387e98485ad3b2013e977558820c65fd150e8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ceba5bb5f05a80994f5ec9d0dbcf439", "guid": "bfdfe7dc352907fc980b868725387e98c2086f8a11cbd78d8976685817de5553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b068619b6fdcef297f384ceed1884e7", "guid": "bfdfe7dc352907fc980b868725387e9854fb37d23ea8eaf9e632e743714077f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f1c5106881e4df1fc6ee1fae0fcaee4", "guid": "bfdfe7dc352907fc980b868725387e9830f8f83e65c94157dc12438b8c65bc30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985655f1ceaef42f2ebe3e74ffafefc781", "guid": "bfdfe7dc352907fc980b868725387e9897203c0874b9936b3c541ac26d845adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454c699ab8d2a4800c76088ad73fd927", "guid": "bfdfe7dc352907fc980b868725387e98e9e2b3f9fc6c1109d27f8c26ffd8f096"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f45d83a4e6eb83a4e5528e42a3fd7e", "guid": "bfdfe7dc352907fc980b868725387e987c7b76a2102c84f9c3e44b31fb4d2976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9f78987c7b22b10b3016d26f5ad60e", "guid": "bfdfe7dc352907fc980b868725387e98996ac970e7d542606f9930535cf18ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c7ccaa8196a196a4186f812991af95", "guid": "bfdfe7dc352907fc980b868725387e980ad58c1ba6ffb4fd992d5f6656607798"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b576aabb4620be3090bb53156bd652", "guid": "bfdfe7dc352907fc980b868725387e98a2b20bf21a1f4d8d8ee21211970a9dad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9102acf0cd7bd27ad8d24226577cca", "guid": "bfdfe7dc352907fc980b868725387e98162a263dbfcd2d33ce7c9287cbe36ac2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871732c89a3d4bd59568a50b74d5ec56e", "guid": "bfdfe7dc352907fc980b868725387e98ab9bcf4b13903586b79fc3eb0c325167"}], "guid": "bfdfe7dc352907fc980b868725387e98e68f1fdb27ac30e9ea3c65b552f4c2df", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e984f3fa86cfada3aed952c42b5337bb185"}], "guid": "bfdfe7dc352907fc980b868725387e989ee395237b27f3b93a58c69c380c8878", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987672ef1d1e0e5907f90a0ed85a5a21d9", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98ac8b18c63225a66be30d1085e8fac012", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}