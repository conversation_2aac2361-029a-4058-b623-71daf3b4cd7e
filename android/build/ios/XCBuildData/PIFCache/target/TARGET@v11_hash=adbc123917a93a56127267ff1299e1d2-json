{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983aa955446eade5f113c1900f722b9fb5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2e8b29b7437ab0a93be27bf4332602d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803e97f2be0ea203827fff5f85dca9bc5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b284be979654960cc38b75443334388a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803e97f2be0ea203827fff5f85dca9bc5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98759866daeb839715123c84893ede373d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983e46eb397c8e70fdc911b411681114ff", "guid": "bfdfe7dc352907fc980b868725387e98d7c0afc0f0dec60f682602a819300844", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f1a4ada9f50c66cc5afef13d8649d1b", "guid": "bfdfe7dc352907fc980b868725387e9853d095b1b9d5164469ae81baf8e95075", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801921f8e6bb19d753fa0fcfca494d7e5", "guid": "bfdfe7dc352907fc980b868725387e98901b0496497c184022580c0cdb5afcf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a95bc7db9ebc3809902adec702c305a", "guid": "bfdfe7dc352907fc980b868725387e989aedd3a10d30dbb71efcd1ae5997f44a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a57896e73cc6b97d7b402e7db707997", "guid": "bfdfe7dc352907fc980b868725387e98825fe4f9fb0315d5d70f328752e95047", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6cfcdc9ba84164ce56698018e172ec9", "guid": "bfdfe7dc352907fc980b868725387e98e7c2032a2ce9386234266ed488032c7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd9d51d992368e9ffd4571be6389f24", "guid": "bfdfe7dc352907fc980b868725387e98aec115b9ebfcea0d1828c23378e3b1c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac4bdba207e3eb2ee6b696f689eadef", "guid": "bfdfe7dc352907fc980b868725387e98d5051e52eccab4804ab9e3b0ba41e8a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190885f5e6091888e5df36a618fdf63b", "guid": "bfdfe7dc352907fc980b868725387e985460665d4af0681d2f98a08f5a89c987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9c9687fcaf94c5517446be36ecb3f9d", "guid": "bfdfe7dc352907fc980b868725387e983fb10d3762e204506ea6984a6d7da3a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852d1a47a4556fe80e5a52a9133eb4e0e", "guid": "bfdfe7dc352907fc980b868725387e982002c5e326feebddb1ecb23faa7022a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e2c8403769fc99bd566add5e09a251e", "guid": "bfdfe7dc352907fc980b868725387e985054ab6a32580a29274f2d4fe8a2f10e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98947e6a8bbd085ca8d17becdbe57e7be8", "guid": "bfdfe7dc352907fc980b868725387e986995aafac1e031dcac6b6667594f949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca6ec357bbd8d6f664eb292a9b1d186", "guid": "bfdfe7dc352907fc980b868725387e98f56388c7b139055009d27c2414cf02d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867430d1f2859f7acf81e020cf2bf6035", "guid": "bfdfe7dc352907fc980b868725387e9872d1383ecbb65a28c21ae1437d5ccc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860431171586c97897a5331623f000439", "guid": "bfdfe7dc352907fc980b868725387e989aadee1ded00c87c562f33d4d0c6748c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981150d0b8ec43e1083e8f6c7dd7ec8187", "guid": "bfdfe7dc352907fc980b868725387e9852857e24eae9094447ceb33283e99f6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c287e1322914e2d9d7f289a2278731a2", "guid": "bfdfe7dc352907fc980b868725387e987b4a74377011451acfe863c71e5dbeb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3de7605eba0a849735afa73691a300", "guid": "bfdfe7dc352907fc980b868725387e98b42aaf8c48818073df5eebe26d0af86e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f93d30949ab9eaa0ec6393934f4df89", "guid": "bfdfe7dc352907fc980b868725387e981f44e716dab2d61e5f4cb0678d050105", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4b07d5ecdd927abcc872aba144d3415", "guid": "bfdfe7dc352907fc980b868725387e98237913dc64b129dac7270ae3632f634a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c49e38c43f594cb6db5212306b7ecc23", "guid": "bfdfe7dc352907fc980b868725387e9883aed1694cdefff26bf5b21c8cfff062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7fcadc54a89dc5b2dcc5565721452e3", "guid": "bfdfe7dc352907fc980b868725387e98a5d95b910ec2910751ea20aa1d0a1ca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d228e7f069f8496f3e2e4187bc65cc63", "guid": "bfdfe7dc352907fc980b868725387e98453e5d4e8a053d4af530996ba3285c2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3d820a9bc218a1cae0d8ae0a5a3bc27", "guid": "bfdfe7dc352907fc980b868725387e98b9e3f0199b8f3a32af19d5f2b8628095"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1b664cfc4e7171d7418c483c703251", "guid": "bfdfe7dc352907fc980b868725387e98531d35f28eebcb7e189b857efd59204b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7fa7a3d95a17468898d22587313044d", "guid": "bfdfe7dc352907fc980b868725387e98062bfa7f720322219511dae1b63bea91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b710bf58bf4b9239393af3c343b2e3e", "guid": "bfdfe7dc352907fc980b868725387e987d4952edd59e924b031cffacd6abbff4"}], "guid": "bfdfe7dc352907fc980b868725387e98c0e47988fb449c14c1136eb279dc14e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986ea8ee1c528841498d4f152df3cc48f3", "guid": "bfdfe7dc352907fc980b868725387e989b02af0d358d3b6a5dd1a8259b9b2de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c979a338d57e7019e92128bac0234668", "guid": "bfdfe7dc352907fc980b868725387e98037a1e319c830d77bb70963ccfb72e6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a8c83086989b787e512c724f5cd6193", "guid": "bfdfe7dc352907fc980b868725387e98b1086276b4d3feb7d3ba365b1104fda4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985824727f17b32fb1162faa39f028b86f", "guid": "bfdfe7dc352907fc980b868725387e9829d68d4be2f8925328d653b0efddb0f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98927182f3c71bb9f3b3fde26294836450", "guid": "bfdfe7dc352907fc980b868725387e98e8a3e87f94cfaefaab2b2f2008b772c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d181c72076cd76a05d487648fc266c7f", "guid": "bfdfe7dc352907fc980b868725387e981f6842780af17b4210b59300696c9714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a144a5524da44bf8d26bfeb231f57575", "guid": "bfdfe7dc352907fc980b868725387e982646773f9bf808f2aab36a66457b81a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3ef6ceacd6a716a977b5de6e1dc585c", "guid": "bfdfe7dc352907fc980b868725387e9839fb378b5d43421189e3067dbc70f03d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae36b7338abbd4b8a5ee3973320fc536", "guid": "bfdfe7dc352907fc980b868725387e984e6357ffdb72cdbeeca3bb6ce0ff4a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4949ac3b388362070993b2eab9d311", "guid": "bfdfe7dc352907fc980b868725387e986eb1b48825a5384733a4704588c7de02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e82d41fb57db2226a4729f04db2dc33e", "guid": "bfdfe7dc352907fc980b868725387e987c0daa58f7be68cf0bba325689821371"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0423839cdf52820c2e393c6cb0eb1fe", "guid": "bfdfe7dc352907fc980b868725387e98c1bcf9c09ca263827e40aef59ac13638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e229eb2e89534dd7435f99c7cbe80c1", "guid": "bfdfe7dc352907fc980b868725387e98a0b78559d3da3a0f60a2faed38cb2c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd16aaa60c07b0004d99c6ae49c320a7", "guid": "bfdfe7dc352907fc980b868725387e98246320921f68032aab4a65dc8d4e3af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98765f55078afe194a7ceb57e0c40d93db", "guid": "bfdfe7dc352907fc980b868725387e9843d8fb7eb5e09f422cbe1a6a5edc7273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a636b58a13d6b25e3b859a8d73807921", "guid": "bfdfe7dc352907fc980b868725387e98dea2e6ee2ed4f399b43f6f74d318b8ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f11e6c3aad04eca683a5bb9a88f8af0f", "guid": "bfdfe7dc352907fc980b868725387e98312371d57c19f1244a4ef1e16e7cdd80"}], "guid": "bfdfe7dc352907fc980b868725387e983579964f22fc9e520cd377f0bc1de360", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98c8c57cc7aec20595ae8e4921b07aadef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98c47576b1aff7102148eb13e2f6e68260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e9816dda9d354dbb90fd6c283e36eb95141"}], "guid": "bfdfe7dc352907fc980b868725387e98f7e1aad0dc8b5fa00df7b0ba0afb9070", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986cdfd0bd31679224245a73414c755fc1", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98d58c60559b4c450d5ac212bfed94c17a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}