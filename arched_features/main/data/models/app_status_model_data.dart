import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_status_model_data.g.dart';

@JsonSerializable()
class AppStatusModelData extends Equatable {
  @Json<PERSON>ey(name: 'android_url')
  final String? androidUrl;
  @Json<PERSON>ey(name: 'ios_url')
  final String? iosUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'huawei_url')
  final String? huaweiUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_version')
  final String? latestVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'force_update')
  final bool? forceUpdate;
  final bool? maintenance;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lookup_domain')
  final String? lookupDomain;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'about_us')
  final bool? aboutUs;
  final bool? prize;
  @Json<PERSON>ey(name: 'iq_show_room_lat')
  final double? iqShowRoomLat;
  @<PERSON>sonKey(name: 'iq_show_room_long')
  final double? iqShowRoomLong;

  const AppStatusModelData({
    this.androidUrl,
    this.iosUrl,
    this.latestVersion,
    this.forceUpdate,
    this.maintenance,
    this.lookupDomain,
    this.aboutUs,
    this.prize,
    this.iqShowRoomLat,
    this.iqShowRoomLong,
    this.huaweiUrl,
  });

  factory AppStatusModelData.fromJson(Map<String, dynamic> json) =>
      _$AppStatusModelDataFromJson(json);

  Map<String, dynamic> toJson() => _$AppStatusModelDataToJson(this);

  AppStatusModelData copyWith({
    String? androidUrl,
    String? iosUrl,
    String? huaweiUrl,
    String? latestVersion,
    bool? forceUpdate,
    bool? maintenance,
    String? lookupDomain,
    String? iqFacebook,
    String? iqInstagram,
    String? iqOnlineGroup,
    String? iqOnlineWeb,
    bool? aboutUs,
    bool? prize,
    double? iqShowRoomLat,
    double? iqShowRoomLong,
    String? supportPhoneNumber,
  }) {
    return AppStatusModelData(
      androidUrl: androidUrl ?? this.androidUrl,
      iosUrl: iosUrl ?? this.iosUrl,
      huaweiUrl: huaweiUrl ?? this.huaweiUrl,
      latestVersion: latestVersion ?? this.latestVersion,
      forceUpdate: forceUpdate ?? this.forceUpdate,
      maintenance: maintenance ?? this.maintenance,
      lookupDomain: lookupDomain ?? this.lookupDomain,
      aboutUs: aboutUs ?? this.aboutUs,
      prize: prize ?? this.prize,
      iqShowRoomLat: iqShowRoomLat ?? this.iqShowRoomLat,
      iqShowRoomLong: iqShowRoomLong ?? this.iqShowRoomLong,
    );
  }

  @override
  List<Object?> get props {
    return [
      androidUrl,
      iosUrl,
      huaweiUrl,
      latestVersion,
      forceUpdate,
      maintenance,
      lookupDomain,
      aboutUs,
      prize,
      iqShowRoomLat,
      iqShowRoomLong,
    ];
  }
}
