import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../lib/src/core/common/domain/entities/custom_header.dart';
import '../../../../../lib/src/core/utils/managers/http/http_manager.dart';
import '../../../../../lib/src/core/utils/managers/http/http_methods.dart';

abstract class AppStatusDataSource {
  Future<Map<String, dynamic>> getData(CustomHeaders header);
}

class AppStatusDataSourceImpl implements AppStatusDataSource {
  final HttpManager httpManager;

  AppStatusDataSourceImpl({required this.httpManager});

  @override
  Future<Map<String, dynamic>> getData(CustomHeaders header) async {
    final Response response = await httpManager.request(method: HttpMethods.get, path: "/appstatus", headers: header.value);
    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}
