import 'package:dartz/dartz.dart';

import '../../../../lib/src/core/common/data/models/error_model/error_model.dart';
import '../../../../lib/src/core/common/domain/entities/custom_header.dart';
import '../entities/app_status.dart';
import '../repositories/app_status_repository.dart';

class GetAppStatus {
  final AppStatusRepository repository;

  GetAppStatus({required this.repository});

  Future<Either<ErrorModel, AppStatus>> call(CustomHeaders header) async {
    return repository.getAppStatusData(header);
  }
}
