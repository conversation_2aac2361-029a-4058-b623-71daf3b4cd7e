// import 'package:freezed_annotation/freezed_annotation.dart';

// import '../../../../../core/common/data/models/error_model/error_model.dart';
// import '../../../../../core/common/domain/entities/user_entity.dart';

// part 'splash_state.freezed.dart';

// @freezed
// abstract class SplashState with _$SplashState {
//   /// Initial/default state
//   const factory SplashState.initial() = _Initial;

//   /// Data is loading state
//   const factory SplashState.loading() = _Loading;

//   /// success state
//   const factory SplashState.success(UserEntity user) = _Success;

//   /// this state we have when out token expired
//   const factory SplashState.tokenExpired() = _TokenExpired;

//   /// Error when loading data state
//   const factory SplashState.error(ErrorModel? error) = _Error;
// }
