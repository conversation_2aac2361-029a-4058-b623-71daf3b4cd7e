import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

enum TextFieldTypes { email, password, phone, pin, text }

class TextFieldView extends StatelessWidget {
  final TextEditingController textFieldController;
  final String? fieldTitle;
  final EdgeInsets? scrollPadding;
  final int? maxLines;
  final int? maxLength;
  final int? hintMaxLines;
  final int? errorMaxLines;
  final bool? autofocus;
  final TextInputType? keyboardType;
  final bool? obscureText;
  final TextFieldTypes textFieldTypes;
  final InputDecoration? decoration;
  final FocusNode? focusNode;

  const TextFieldView({
    super.key,
    required this.textFieldController,
    required this.textFieldTypes,
    this.decoration,
    this.fieldTitle,
    this.focusNode,
    this.scrollPadding = const EdgeInsets.only(bottom: 200),
    this.maxLines,
    this.maxLength,
    this.keyboardType = TextInputType.text,
    this.autofocus = false,
    this.obscureText = false,
    this.hintMaxLines = 1,
    this.errorMaxLines = 5,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: textFieldController,
      autofocus: autofocus!,
      obscureText: obscureText!,
      keyboardType: keyboardType,
      maxLines: maxLines,
      maxLength: maxLength,
      scrollPadding: scrollPadding!,
      decoration: decoration ?? InputDecoration(hintText: "Write_here".tr()),
      focusNode: focusNode,
    );
  }
}
