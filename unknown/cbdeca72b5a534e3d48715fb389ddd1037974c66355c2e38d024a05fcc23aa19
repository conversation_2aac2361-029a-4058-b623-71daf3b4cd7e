import 'package:flutter/foundation.dart';

/// A utility class for printing colored logs in the console.
///
/// This makes it easier to distinguish different types of logs during debugging.
/// The colors will only appear in terminal outputs that support ANSI color codes.
///
/// Usage:
/// ```dart
/// // Simple usage
/// Log.e('Error message');
/// Log.s('Success message');
/// Log.w('Warning message');
/// Log.i('Info message');
///
/// // Advanced usage
/// Log.custom('Custom message', color: Log.RED, bold: true);
/// ```
class Log {
  // ANSI color codes
  static const String _reset = '\x1B[0m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';

  // Background colors
  static const String _bgRed = '\x1B[41m';
  static const String _bgGreen = '\x1B[42m';
  static const String _bgYellow = '\x1B[43m';
  static const String _bgBlue = '\x1B[44m';

  // Text styles
  static const String _bold = '\x1B[1m';
  static const String _underline = '\x1B[4m';

  /// Prints a debug message in red color.
  ///
  /// Use for errors and critical issues.
  static void error(String message) {
    debugPrint('$_red$message$_reset');
  }

  /// Short alias for error()
  static void e(String message) => error(message);

  /// Prints a debug message in green color.
  ///
  /// Use for successful operations.
  static void success(String message) {
    debugPrint('$_green$message$_reset');
  }

  /// Short alias for success()
  static void s(String message) => success(message);

  /// Prints a debug message in yellow color.
  ///
  /// Use for warnings and cautions.
  static void warning(String message) {
    debugPrint('$_yellow$message$_reset');
  }

  /// Short alias for warning()
  static void w(String message) => warning(message);

  /// Prints a debug message in blue color.
  ///
  /// Use for informational messages.
  static void info(String message) {
    debugPrint('$_blue$message$_reset');
  }

  /// Short alias for info()
  static void i(String message) => info(message);

  /// Prints a debug message in magenta color.
  ///
  /// Use for highlighting important events.
  static void highlight(String message) {
    debugPrint('$_magenta$message$_reset');
  }

  /// Short alias for highlight()
  static void h(String message) => highlight(message);

  /// Prints a debug message in cyan color.
  ///
  /// Use for network or API related logs.
  static void network(String message) {
    debugPrint('$_cyan$message$_reset');
  }

  /// Short alias for network()
  static void n(String message) => network(message);

  /// Prints a debug message in white color.
  ///
  /// Use for general debug messages.
  static void debug(String message) {
    debugPrint('$_white$message$_reset');
  }

  /// Short alias for debug()
  static void d(String message) => debug(message);

  /// Prints a debug message with bold formatting.
  static void bold(String message) {
    debugPrint('$_bold$message$_reset');
  }

  /// Prints a debug message with underline formatting.
  static void underline(String message) {
    debugPrint('$_underline$message$_reset');
  }

  /// Prints a debug message with red background.
  static void bgRed(String message) {
    debugPrint('$_bgRed$message$_reset');
  }

  /// Prints a debug message with green background.
  static void bgGreen(String message) {
    debugPrint('$_bgGreen$message$_reset');
  }

  /// Prints a debug message with yellow background.
  static void bgYellow(String message) {
    debugPrint('$_bgYellow$message$_reset');
  }

  /// Prints a debug message with blue background.
  static void bgBlue(String message) {
    debugPrint('$_bgBlue$message$_reset');
  }

  /// Prints a debug message with custom formatting.
  ///
  /// Example:
  /// ```dart
  /// Log.custom('My message', color: Log.redColor, bold: true);
  /// ```
  static void custom(String message,
      {String? color, bool bold = false, bool underline = false}) {
    String formattedMessage = message;

    if (bold) {
      formattedMessage = '$_bold$formattedMessage';
    }

    if (underline) {
      formattedMessage = '$_underline$formattedMessage';
    }

    if (color != null) {
      formattedMessage = '$color$formattedMessage';
    }

    debugPrint('$formattedMessage$_reset');
  }

  // Color constants for use with the custom method
  static const String redColor = _red;
  static const String greenColor = _green;
  static const String yellowColor = _yellow;
  static const String blueColor = _blue;
  static const String magentaColor = _magenta;
  static const String cyanColor = _cyan;
  static const String whiteColor = _white;
  static const String bgRedColor = _bgRed;
  static const String bgGreenColor = _bgGreen;
  static const String bgYellowColor = _bgYellow;
  static const String bgBlueColor = _bgBlue;
  static const String boldStyle = _bold;
  static const String underlineStyle = _underline;
  static const String resetCode = _reset;

  /// Prints a Dio logger message with appropriate colors based on content
  ///
  /// This method is specifically designed to work with PrettyDioLogger
  /// and will color different parts of the log based on their content.
  static void dio(Object message) {
    if (kDebugMode) {
      final String text = message.toString();
      String color = _white; // Default color

      // Determine the appropriate color based on the content
      if (text.contains('Request ║')) {
        color = _cyan;
      } else if (text.contains('Response ║')) {
        color = _green;
      } else if (text.contains('DioError') || text.contains('Error')) {
        color = _red;
      } else if (text.contains('╔ Body') || text.contains('╔ Headers')) {
        color = _yellow;
      } else if (text.contains('"status":') || text.contains('"data":')) {
        color = _magenta;
      } else if (text.contains('║ {') || text.contains('║ [')) {
        color = _blue;
      }

      // For multi-line logs, we need to add color codes to each line
      final List<String> lines = text.split('\n');
      if (lines.length > 1) {
        // Add color to each line and join them back with newlines
        final coloredLines =
            lines.map((line) => '$color$line$_reset').join('\n');
        debugPrint(coloredLines);
      } else {
        // Single line log
        debugPrint('$color$text$_reset');
      }
    }
  }
}
