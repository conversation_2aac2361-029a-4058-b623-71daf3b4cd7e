import 'package:dio/dio.dart';

enum ReachabilityStatus { reachable, unreachable }

abstract class CheckEndpointReachability {
  Future<ReachabilityStatus> check(String endpoint);
}

class CheckEndpointReachabilityImpl implements CheckEndpointReachability {
  late final Dio _dio;

  CheckEndpointReachabilityImpl() {
    _dio = Dio(
      BaseOptions(
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          "charset": "utf-8",
          "Accept-Charset": "utf-8",
          'Accept-Language': 'en',
          'X-Channel': 'mobile'
        },
        responseType: ResponseType.plain,
        receiveDataWhenStatusError: true,
        connectTimeout: const Duration(seconds: 15), // 10 seconds
        receiveTimeout: const Duration(seconds: 15), // 10 seconds
        sendTimeout: const Duration(seconds: 15), // 10 seconds
      ),
    );
  }

  @override
  Future<ReachabilityStatus> check(String endpoint) async {
    try {
      await _dio.get(endpoint);

      return ReachabilityStatus.reachable;
    } catch (_) {
      return ReachabilityStatus.unreachable;
    }
  }
}
