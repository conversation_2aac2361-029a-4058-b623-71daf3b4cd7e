import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../injection.dart';
import '../../utils/managers/firebase_services_engine/services_engine.dart';

class CardView extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? color;
  final double elevation;
  final String? semanticLabelValue;
  final double borderRadius;

  const CardView({
    this.onPressed,
    required this.child,
    super.key,
    this.color,
    this.semanticLabelValue,
    this.elevation = 2,
    this.borderRadius = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onPressed != null
          ? () {
              serviceLocator<ServicesEngine>().logEventFbAnalytics("CardView", {
                'onClick': {semanticLabelValue.toString(): ""},
              });
              onPressed!();
            }
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: color ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.14),
              blurRadius: 5,
              spreadRadius: 0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        padding: EdgeInsets.all(16),
        child: child,
      ),
    );
  }
}
