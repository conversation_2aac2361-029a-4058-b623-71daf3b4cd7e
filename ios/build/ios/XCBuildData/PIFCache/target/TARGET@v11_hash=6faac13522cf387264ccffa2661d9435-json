{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9858523819b5d1f88f45af21fac3651238", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890819a170250710907b804bfa1ff2312", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890819a170250710907b804bfa1ff2312", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd9c4641d627ca48ef40d8445f26ac75", "guid": "bfdfe7dc352907fc980b868725387e98f796ae3b296b723f56e05baba122c560", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5296b28358fae7c437b1b67d0d7c4d1", "guid": "bfdfe7dc352907fc980b868725387e980db98772e6111672a1951845b9fe4498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a34a301ca272e109f2e2db5cdcd651a", "guid": "bfdfe7dc352907fc980b868725387e98d995cd42abf9537adeb9ef2b0074d4a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985482547cfa435d88fdb8cc5d0af25cbe", "guid": "bfdfe7dc352907fc980b868725387e98d3a81096cb1a49bc36d5521745724604", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0474c0414f5e6bfb857fcf2ac3ad086", "guid": "bfdfe7dc352907fc980b868725387e985ef063b1ef9ee9c4ca6a668e59fa1f98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98027759b457fe2bc2f2f9f1e3cf2561f4", "guid": "bfdfe7dc352907fc980b868725387e9842b8c36aac900f2b53ce2f2427f15694", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b6c20b22e9591d0bf02f7caa757671", "guid": "bfdfe7dc352907fc980b868725387e98053e6ee45256d0913044f0b250d63f20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01e3d98bc2ab433e5f36346b18f36cc", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309bb04874555314e0112b8db9e6afd3", "guid": "bfdfe7dc352907fc980b868725387e98ea20a8b7ee82b71c8c6e77a920789ce9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab8bea2a54325e50a0886336d11c113", "guid": "bfdfe7dc352907fc980b868725387e9890f87a76cd0165ba7fb47409116b7bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856afc484a60870aea00e6c92ff7de0d0", "guid": "bfdfe7dc352907fc980b868725387e98076c4bb8b39664db5729643eeeffd3a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea93b6d7e09b9e10cf0f1d058e0fdbac", "guid": "bfdfe7dc352907fc980b868725387e98d3f60b57b9b70acd8be55663558637c6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889dae8dabdf427f0c739f5296dc89f19", "guid": "bfdfe7dc352907fc980b868725387e98a86c0926d2b2e8154aeb7b9a54324b37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4f742edb214b1127bd6972606a23d3", "guid": "bfdfe7dc352907fc980b868725387e98dc94e8e9e41b5cd9ce1ef745a5d3f6aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895018cacac32dbbe7b348f7fbabf7edb", "guid": "bfdfe7dc352907fc980b868725387e980148653b3c31ab69678aa137ee2bb129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984185174f1063177127ed5fc5e41c9311", "guid": "bfdfe7dc352907fc980b868725387e984b8a1494c80ebe33f2f9896061700fd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aef15f2cadeafeef0e325db97b40f62", "guid": "bfdfe7dc352907fc980b868725387e9888152ab1364126b74b5d2906f7b0ae7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea43b8008d97c91047df4702f9e71a9", "guid": "bfdfe7dc352907fc980b868725387e9823f761480bdf14780e4773ac9a5d8370"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b98a6acda3b8fb385121d1c642a613c", "guid": "bfdfe7dc352907fc980b868725387e98d51170c14d43a831e9310a9d05e69517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987910602c174a7170e6dee3c43b246045", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beaeeeefa506759633e04555b0d37a67", "guid": "bfdfe7dc352907fc980b868725387e98ca57b60f855bf59e9205fca17ec155a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663b2c258aeff6562b14811e6227ee85", "guid": "bfdfe7dc352907fc980b868725387e982a8d5b85200b39246535b2cf0682ec06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de532d51c066f62df9e1ec36d7a377aa", "guid": "bfdfe7dc352907fc980b868725387e98f482f32cf56037032a9c2aa35ab8610c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db9c0e6af1dd42cbc43f2db2fe4c1427", "guid": "bfdfe7dc352907fc980b868725387e98f8838aeb4d8d9908a58c1bfb7603c9c9"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}