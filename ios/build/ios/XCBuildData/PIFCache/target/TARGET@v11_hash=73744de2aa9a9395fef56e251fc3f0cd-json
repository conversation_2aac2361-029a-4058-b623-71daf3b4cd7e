{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98491d793627afb6864820155e30bc65f6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9884c5620a4c44fbece80d930bca0bd781", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e00e60a8f74082b9d54b4af9203bb25", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811d94d6de7c58934ed44be60b889f773", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e00e60a8f74082b9d54b4af9203bb25", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fa2b5a65d7d60b1da015b765e26742a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984069f5cdead316c5b6d214d546625e0f", "guid": "bfdfe7dc352907fc980b868725387e98dd1733c4d72750a2fe17b1071e096470"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da8bb00bf4b7145345acdcbd2514c9d5", "guid": "bfdfe7dc352907fc980b868725387e985584dbd36b45ad0ab31e164b2602251f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882aaecb0614f35b9efeb4247152e2ade", "guid": "bfdfe7dc352907fc980b868725387e9888c4700f050b7a945d64bcccca407eaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086e865bf62404d373caa614c63b1b79", "guid": "bfdfe7dc352907fc980b868725387e98f6fc10191e25ac00c320a08fdc461e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1f4827fb5503f55b75b0efbb185fa5", "guid": "bfdfe7dc352907fc980b868725387e98229442323c8c94ca9f7cfd63321accc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c45360776abd332ee4654baae2a57e", "guid": "bfdfe7dc352907fc980b868725387e984dc77acc36faf0f527d2f2c5c33d46de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c8966ac3e43264fcf7453b9425feec", "guid": "bfdfe7dc352907fc980b868725387e98ab06ba2a0478c2b78a63390a8c7c4b75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad215796c49d91710ee1eb0cf14d45c6", "guid": "bfdfe7dc352907fc980b868725387e983aaf259eec88d16ff280d2167201ef4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c3213bf8c915066385a27f643a94776", "guid": "bfdfe7dc352907fc980b868725387e98658e0ecc36f218998accf85d3dc17a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875cee72e6ae8288f2caa00710d25f729", "guid": "bfdfe7dc352907fc980b868725387e983c25fe984ea1ba0d69ad30bb765c87c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869137f380c40072cfd9be185f0bd368c", "guid": "bfdfe7dc352907fc980b868725387e9831fb312af222560ec56ac7855ec6cf3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e212856077a2f592b8272c2ac9d0ece7", "guid": "bfdfe7dc352907fc980b868725387e98dfb770e99abf3b49b97b3871fb1c36f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6145b9cf27491e9d63285545c36f810", "guid": "bfdfe7dc352907fc980b868725387e98e76207c3da4b7f91eb04eed836f49f0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980213dc8005cbdbd4c7126ee95d479be7", "guid": "bfdfe7dc352907fc980b868725387e98715a6f8048738a06517329f6aef15fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816efb46a45d85bc6abf357b1b9aa6cdf", "guid": "bfdfe7dc352907fc980b868725387e98f82c232f002a261e21626bd12549c68c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988223154abe75fffb6c188b3977b55005", "guid": "bfdfe7dc352907fc980b868725387e98d5b489388d50e98c841d8dababa39337", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cda451b5ebfd8f169877ea7091e6d44", "guid": "bfdfe7dc352907fc980b868725387e98b07ea3bc67521ded638a9713368b02a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8a47ae1741e63a8c1f776b7a063366", "guid": "bfdfe7dc352907fc980b868725387e989fad30ecca62cf0fc1022178afb6af41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830e35519868f0911d3cd328a309467eb", "guid": "bfdfe7dc352907fc980b868725387e98cfe8977081816c9a27431c81c0bb34b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4663a5b037ff0fd0d7ba21dd28478c6", "guid": "bfdfe7dc352907fc980b868725387e985077519a187e7c61776d8306962a68f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba9e19923a21fad5bb1d8ad869b39bd", "guid": "bfdfe7dc352907fc980b868725387e98c3a0a663c35adabcf367844b16142563"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98531976f2b78dd5d00c59b821fcf786de", "guid": "bfdfe7dc352907fc980b868725387e98580c0c58aa8ed880ba9730d958fb918a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347a729b076e3da2a9904001f5cdbd79", "guid": "bfdfe7dc352907fc980b868725387e9840ebd632a421b1ed8f2d1db0ac79bd71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898b03cd80068475f376a47df66663941", "guid": "bfdfe7dc352907fc980b868725387e9860d480191ffddbbff072368fe4904e50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2a9361365eb5a4adbbea5c3966a481", "guid": "bfdfe7dc352907fc980b868725387e9874bf838b3fa0729f175b989af235d58a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a054de753affbf8db2f2518c9aa2ef", "guid": "bfdfe7dc352907fc980b868725387e98be4f77824169ad69448954fa42f6f75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b51cd63effd3cbffd8f0e65a9fb0e3", "guid": "bfdfe7dc352907fc980b868725387e98a10f3f9fa1b8ba267be7b9dd1b4152e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982398bcf2db7419621583342734bc1254", "guid": "bfdfe7dc352907fc980b868725387e98e8d18707471cac431b9f144f7a96babf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcdb7cd7e4f3edc525cad61bc4673992", "guid": "bfdfe7dc352907fc980b868725387e9869e4cc15cca8d2db8482c83582c910fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8266ba4cfa9b6d6bbc561825c40ff06", "guid": "bfdfe7dc352907fc980b868725387e98a1cb054074ce3950f31bca23b0ebe664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2912e8b1dc7f31422ac766a3b7b4b7", "guid": "bfdfe7dc352907fc980b868725387e98e5a0fa2e4955d1dbbc2f4977293be979"}], "guid": "bfdfe7dc352907fc980b868725387e983eb22f12984174f4b9a6943923d98ba0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981c18b766c4efbed1d8fb617af1b85cd0", "guid": "bfdfe7dc352907fc980b868725387e98c6d5e8b39dd107aca0947cb5a566ef84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c00276cd133d879474f9694600884ae3", "guid": "bfdfe7dc352907fc980b868725387e98d8c14bc4dc07a09f911ac1d9611a13dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f3f8bbba0d19215232a6d0a7e269a66", "guid": "bfdfe7dc352907fc980b868725387e98e3fcff7654473d0867f188855c0c8cbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98519418f6da454bf98281a1d49aff9f15", "guid": "bfdfe7dc352907fc980b868725387e9830044088c4e24d69008b8d957798f43d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98035716a77c3216fbf4b2fd05603971d2", "guid": "bfdfe7dc352907fc980b868725387e9844eb699df944a0db9e77a900f7cf32e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98065dd34714f5419b4f817b02264e337d", "guid": "bfdfe7dc352907fc980b868725387e987619b82397844c1a2ea7147b2c796637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc80e0101d6608e3aef3ed47496f16a6", "guid": "bfdfe7dc352907fc980b868725387e9881d7ca9541d777ea100969873fc928f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b79f875b7e9acef38be6480665ac48d9", "guid": "bfdfe7dc352907fc980b868725387e981eac705c873a06db9bb44fc15db4b4be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989546436303ee0e5548aa737ae81ff371", "guid": "bfdfe7dc352907fc980b868725387e98113ff82b4ef9638c512a8acbef2f841a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7298f2cac1cdb3e26d3206ca6b554b1", "guid": "bfdfe7dc352907fc980b868725387e98fcdd8f3a999872c20630bc4e4ac62d1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846bb69f0b3f377dfde43323d65847c39", "guid": "bfdfe7dc352907fc980b868725387e988ea99c484707236bcc8a9333c4b507d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98535f4ba7e6003a0bfa6f5984617eb803", "guid": "bfdfe7dc352907fc980b868725387e98fd5bf7fe715cb40138f4a6e9d09e45e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e63b6005515a654ef815e856c93e9de", "guid": "bfdfe7dc352907fc980b868725387e985b940bff9b922c3e542fa13abf3ceae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bcb3c9197288db13e07c4fa957e804c", "guid": "bfdfe7dc352907fc980b868725387e98f49d54954d0ee2a835b6001d609ea79a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f728cc90a7939abe8cdd069fed790bd1", "guid": "bfdfe7dc352907fc980b868725387e981ac7c2fccd188d5b2afd838b58981052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866688417e6862c28839f9de486bc8f3b", "guid": "bfdfe7dc352907fc980b868725387e98a4913de026bf68e4faff54e40ade503d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985285bb4ee1de023a00c6e118f7527530", "guid": "bfdfe7dc352907fc980b868725387e98c8d554d4f82f5feb4d1be42cf60d6aa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a2d1f705ac1ce7546dc38626620c22", "guid": "bfdfe7dc352907fc980b868725387e98c27cfe020ffc4899cc99d396550623dc"}], "guid": "bfdfe7dc352907fc980b868725387e984796e6f1b26f58ef20826e3beed13f4a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98396643ab5cefe765f6f5d4377e3639e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e984bb54875f6b1b7574dcc03994b656dd4"}], "guid": "bfdfe7dc352907fc980b868725387e983a2c2f32f085d06dccbccf842e80fad6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987126ab9fbb8883a0c47168b401cd2d4a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98ab569b4c5129d907f2f90f0b8ebe35bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}