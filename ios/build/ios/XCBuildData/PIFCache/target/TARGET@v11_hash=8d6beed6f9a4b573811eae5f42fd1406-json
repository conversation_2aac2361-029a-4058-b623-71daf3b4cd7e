{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831cfb849e4d9bdc94e7ba2ebc196e50d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98728e628e958e143919ccfcc7d861fdbd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f059ba406625a7badba3fb97ed1c3a8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810b12757158c848c9356834d932ea17c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f059ba406625a7badba3fb97ed1c3a8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800571886a3cf21a0d144aff6a0bc65c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c2d45ebd5041478bd6fad738e9bda7a7", "guid": "bfdfe7dc352907fc980b868725387e98ce010b3853911077615d9e003b11de38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812bb38de7510f94a3c4797702255740f", "guid": "bfdfe7dc352907fc980b868725387e987b00e2dbeb9b0114eb0a75b63ae3805b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3345a561ae0de8f8cd8090edd2e2d7", "guid": "bfdfe7dc352907fc980b868725387e9852b619c158f2dff14318bb3a32190fb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac1706792bae3dfc0a79f52704937812", "guid": "bfdfe7dc352907fc980b868725387e98e12119391e8393d82fffa3bb9bfef4c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babed8a6b9ae6f7317b7a37f30f8d40a", "guid": "bfdfe7dc352907fc980b868725387e981a0900f35c9aa00816407e05bf520345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579e90bbc485706de4c95dbdc608f67e", "guid": "bfdfe7dc352907fc980b868725387e9805afa0b05d473b16fd53b5032fb164d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5278bd523f9c17b71f732221bfc4c10", "guid": "bfdfe7dc352907fc980b868725387e98f1ff2ab1dadc05f4fee601339f1ed5bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984049e4680f8479acaa1216c809700856", "guid": "bfdfe7dc352907fc980b868725387e98f4ceb829443119eba139a5382b07bd98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980243a5da6b37be9044a4824a8a52ca62", "guid": "bfdfe7dc352907fc980b868725387e9849ed469d3e9cde8cbcefefc9d3c875e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a61af77db299f1f92eea56025d5e0b", "guid": "bfdfe7dc352907fc980b868725387e98549c444af5b588795c47eed77f0ee06f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ebdfabd2d5bc3b715d4fba6e526449a", "guid": "bfdfe7dc352907fc980b868725387e9875a5dd2a87ff82ed5861d1796d20af9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c36d1725ed7daa7ebf37ed2c3312481", "guid": "bfdfe7dc352907fc980b868725387e98756f43fbc3664bdc461c2ba74c1f8052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883961844b054f60d3952d52325368f25", "guid": "bfdfe7dc352907fc980b868725387e98fbb30c15e5ca8b99101a3db8176ebc08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c44df285239ea185a178f9e66f7fc86", "guid": "bfdfe7dc352907fc980b868725387e98c29d87690977c8953e3eca3acf744373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce4f9f7a4ed6c65c522ada84c544f72", "guid": "bfdfe7dc352907fc980b868725387e981dfc1d3a126ee862a1a4d128b882f48f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a43e6122aa6a76660135658a52c8ff0f", "guid": "bfdfe7dc352907fc980b868725387e98cde1401c34fbbbfb31c82e1363446530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e7b0fe38b171a8314e09c580b0d195b", "guid": "bfdfe7dc352907fc980b868725387e98a46659e4f8e6b3d652867c1f0e6fa1bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc7d381c160c3be2f80f2e87b2225bc", "guid": "bfdfe7dc352907fc980b868725387e98f510fb4c70552231154352e9d8c050ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be6749f3d6d98a48b69e9b2d0969df8b", "guid": "bfdfe7dc352907fc980b868725387e98e9076c50663ed0853b2079693cb18e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c1c32b59aa4eeb8096670ebea53c4d", "guid": "bfdfe7dc352907fc980b868725387e982b865025ca3042272f4a2b62a83d8101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e75769b318a2dd801e587736957069", "guid": "bfdfe7dc352907fc980b868725387e982bcef44568adcaaeee0b04b08aa6a87d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c020a6f5f00f919f5748b4df5393912", "guid": "bfdfe7dc352907fc980b868725387e9830a74c3f5aac1dcfadf0774172812499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981729eac265b8a22f8d0534e44f62d7f4", "guid": "bfdfe7dc352907fc980b868725387e987633d20e26b53f2d30581dd9cad1fd27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1bae27a91bc70f11f0a7dfe551651a", "guid": "bfdfe7dc352907fc980b868725387e9875080c401a7912bc228257d40a7f4a95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c80893c61f82300f1d0c44f6eb9d09", "guid": "bfdfe7dc352907fc980b868725387e9856626bd64a690f44d04738d8788c08e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f819786ff97341a468b3e4b99dd3891", "guid": "bfdfe7dc352907fc980b868725387e98a0885e251db40af583797c43630257da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981744a5298c8d2e2a910ca8b2df4bf01a", "guid": "bfdfe7dc352907fc980b868725387e98871204c5b2ac2b2c2d302228006107a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2886397d182bfa28711f5c3314ed5d", "guid": "bfdfe7dc352907fc980b868725387e98b6b7ca27488a1f8654b81aff4fbc3cc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872503d0cf0346a068841cc1e8904f243", "guid": "bfdfe7dc352907fc980b868725387e98827d6d28640a091dfc511eacda945e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a32ab1927bd7e66776bb36307eb1dd4", "guid": "bfdfe7dc352907fc980b868725387e98f665e8c29b99e15fd7ef0cebec88c934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc10c68f645c35ee3ae60392ae507b9", "guid": "bfdfe7dc352907fc980b868725387e985c06cf507124832f6c8deb3f83a2e95b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981244cf71bad2565d430cb4c86b31196e", "guid": "bfdfe7dc352907fc980b868725387e981e5bd287f1c167e988740e627356dfee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff9fc1ca78afd22259291542861284cc", "guid": "bfdfe7dc352907fc980b868725387e9825d9eb272a28a6683da0801fc82dcf93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98723b80e8b8c27e677bd6d83d03a4fb93", "guid": "bfdfe7dc352907fc980b868725387e9817007161d7ccfe5c7e2d4e8483979f92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64ea23797241d2f0f4dfbc2ad583ade", "guid": "bfdfe7dc352907fc980b868725387e985b32aa333b9e1d4aa63544de9693105d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813bd905a34f35954875cfdf4ab3b5f14", "guid": "bfdfe7dc352907fc980b868725387e98d02cd9c05a31c901bfe6fbfe38308490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb29b71d71563e78a48dfeedab45c140", "guid": "bfdfe7dc352907fc980b868725387e982911c9b437dffa85587b93d1f04f87ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb7de09159dd153919cbf84b6aa73c54", "guid": "bfdfe7dc352907fc980b868725387e98cfa09d0d4d049c52a9c2563ba5aada59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5c7556767a2d58943f92a04c66b085", "guid": "bfdfe7dc352907fc980b868725387e9814838c13732f232ef1c0c5c5a601399d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d40f952f127cb0b1217fdf1975349a", "guid": "bfdfe7dc352907fc980b868725387e987e91b3dfb3bd876f7a7585cfc0e81f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4294c62aa2e3e51d474a12a82e9193", "guid": "bfdfe7dc352907fc980b868725387e984da95cd2da966ee1466af9329ab7c4ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980235b405e18d823cb85500a1f0ad5595", "guid": "bfdfe7dc352907fc980b868725387e98953e102c17772e5666c292de712342f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c990d74554df3048c891bfac117d95", "guid": "bfdfe7dc352907fc980b868725387e98cdf7b4f846a2083d1cf07e77acbec0ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9780e9546bd75e1849dfc30014d2eb0", "guid": "bfdfe7dc352907fc980b868725387e98e264b2d22acb8621551efee6ab36b8f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98502ee2d9ed658d4485e25149980cbe04", "guid": "bfdfe7dc352907fc980b868725387e9832b7e60c38198a338105521c13354445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983937c7f3cf5eee17459276a7581da75b", "guid": "bfdfe7dc352907fc980b868725387e98a4f9570b6b96e223291395715eab12eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98050357a556fe33520ad4ec0cbe7e8f73", "guid": "bfdfe7dc352907fc980b868725387e98c63095f9461198157a1a29eefd771adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb3a8e1eb363ba26ebf44a4eb3324513", "guid": "bfdfe7dc352907fc980b868725387e986dac0844a378208a0d7ca081b3c6f47e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd283b8e47bad2384c86186c2a8c0c0", "guid": "bfdfe7dc352907fc980b868725387e9830c1666079c41dc520912e7bb72d76be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b543d2628ecceabb98027e3530e2dd", "guid": "bfdfe7dc352907fc980b868725387e98a26bbcab4957124b6cb302809f133070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aaade68d17ce589c885f65f53de0e25", "guid": "bfdfe7dc352907fc980b868725387e987bb43c0b57897189aade1aab689eb37d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859fa6e3f2e313a0f736e3e6028dc5d80", "guid": "bfdfe7dc352907fc980b868725387e98d91e82d2d18a95f227c8179535b63fdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7428d36c7f1fa7fe8b9ff9b96c545b1", "guid": "bfdfe7dc352907fc980b868725387e98c2b2734b861b9ef01e964ce100dfc303"}], "guid": "bfdfe7dc352907fc980b868725387e98de701da15d498c1f6bdc2996f6519a05", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f9956763c9a8a443788f2ffa770e0455", "guid": "bfdfe7dc352907fc980b868725387e980b08f27c8a711c872bb61690e3c8b264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7eb4fef37ce8ffc528c6f4007d7fc18", "guid": "bfdfe7dc352907fc980b868725387e98b399499b395b01d0b11eb264e0d039cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879dabbc30ea855ef7f756ea419a948fe", "guid": "bfdfe7dc352907fc980b868725387e985b3c24767bc9045ef1121f84457f8c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d462616595714a25d9dd55b5cda679d", "guid": "bfdfe7dc352907fc980b868725387e98ca8fd52d786368216840fa6eb1a7c230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d52871410926e2631d0a95910c5d0bf1", "guid": "bfdfe7dc352907fc980b868725387e986596e056a528b4c1d3bf94909ac7062e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec44115a526e7d1ee722439b18564a18", "guid": "bfdfe7dc352907fc980b868725387e98431bfa42069dc8bb66280d46bced772e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c471cd5891520e57bc26afacaeb9c44f", "guid": "bfdfe7dc352907fc980b868725387e987cdab2061bbf7836477d8b0a58c92451"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988591a17a3130402ab175a9675db502af", "guid": "bfdfe7dc352907fc980b868725387e98ea6ad1827229a6f847f412bcfe3a10d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbb4df5b603f6454f819b0102a958de2", "guid": "bfdfe7dc352907fc980b868725387e9868d4d3fffb6fabcc38607a3a576ffdcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5b7c80bd3663188b8aa4be7e1eb3e8", "guid": "bfdfe7dc352907fc980b868725387e98ee31547b261017ac887b6201bd91ae23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ffeba73d8b4f69a0dbd88e70d8f294", "guid": "bfdfe7dc352907fc980b868725387e98d2eab434ee4382b40b9adab388980281"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3edfc0442c6ab00446c6d905ae73a9", "guid": "bfdfe7dc352907fc980b868725387e985390c40daa2e28bf05769515ca9c4334"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7aa39813c25a6c2f220f95785c4c2af", "guid": "bfdfe7dc352907fc980b868725387e9868e4136f19f33b993ae28e53ab70d55b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a0b8e94d8b9007e79f52dac572933ba", "guid": "bfdfe7dc352907fc980b868725387e98683ac36824c3f3b00ca96134a9df0d8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980266f1bd625fdd2adfed52a4f97ac165", "guid": "bfdfe7dc352907fc980b868725387e984ebf61733df75ced83a6413acc996e2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814104084aa4d8da7645255c09968977f", "guid": "bfdfe7dc352907fc980b868725387e98dad02a2358c75d20db1db9984939a63c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa58e17743b8618fff1661e3416164b9", "guid": "bfdfe7dc352907fc980b868725387e980407599c2d1b9b64aa3b55f55c00942c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98244ae9b8e194f1ae70189270ed84478b", "guid": "bfdfe7dc352907fc980b868725387e9842f6535774480fa6c95aff37c01403e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e740b03b5e9b74f542780d4da96d2e9", "guid": "bfdfe7dc352907fc980b868725387e9888e92c6184567f2edf90e803de4f0ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c14c0138de33df6da9bf0553648454", "guid": "bfdfe7dc352907fc980b868725387e985b239b98cba6973f4bb6bf62b19df0bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef7c8f83a1fd9efc8c6eee6f3333e105", "guid": "bfdfe7dc352907fc980b868725387e98aa8a17d7a58053a64eb0406931fb4bca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af629b4326537e4f1b0106b88e62f88", "guid": "bfdfe7dc352907fc980b868725387e98f114657d99f392abd0ec24bf184a5260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830aa4bedec611addf454f9e5a16a9960", "guid": "bfdfe7dc352907fc980b868725387e98d9cb8b200e0c427f29195889bbfa9157"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844dc32a75e1691edc62276aee2b6cee3", "guid": "bfdfe7dc352907fc980b868725387e988cd3bf726c8e9983a0769addff3a47f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad478d301689f65228d4af011ef91910", "guid": "bfdfe7dc352907fc980b868725387e9818bedc1eb00c391f771e00ef4defa0c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988916eecaa4a519630046063ee7332022", "guid": "bfdfe7dc352907fc980b868725387e9841cca3d0c2ddf1f27b72218d5b9cd1f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecb65d47554d2ee62ca006e26c918a96", "guid": "bfdfe7dc352907fc980b868725387e98548bd132029b78afc563210fd4aaf439"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5aa86a2d5502462ed1b50135a27b6f", "guid": "bfdfe7dc352907fc980b868725387e9852052f7bc5cd9822b4b0f76b3ba82306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b098f27597f4138e4413392764ddda", "guid": "bfdfe7dc352907fc980b868725387e98e290686ca3faae185f3c83c2fc8db4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8c6779186029986d091f8f11b9a258", "guid": "bfdfe7dc352907fc980b868725387e98050dcda623401bfb813dd33d0294d876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4e801c1f28711375cce4f17f25b1dff", "guid": "bfdfe7dc352907fc980b868725387e98b7824d72181c636ae1db401a62dc6d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f58565ed94bfc9a19b1df5546baf36", "guid": "bfdfe7dc352907fc980b868725387e987b98712ca95f07a727f6b231b38b3e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985925bb0e21fe7820515a13c9324c9b39", "guid": "bfdfe7dc352907fc980b868725387e988e49edab40058730f50516b59ec3ea6d"}], "guid": "bfdfe7dc352907fc980b868725387e98121e9c97e7b8cef398caa1441010159a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98caffccc4ed4b155972d47dd99685da69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e9847d0123a193cf118c231b522056ccade"}], "guid": "bfdfe7dc352907fc980b868725387e9822d6d2aae7302bdd9b788386273b78a8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983c78638a007104fc38df7aafacfb9df6", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98f57a72c5391e4b17d016337e204a1d96", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}