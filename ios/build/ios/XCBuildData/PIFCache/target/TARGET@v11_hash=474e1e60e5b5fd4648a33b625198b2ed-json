{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98079e475f5df58a2ce298cb00b29dcbf6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832cb808c83d79f00d8616a375daf7b26", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833640b41e1b40650d06b87cea691ed53", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98025427438aff189e1ff50a132648efdf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833640b41e1b40650d06b87cea691ed53", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd940fc009a6595506e556bd5670897e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863026696c2ed13ce3b061689d64d9cb9", "guid": "bfdfe7dc352907fc980b868725387e98e9e9a21c33a256b3dfd52d90369131be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee870019844cc9cb4081fd4eec5820d0", "guid": "bfdfe7dc352907fc980b868725387e982041433cb299ac9c1ec50a873a256750", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7baf3e21b4541d502e7e5a02376ce6a", "guid": "bfdfe7dc352907fc980b868725387e98b5a8a7973e80264872fd91cd85ca39e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d574c805248f70ae140498aaf39d77d8", "guid": "bfdfe7dc352907fc980b868725387e982bbc2ee114ae4dc188f0417d64521849", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894876989d7699bb38d2d1a7491d59070", "guid": "bfdfe7dc352907fc980b868725387e988fde96fda2794b3cf4c1922e996ac9e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ad501fec61effd96afe0b2426c48c9", "guid": "bfdfe7dc352907fc980b868725387e98939b8e4c89c6eae325311dcc128c94ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a2b510d22702815389d4898dfe23ec6", "guid": "bfdfe7dc352907fc980b868725387e9827c606c7ade29e04c15a77170be96bda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e00a7afe6ec8f009fefa1b2af425b52", "guid": "bfdfe7dc352907fc980b868725387e98c2e743bada98854129face9fb91235d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c7f415560c6e160f4c5d6d1a82256e6", "guid": "bfdfe7dc352907fc980b868725387e986eeb8db5776c4575a9b41f70b9d8b1d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98946b5658cbd59270012ba15fe1a2145c", "guid": "bfdfe7dc352907fc980b868725387e98f4a7bec35dda32fa4e031b53dc3a0d88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d9aa4fd15cededae251fc283f0ff29b", "guid": "bfdfe7dc352907fc980b868725387e98a25e1b0f325f29225b8ea60b01eeda5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988135e2e1958a79b0f483032fd6220ef4", "guid": "bfdfe7dc352907fc980b868725387e985b73c719c2d0ed4097081e419a374c85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843effc73509ef2a413fe6bf98473925f", "guid": "bfdfe7dc352907fc980b868725387e984703c886ef8dd90de81f119e1a98a780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986797b08a06abc50a496d0b063a508f8c", "guid": "bfdfe7dc352907fc980b868725387e98a93d3eecae22e34b5a1e3419f5bffebc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98235e96eafa8ad2f2acb804043955d5b0", "guid": "bfdfe7dc352907fc980b868725387e98a5d84d166c6309a1b0c6a9278d2bd98a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf195cb67e7991338fdc48f67506b05c", "guid": "bfdfe7dc352907fc980b868725387e987c0328eefe095581be132750941d2ea6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989464bd9011fad7a533a3addeb089d857", "guid": "bfdfe7dc352907fc980b868725387e98159b621db60b94e135363e43caf1b220", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de690015a00759e5f2311eea049607b9", "guid": "bfdfe7dc352907fc980b868725387e98cf256248fb449771f507ac1386a8e920", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cfc8dca0f998f3665d4608be392416c", "guid": "bfdfe7dc352907fc980b868725387e9875918a5d06c6da5e4e4104ae4227418c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a836d101ce78146ba3455afa165ea97", "guid": "bfdfe7dc352907fc980b868725387e98a8492169074169a18e13f4a8826fd80c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497cac12b2be14586caf18c623abf0e8", "guid": "bfdfe7dc352907fc980b868725387e98f4e6c681abc0ca178594819515ef0921", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea37d8cf87eab2c3aebcf32342a2dbf2", "guid": "bfdfe7dc352907fc980b868725387e986a449de3f0fa9edb0589f4df95870d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e365262aedf1ae1bb7bb8939cecf6b", "guid": "bfdfe7dc352907fc980b868725387e98f42310e6040a430fee3f0be1213c3cbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d28ff7ad098134e452f01fc40e453d", "guid": "bfdfe7dc352907fc980b868725387e98a2a84b2a62ffd1a3a5455bc5eb6d77a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c090793e79247fae22472577c72506ca", "guid": "bfdfe7dc352907fc980b868725387e98fa78ede52c00707234bdaecca5873e4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892c8f707c19538a291d9bf41af7b08c6", "guid": "bfdfe7dc352907fc980b868725387e986ed730adfc23baf10188de0bd0b8ae7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f724f1181d32eca98da2e4d37872edf", "guid": "bfdfe7dc352907fc980b868725387e980792ea648370793867afec74f88ee917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645d928c566c567b7b025afe108f9a37", "guid": "bfdfe7dc352907fc980b868725387e98f53bd514da76501727ff74522c2b0f85"}], "guid": "bfdfe7dc352907fc980b868725387e984882f53962425f9fd045506d874ba0ab", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c833cd8e090ed02db61addb607834043", "guid": "bfdfe7dc352907fc980b868725387e98c5949ab7166e833d15bba920f94a343a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987742b2a95604b2c5e0b6b5ba70774784", "guid": "bfdfe7dc352907fc980b868725387e98d5ec08bcf7db1758684751c4e7529da0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887ac2a0d1ef4673db39f2ef01181dc5a", "guid": "bfdfe7dc352907fc980b868725387e9808532f7928a735d6d78be776ef947ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32a4a8491e213b0e79d59d4718b632b", "guid": "bfdfe7dc352907fc980b868725387e9813c856dbc1a0cbb2bcf59f86d8cb62e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f39acf367640fd2941e7efee2b87de0", "guid": "bfdfe7dc352907fc980b868725387e984dfc93eb6e5396705df6ee6495247cdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245767a5d1712b9a766e19decc69eb9a", "guid": "bfdfe7dc352907fc980b868725387e982ae1890ab0299888fed60a4c5a07abbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de8c8bea2d106258ec71c67f86398cda", "guid": "bfdfe7dc352907fc980b868725387e988696500a466f0505d6738d2a049ba99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d626a67e6832a4346419ad38fb2e306", "guid": "bfdfe7dc352907fc980b868725387e98a5a8475572dd9248821c386389871832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d85a1b3eb765e5bbdc852f0bfc8c4ce", "guid": "bfdfe7dc352907fc980b868725387e982bc08241f85dad672fe328f48e642876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98783a4fcd2e18d6679035e45fc7900435", "guid": "bfdfe7dc352907fc980b868725387e98658562c361b501ae6a4e8c05a0b2bcc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a181bba386abf29cde19fa650e0a7d", "guid": "bfdfe7dc352907fc980b868725387e9885a6121db800c42d504084376a73e58a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e293cf00a5154c5e784895b39d99f113", "guid": "bfdfe7dc352907fc980b868725387e986f410f2625f031726b90b1d272600a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844498e638471d90403ed6bcb6833f533", "guid": "bfdfe7dc352907fc980b868725387e98d2cc385a245d646a8bf20ffb46ad51a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae03a6d1357308b452ab759ff51db60", "guid": "bfdfe7dc352907fc980b868725387e98783e39cf9bc0acc6d46ba60f604c52db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c96d16c5125f72ee288f4e357e4b7f", "guid": "bfdfe7dc352907fc980b868725387e98d423b598de04edfc48f06e1c8df82aec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fae34beca84fd057fc7825a23e59306", "guid": "bfdfe7dc352907fc980b868725387e98030f5c07efcec08ba8cc684d1874d011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf7473a268959968e60ae8734af0c71", "guid": "bfdfe7dc352907fc980b868725387e981454b79b1a8672c65bf5c588f00f3bee"}], "guid": "bfdfe7dc352907fc980b868725387e9853949a1dcd0119bfce805a18157935a6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9805ad083ec5da6d3716ba41f1bc25076a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98dc93aa3f9bc79bd26efdbc1e48476432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e9859d439315d94ca27d33022d96348d853"}], "guid": "bfdfe7dc352907fc980b868725387e985f369ca9bc1da9612aadf886c24a4132", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9844effa4978847f4f6b3fb7b706eb59c5", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9878ccdd00028dead59d200f2cbbf4cce9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}