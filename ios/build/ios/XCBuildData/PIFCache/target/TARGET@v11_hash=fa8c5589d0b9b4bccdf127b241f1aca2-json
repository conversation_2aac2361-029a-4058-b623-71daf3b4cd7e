{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980d4168d694b324f8259a8735bd5c96e0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98faed0b85bb69dec3b8dd8b6a39310504", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98702469fe259fe05e0f3efbabfc70d2e8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984198c389557aedc9f8d2acd7256149aa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98702469fe259fe05e0f3efbabfc70d2e8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e23345e7aecde7a76983eb50b08e6cef", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d697d4bc6b3dd40e26a93fda30660ba8", "guid": "bfdfe7dc352907fc980b868725387e98eaaa846235bbc2e5e3cbcaa9ac5b81f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c40fc0acc79a33a0acecd874dcc500", "guid": "bfdfe7dc352907fc980b868725387e9877519ccaa05183a3543a00cb364371ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf0f7210933ec0a671bb19a422fbf44", "guid": "bfdfe7dc352907fc980b868725387e983a81e7d21e6a1479fea1cfe607cab091", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f2fc93551ad12c5763ea6534bc5a292", "guid": "bfdfe7dc352907fc980b868725387e986e75a4a1021050cf7efee032437323f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2a8def4f22c6755b2dfbfa8c546867", "guid": "bfdfe7dc352907fc980b868725387e98d5d631b518c036c5988de060f6126cfa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986571ceaae78405c6c91a8e1125033fe8", "guid": "bfdfe7dc352907fc980b868725387e985a02b6350b3a87cef06e294768c8d6e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694763876b9b4c36b58c71e9c1a2b3e1", "guid": "bfdfe7dc352907fc980b868725387e983a6c3532e9231ff97669920bc7f12640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985701dfac76d802476b01175dcdcabc8b", "guid": "bfdfe7dc352907fc980b868725387e98c26a84454c216e6748741efce0788cd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e0e7abd279c7ba8d6d1efd33c9c5031", "guid": "bfdfe7dc352907fc980b868725387e98a1df68b095fbe7fb7cd33c01e7ef8fd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff51431f0f10a21a4c13543c4cd36ed", "guid": "bfdfe7dc352907fc980b868725387e98e3feef60a07be0525dcdec43a98d12e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20a8db8874dac6478864a155dfd230b", "guid": "bfdfe7dc352907fc980b868725387e98ed45a462faecc6472276b5e017437e60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a6e998e706cd279d4fb34e6a614cbc", "guid": "bfdfe7dc352907fc980b868725387e983b043ec97950b774496635ec98344e80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838026884133429fbb5ee0cfbab7cc53c", "guid": "bfdfe7dc352907fc980b868725387e98be5c98e307432d7f59ed60c6abd9ba49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0aab3387e2bbd89e4b964ae76b4ce31", "guid": "bfdfe7dc352907fc980b868725387e98071daceb07f3922dba267fb17840c63f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980df131dba57bdb727e6b072a56770d77", "guid": "bfdfe7dc352907fc980b868725387e988873360c4e5ff102b301ccaa2a93cf36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98390b0ca311e7a6b87e2adea68b2beec1", "guid": "bfdfe7dc352907fc980b868725387e98983df9506ad3783d193f25291fa3fa92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bc168084270755c5ac96f5802e6365a", "guid": "bfdfe7dc352907fc980b868725387e98b675b50ac9b9cf5a44548ac1af750cff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f158d902b4d9f06771b83a2676b4752", "guid": "bfdfe7dc352907fc980b868725387e981bd56fbc63ea0f6aa1d898eff263a2df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c55bbf82bd8c413abc8a59d3a243bf9a", "guid": "bfdfe7dc352907fc980b868725387e988974adba5ab6bd08324688ee0d4acd65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8fb39106f422d83a6d665695de614c5", "guid": "bfdfe7dc352907fc980b868725387e981e4e221a8d4e801041bec62e830308d8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc22bb472d66047e19cfae6ccb552660", "guid": "bfdfe7dc352907fc980b868725387e989215b515adbac73157168584415227b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d706f05bdf0dcb762c4da63b83dbbf35", "guid": "bfdfe7dc352907fc980b868725387e984e9b55c5f0df49c020a014bc8bae5136", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9871b6820f26707bf3c6e249f8f04a38a3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98883e5be1bd7107040abaef39371650f6", "guid": "bfdfe7dc352907fc980b868725387e9818e3c5194caf274484011b15f39b2703"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e177d46d36317e39870b22751f504ef", "guid": "bfdfe7dc352907fc980b868725387e984cd20cf864fd3e167d38ebb3b33bb237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53579730a4904f49806b5ee7480221b", "guid": "bfdfe7dc352907fc980b868725387e9847ed0274d8e9026f69d7252e84d49168"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a75ea77a9ea8aa1dbcf0ad7cd578e8", "guid": "bfdfe7dc352907fc980b868725387e9800c9f1dc0495c348bd5562c0abca5e97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5e295a3047d529ab766f55101404154", "guid": "bfdfe7dc352907fc980b868725387e984055613f208d9524fef2ac54071f0af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843c3e3e85f490e85a122472cb155e337", "guid": "bfdfe7dc352907fc980b868725387e98f100b543800a4d0d81455dde56f03279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6bd6a7c092de9e212c773823173dfe", "guid": "bfdfe7dc352907fc980b868725387e98a2e7e12a12060b3d2ac84d1287dedfc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98967334f74b32441b8b61da68fb0eab32", "guid": "bfdfe7dc352907fc980b868725387e9804eb5ad7a262c70fc65c9277365b232d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e146c5e7af0f4e7d6a05b8b9002404", "guid": "bfdfe7dc352907fc980b868725387e9804ee808a356b639d6b42803853cef277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f22473a61ed7683fa7380105368c12a", "guid": "bfdfe7dc352907fc980b868725387e980204733906ae9a695049f468c39f2667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad33f20ac301454c0bc8ab529200f05b", "guid": "bfdfe7dc352907fc980b868725387e98a3b512a8c2900345416388a5c3b51fa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7fb088be7e03fd4750fb23011589ad", "guid": "bfdfe7dc352907fc980b868725387e9835bb47108f37dec2d732b1c482728cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98406d58ef1dc3073a24f37383025cb721", "guid": "bfdfe7dc352907fc980b868725387e982fe32fdaa18371e700166255ad9f3ec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a31eab23d294cb27c166382c0697d37", "guid": "bfdfe7dc352907fc980b868725387e989fbf1746da6a23913ee196fd4169c652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee23682e3605f47b63bd0a9876948c8d", "guid": "bfdfe7dc352907fc980b868725387e9899c95f16e27f65ab9029e4c9cc4f0b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2fe174a725ff48a4f065b791d4112ec", "guid": "bfdfe7dc352907fc980b868725387e989c905f3edd511068f787077984da2aa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad21cb848e0ee6541363137cc5164bb", "guid": "bfdfe7dc352907fc980b868725387e98bbd04842aa705d5fb4c7f2efcf351948"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847dffdafc70f0364a3d0fd85ed86029f", "guid": "bfdfe7dc352907fc980b868725387e9890492609d9084d6be0b4347c463ff308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989654893177fabfdd8cfa0f2bcf71d410", "guid": "bfdfe7dc352907fc980b868725387e988a4b434a7e19d0d06536199af132c948"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988812de036e7d6eb81c6a104810636b0e", "guid": "bfdfe7dc352907fc980b868725387e98c0c57fb375471ff2a77a2f1ce7e5ca84"}], "guid": "bfdfe7dc352907fc980b868725387e989d0427b23a082611e95dfdd7698eafcc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9848075707c31ccf13e15c8d016c94236b"}], "guid": "bfdfe7dc352907fc980b868725387e98e5c981d4cbdc635695346e88559fa2f3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c0dd5cf36a7e82ee1ea452e8ceb2acab", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e985f4deb0b34077e4b530ff82ce464dfc8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}