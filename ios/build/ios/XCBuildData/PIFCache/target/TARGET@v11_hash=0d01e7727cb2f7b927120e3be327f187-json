{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9830d9b30eb5007ec38fc30f4b09c4b8ff", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98066a27ffaf2c86587451a1a938aaba3e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846a11597dcfb7210d0e7ea2b5ad0a4e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98195aaa409eccaec85df85577fad234a1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846a11597dcfb7210d0e7ea2b5ad0a4e3", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a1eb90d81ec81b775a34e08999e669ba", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9813cb4f047dd76d758faf7344baaddd49", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98630ffe657468d4d333da0dab3cb6bbb5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9891a6ad2d139a3c48541f5f5efed5b4b7", "guid": "bfdfe7dc352907fc980b868725387e98c9f6738480db81e1c59a64ddc85c72bf"}], "guid": "bfdfe7dc352907fc980b868725387e980c2e39b3a2d823d093cffd4ba7535b50", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981126092e527a43878ba047c0d6b5be37", "name": "FirebaseCore_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}