{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9830d9b30eb5007ec38fc30f4b09c4b8ff", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d4d4869866dfe1508d4ce853c7ac91d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846a11597dcfb7210d0e7ea2b5ad0a4e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d69c3eb41dd4436afd5a6ac77f4a961", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846a11597dcfb7210d0e7ea2b5ad0a4e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a2d962957610cd17d2d06d5324700f5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989ea32c3a793444fc1df0b72097034b34", "guid": "bfdfe7dc352907fc980b868725387e985c86d16d4740daea59a016526e93b819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32e92594599daab20e366bc8c8bc9c8", "guid": "bfdfe7dc352907fc980b868725387e980732ca92cd6b0558f6c2d14d836d4471", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0bf229ef2289df37883a0683e7ba19", "guid": "bfdfe7dc352907fc980b868725387e983b468d3cf58af461f2ddfce30d15a484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d604b0beb858a21c7c1f26d6642857", "guid": "bfdfe7dc352907fc980b868725387e9812a2eb5612cac3c06660e08028eaec7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d16301a443e68ab005195b5f11278407", "guid": "bfdfe7dc352907fc980b868725387e981c7e3177f6ba94a1539c4b46c4116be9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378c9632354764b1b3bcb215cf015f53", "guid": "bfdfe7dc352907fc980b868725387e9852be8566128bad9a38afea8fff1f4a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985962c69d5432b7fa53191f5b45121bb2", "guid": "bfdfe7dc352907fc980b868725387e983433ad446f6874b225001aa867458caa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d59f7024a44eedfb50dc3b51eb7b2c5", "guid": "bfdfe7dc352907fc980b868725387e98661e767f197e45ef5afbe24d0b567dd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e242758a0f3d1a3d8051a1def4095d44", "guid": "bfdfe7dc352907fc980b868725387e987cf64a4ca82482191bc1787d52a46d0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985500a2ca019d35eadd43945453a72425", "guid": "bfdfe7dc352907fc980b868725387e98a961e5a356b94504fe592f4d5bfed654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa1618ca28d6a5c5c4f308134ca08f12", "guid": "bfdfe7dc352907fc980b868725387e9855cf59e41b774c988bc52b1ae68768ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe2c7817f3a8dc03bfeada729805836", "guid": "bfdfe7dc352907fc980b868725387e98ca8aa90ef31f980cbd811d527a2819d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4e2bf3dd11d8657cedfd36d6a51407", "guid": "bfdfe7dc352907fc980b868725387e98eb0a05fe9fac3cff6c1c573e7265ee5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bcc3f0ed00cf263b6ccc65ff4234da7", "guid": "bfdfe7dc352907fc980b868725387e984e4c3d2e8ab9eea11c2ae5514ad12f3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c4511cddd28f3e9adbd865735a9f758", "guid": "bfdfe7dc352907fc980b868725387e9838cdc7245ea75deb096d0cf95eee837a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899b126ad5005f6e3db84d4239f8058dd", "guid": "bfdfe7dc352907fc980b868725387e9843b887b5aae78c1f9290d958551a79d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c563c27c3e4e680c2a78caf0b254f3", "guid": "bfdfe7dc352907fc980b868725387e98c2fe7faf08afb65ce7a581793bcaeaf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d95a6ead87b918eb2cd82b4bb1d209e8", "guid": "bfdfe7dc352907fc980b868725387e98a82a599721fc70576be425dc6ecc5cad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca515f23da51a99eb91673579d6c0f7", "guid": "bfdfe7dc352907fc980b868725387e9800843efc49ef44d4b7637a880907cee9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988256d97008eb88b2989d2b4b2a7a97f4", "guid": "bfdfe7dc352907fc980b868725387e986fe26b7583f0f6446eb32934a4449080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427d6988265b2b1d6275fe22a7c0126b", "guid": "bfdfe7dc352907fc980b868725387e9843996a7f0e5bd5743f26c2c46a1fe7cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988493745a72605973925e6e31b0a802d1", "guid": "bfdfe7dc352907fc980b868725387e98506c1b03005fa7be5be10b4163c01a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859265e82deb41b6408f7af5434b0c6c4", "guid": "bfdfe7dc352907fc980b868725387e9896db896a6b6bb8e07c2eb73cf9dca8ba", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9870c86885376b1939b65b59b21e8c879f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98176961f473e617a8bc61519e4cfafd6f", "guid": "bfdfe7dc352907fc980b868725387e985d8ad85f2d6d1640ce3919a215c43f7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988491df84204041139211f51ba179684b", "guid": "bfdfe7dc352907fc980b868725387e984dfaf239a9573a6f23f57976170f682d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93beb27025641e4204f14871fa3e0ad", "guid": "bfdfe7dc352907fc980b868725387e984ec51ba4197bccb1b387cba641184ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e3af5914210c450b241de60b2179f93", "guid": "bfdfe7dc352907fc980b868725387e986bc5159c15211d498e5bf10c5949210b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2261025a288344cb5e6430f5149e90d", "guid": "bfdfe7dc352907fc980b868725387e98efa3ea7ff773fc3012bb861ac34248b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d424598a0f12c1bd332a702d044c0ada", "guid": "bfdfe7dc352907fc980b868725387e984a130e6b5395f984961571be613ecf23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878876b469cb1f9796041519be4f77465", "guid": "bfdfe7dc352907fc980b868725387e983d42e48d8756840436bc1ddf34c3af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb0370b938cdac0e715158dc3e1d92e2", "guid": "bfdfe7dc352907fc980b868725387e98288dc43bea2c8e27564c61eccf48d2cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc5f75c79e1645dab1a4a4ee810eb1f", "guid": "bfdfe7dc352907fc980b868725387e9800a82147fc54771555132aedc613e02b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa83b7be616ff1128916cc1205ab3fb0", "guid": "bfdfe7dc352907fc980b868725387e988a98bdb6c19ed8f84913a81391f77523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f29cab49e347df0415525ca92b9697f1", "guid": "bfdfe7dc352907fc980b868725387e9825997c45ec5cc9d7a755198230abd6a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65376b3d5b02f42d5e8703fe07929fa", "guid": "bfdfe7dc352907fc980b868725387e98545b93bd2d8e7d74ac65368b4c73784d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989549e34a334b07dba0a1982be9288259", "guid": "bfdfe7dc352907fc980b868725387e988d7a942e5af284ffabe1c8fcbc6fa694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5301c31eb6486c4148ab4750b1e75a8", "guid": "bfdfe7dc352907fc980b868725387e982fb4ff3147f5834537ca1b706ba1ae08"}], "guid": "bfdfe7dc352907fc980b868725387e98f3b16c4c6386fd2037bd84016074ce6e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9844c3aaed3ac1e0d7bd41063b5ed4b2ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5192e7597d49b2508d02a0ca39a2058", "guid": "bfdfe7dc352907fc980b868725387e985bd85f227d38dc9232178ec0ef539205"}], "guid": "bfdfe7dc352907fc980b868725387e9822a64e207616839d9e9343ba3c806d3b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bd9ee1553d8700d76680b18195e52077", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98c8d6c2b7226d0bf55c20baa59dd2b147", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}