{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9b06a169d0f734a08395ebde92e5a3d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2306391936207ff3a77bf532265ba12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d6bd70c0c30f48b32619b2389e50ce1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98487ed0955f6ee94f3b149ed57f321128", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d6bd70c0c30f48b32619b2389e50ce1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f8dc2559c8635ba7bcf52e3f977ac5f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f74b35fa972ba2417a0efa513438a07", "guid": "bfdfe7dc352907fc980b868725387e98e2bd21820c90b1f41815129bf4bf9405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9eb5cd2290cb0a830bdb908060ac6aa", "guid": "bfdfe7dc352907fc980b868725387e986c17ed9ca53d14296e8c0881f6afcb5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818860cf4089ee35f7202a2e17446cce3", "guid": "bfdfe7dc352907fc980b868725387e98694a813cd054c22c482e4599329ff844", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a57c061eb9b2ac126fa51ca96b07d5", "guid": "bfdfe7dc352907fc980b868725387e985b3a8c4e58a7df6bd23638c5a4ca721d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc8c24eeb37bf07ad789cff51057819", "guid": "bfdfe7dc352907fc980b868725387e982585d2560ae64ea2b0ac16f50295bd34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3b47fadb2b4434e4d712a00616c6b8", "guid": "bfdfe7dc352907fc980b868725387e982ae6295bcac026ff9845dd0c5ab9542a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98628b66724fa81cfcaf6243c7d6cedafa", "guid": "bfdfe7dc352907fc980b868725387e98ae96daec96ffbfc25a026c67ba591604", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e3f80db82d184dba63d410cc510cce4", "guid": "bfdfe7dc352907fc980b868725387e982073dd99eb233240c8ba8f146f65e712", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3e3b40955364359e29ff58a0f7fcb6", "guid": "bfdfe7dc352907fc980b868725387e98167f03da8c3de9caa0f3bf63ff28a24b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2445c99d2e94473143052602ac361e", "guid": "bfdfe7dc352907fc980b868725387e98ff8bbdaff575c2d33fc4b4a877fb12e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989afd61a4043de550874fef19acc49325", "guid": "bfdfe7dc352907fc980b868725387e983070cc21ebc803eab062c67de7dff795", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2f90e6343b90ad1bcdd16391609d7d", "guid": "bfdfe7dc352907fc980b868725387e98178f798afeb40b54ea8662c68ddfeb10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988597f36efb37df65b26570086354c1f4", "guid": "bfdfe7dc352907fc980b868725387e9816a68341387b4a6baf52d3ae5ff5bea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc9946e676450abfb8230f0cf84b724", "guid": "bfdfe7dc352907fc980b868725387e98814d833ece647414ba02ec6ef4696976", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b667bd62a022f7302479e74d7f3b89fb", "guid": "bfdfe7dc352907fc980b868725387e982f1b6d6c3e214bcf71d983f14c3d74bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989daa79ba156b851ae14af46906b3a0a9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b60db71efedf3ea6c8549441e3fe217", "guid": "bfdfe7dc352907fc980b868725387e980f17e817e499929885dee4313deb21d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f8dca75e17886d8dea085d88c90e02", "guid": "bfdfe7dc352907fc980b868725387e98951d9613b201b8f7eddb290425ee8e5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c078003e751eb360d154f6136ada35be", "guid": "bfdfe7dc352907fc980b868725387e982fa6d7fed276a67d593a8153d22c33db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850cd73bdd6e1a383d81b559e2c9aa8c9", "guid": "bfdfe7dc352907fc980b868725387e982428b98c63c39fa7782792d997812199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e7bc730e0df28afe160c7e825b56a4a", "guid": "bfdfe7dc352907fc980b868725387e98e93c0029f238365dee57e1a90c3e6a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829897479750d00a9f103f43718d89404", "guid": "bfdfe7dc352907fc980b868725387e986d40de42c560db1b8fa41d0761cd7dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d2d7abbe6cb787ded0eb36eb126362", "guid": "bfdfe7dc352907fc980b868725387e983f779cf18eadc28e77c18d3ea3c8cdbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff6861ca03b52ff7b64fd15a66959c5", "guid": "bfdfe7dc352907fc980b868725387e984d2884e377032fd71cd7e3939400ac20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879da6fd771f329faa8438226a39ef1b7", "guid": "bfdfe7dc352907fc980b868725387e98e1955fc3138908e466c830c3aaf88c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b29335822bcfa8dc7627f0b49110ab", "guid": "bfdfe7dc352907fc980b868725387e98183e12f37254189a854de2e26e5ef3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a8dd140fbd27bf89bfff23d4c29431", "guid": "bfdfe7dc352907fc980b868725387e98c67f7ccd4f0daa66adcf204f9f79f507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e6576fe681bef7391c55d90c81ef16a", "guid": "bfdfe7dc352907fc980b868725387e984a1a2774f6b2777dc237bb18ccd2b00f"}], "guid": "bfdfe7dc352907fc980b868725387e98badc07441c3d3f800ea824e69bb5beff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98e3f6bfa9b742e772ecd92d7503f55e33"}], "guid": "bfdfe7dc352907fc980b868725387e98d7f82e88a905b7e353a4e6f9718eb7f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cc8ac38168987ce84f85d8beb6728369", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98558074b4aea693c85351e88b3b5ba2ae", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}