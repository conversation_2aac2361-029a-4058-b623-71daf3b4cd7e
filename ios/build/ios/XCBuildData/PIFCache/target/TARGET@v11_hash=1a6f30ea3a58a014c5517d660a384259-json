{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9853434e915d55eed0d5ab8e8cab0c4b1a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843f0dc49602f33cb4a7e8e390ca6f429", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843f0dc49602f33cb4a7e8e390ca6f429", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882ac1863041d16870115c72185b99811", "guid": "bfdfe7dc352907fc980b868725387e980eb9d23368b95b4f0923cd46a43136d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224de6f11ff7c8d8ceaff0ea1421ce0e", "guid": "bfdfe7dc352907fc980b868725387e98f457cef344e960dcd5e86a14876a724f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98861e294c263ad4870f40839c9b77e09b", "guid": "bfdfe7dc352907fc980b868725387e98448d0ae29de8f3c224c2dcfdec1456de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d001279bfc216ac9aa665ef010f0680", "guid": "bfdfe7dc352907fc980b868725387e98813b7079aa7de27e6db954b27ad09619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd6dc1a967f0457e0910c1c78d437c7", "guid": "bfdfe7dc352907fc980b868725387e981ea8f363f0722babcaed14c60409bf02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875075dd87e352453c3d56fa11053f9d3", "guid": "bfdfe7dc352907fc980b868725387e98e7ac745c3ef27b02fac012aa73444821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c74534dd3c2ecbad109a1658ebbd124", "guid": "bfdfe7dc352907fc980b868725387e985678ecb94e589405999c12157a408775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa87a66e66a4e31c1cd7c4d7f1ca5ca3", "guid": "bfdfe7dc352907fc980b868725387e98cd87b467ebfeb07dfeb1385499e6cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a791676e0ba7c5aa63e913f640870d12", "guid": "bfdfe7dc352907fc980b868725387e9871c163114fc9cc28ecff5b1b2d0a4de2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee88d3da0459dfb23226e3e8c87b828d", "guid": "bfdfe7dc352907fc980b868725387e981978a187afff967da6262086dc0e6fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861834f573981aa7336166cbc18b3d8eb", "guid": "bfdfe7dc352907fc980b868725387e9826c907de3610a0743fa9d834fe3a26ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a6c2a0f66cd5ed3c10aa8e74c6415a4", "guid": "bfdfe7dc352907fc980b868725387e980aab54a36f4355218c04e0f9e0c22709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ae4606026969347ef6d8b5f9962b1a", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cef1cafaba762d76e8fae707440933d6", "guid": "bfdfe7dc352907fc980b868725387e98d24acab5f9f5227c374c09fc08c9e695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803850e08567416bc914583f856eff2a4", "guid": "bfdfe7dc352907fc980b868725387e98eed3f758e63ab4fdfc010074b9de804b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832232f6a5eb1057744160a17958b220f", "guid": "bfdfe7dc352907fc980b868725387e981f08693bd23a6d005b3df15e8e699fde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699c03c46c4fa1b5c63bb536bfe21536", "guid": "bfdfe7dc352907fc980b868725387e98bf1362e319e49dc96debe2ff1acb9025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bac742ca53653119e106b76b360f5f71", "guid": "bfdfe7dc352907fc980b868725387e985b2090fddc5dd999ec75f69196ebf8ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ab0e1c32731cefb89834caacf634fe0", "guid": "bfdfe7dc352907fc980b868725387e98232fcc544ef53e9da9ad40243b28e5a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad26b9c6a8e1996edd712be765c7596f", "guid": "bfdfe7dc352907fc980b868725387e98a63597065bd83c36166c1f6aeac7172a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836787fc887938b91d4160c55d96f761a", "guid": "bfdfe7dc352907fc980b868725387e9829db91ca625511289316787d6d10ea44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddf915ca6efe0c7d9483083bedaefbb", "guid": "bfdfe7dc352907fc980b868725387e98c0b06c2b33df0dfa02bfa09f9932011c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9c6a1f1aee64152072c9ab92665368", "guid": "bfdfe7dc352907fc980b868725387e9851ba8fa6ddb817e605d2665626d9d641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895e8ae84abac15550f9ffbbc3bc6879e", "guid": "bfdfe7dc352907fc980b868725387e98277468ca209872376e5f206f84332670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888fdc53ca017cabb5cd633bd83ad8ce2", "guid": "bfdfe7dc352907fc980b868725387e98e9a6f3c2565cf9a05dd3d353eaa7a4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7bf0755d3226ae896ebdd9d3c769826", "guid": "bfdfe7dc352907fc980b868725387e986111772b572857d7961a008265fc355c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25287a31ba0a8d9ded75413bdb5b195", "guid": "bfdfe7dc352907fc980b868725387e983996f9c15e1fbf682fcc40770fd256d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa099a670057f9de2fcd0f87b299330f", "guid": "bfdfe7dc352907fc980b868725387e9858a8497e775e00416ce705748e767ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0f3229f0db27fa1fb577fdf098be67", "guid": "bfdfe7dc352907fc980b868725387e98c6bee8141090191321d7771c3a1f978b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e222ef4fbdbb26b6fb9eb234a1449d7a", "guid": "bfdfe7dc352907fc980b868725387e981cebb2289f1bb2bf7dee62eea371ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982934d2a43f0390a9b5276e4e80a100cf", "guid": "bfdfe7dc352907fc980b868725387e98f1f6bd36d03eee3c0bdc3bc98e83ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc15907e0aadef9520acefd71a64558", "guid": "bfdfe7dc352907fc980b868725387e98a32f894597a07a2a5504a8b9bcfdabdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b42b65591b980ef60de99e9f65de6e1", "guid": "bfdfe7dc352907fc980b868725387e9800ac44543ad413e32dda38d808c2afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847c54a0f0d6bba0f18c0b65dfa895d42", "guid": "bfdfe7dc352907fc980b868725387e980a3b558904dbc56f2153ae21439d6437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e29bae99a17081778bf183e638ad90c2", "guid": "bfdfe7dc352907fc980b868725387e9803ab6e9df07f67a9975e5c61b861ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e3daed9be09f4465f0f134a72ab2a4", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb9335f1ad3bcfa90c4e2f98f8934adb", "guid": "bfdfe7dc352907fc980b868725387e984a502a85d82ab7ae770f404ac3bb3b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852baac0bad863d04b53e5a7e6441dcb1", "guid": "bfdfe7dc352907fc980b868725387e984c79dfb7fcb3ff07f9037801d36c7134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a718b330cd68956e53be1e58e39fbfa", "guid": "bfdfe7dc352907fc980b868725387e98832efc148e497d7aa81cf3800da27235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc96ad6953625a81ef0ac3785c26acc", "guid": "bfdfe7dc352907fc980b868725387e988b056d871bf720cc83d9b2c7291eec07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988066f135326517baf2d5777269055005", "guid": "bfdfe7dc352907fc980b868725387e98afb0626ba205a690e15081763b4adef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131f828ee746c104eab5c385fc14e286", "guid": "bfdfe7dc352907fc980b868725387e98cf6232b3e1482ebfb128206cef66a17c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ddf134521fbb97b5f4852e983e9c4c", "guid": "bfdfe7dc352907fc980b868725387e98c76565777e1dccbddf6eca764d227a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde79ee028090453f6141ea2f8a4c4c5", "guid": "bfdfe7dc352907fc980b868725387e98dba12151ab617cdd659014f8477b0bfb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}