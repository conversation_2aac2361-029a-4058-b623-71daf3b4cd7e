{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983563d4ea4246da758e6f0790d2b4e57d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b35808274e52f55f37b98a9c2d6c5f4a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f8c63dc474cd530763d59b3c6a8c10", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a8642fc4cbef999ba697c7429dbb733", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9834f8c63dc474cd530763d59b3c6a8c10", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850e4a542d0204c3f38e88e154975e8da", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8d0f4c69f70221820cc2186cf9a2205", "guid": "bfdfe7dc352907fc980b868725387e98e71626cbf5be270de2595c2bf6c23750", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9869a9badca160455bbaba51c624284b56", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98037fee5576f6450572aa858e8a9f8e4c", "guid": "bfdfe7dc352907fc980b868725387e9876e5c80b6baeea1ec4c13881b339c795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd951d95d2f9de80a2a840b9ff10819", "guid": "bfdfe7dc352907fc980b868725387e98ca0d187577200c22932f971a04aef21c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984355162bde268cafdf4677c926bdeb01", "guid": "bfdfe7dc352907fc980b868725387e989659814a1b91723eaf35ae15c6f27173"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335521a96f60a2f5207576a3f1c19248", "guid": "bfdfe7dc352907fc980b868725387e98395aeae9705e0f7a4d5dc160796fed99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983328c9a537cc3d648b4bf5ce1bdc60c2", "guid": "bfdfe7dc352907fc980b868725387e98cf5e475857f9fd8791d92de7b0ea3a3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa5812e642f4f293bfab9e236ac08098", "guid": "bfdfe7dc352907fc980b868725387e98d2f98d11a57ef4c287c9ae26369aea77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c9ae0b6bdb2f70a320b3f41c5dc74f", "guid": "bfdfe7dc352907fc980b868725387e98da030007f00e17d65074486b2eb25ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ebd54847c240b805dbad738c0054c21", "guid": "bfdfe7dc352907fc980b868725387e9893af8c57faa69f397563e4518e7f6dce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a390a9879d19bfac4996b9fd1a83c37b", "guid": "bfdfe7dc352907fc980b868725387e980b60f0fa34444f99f9f5607f30847276"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889545dd1a8abedfcb84c1e185f3021e8", "guid": "bfdfe7dc352907fc980b868725387e989fa7124309b23063738b119258812ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98695556d179e7b7d1c0afe66824df77ef", "guid": "bfdfe7dc352907fc980b868725387e9852152e6229fafe3f66022f1eb80f2a23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1ccc908dfb658f0873d84bba76d0906", "guid": "bfdfe7dc352907fc980b868725387e98029159e7a6261536e46681091cce9b83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197303117091b9c926500c4add58a3ec", "guid": "bfdfe7dc352907fc980b868725387e9894f234c9416a9b1dc0ef7a1451c73dd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fffd35b4126d7ac03d4764039223ae6b", "guid": "bfdfe7dc352907fc980b868725387e9880fbe880ba569a4d65673b976364fb89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449071d600720b472ac0cd13982bdf35", "guid": "bfdfe7dc352907fc980b868725387e98ecf12139ee697d03062363a45e467df0"}], "guid": "bfdfe7dc352907fc980b868725387e98b17a5956896cedb629e21f5b6a6df88a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98b8a584ec43068426d89a18b82561c21a"}], "guid": "bfdfe7dc352907fc980b868725387e9873845fb969af2957842bc4a06b493df1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984c5e3bc28b23da145ea49cca3c6e2b01", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a5accfb02561b5d57f3004cdb74f0a8b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}