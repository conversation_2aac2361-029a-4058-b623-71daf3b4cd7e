name: kurdsat
description: "Kurdsat TV Channel Application"
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # view
  flutter_riverpod: ^2.0.2
  flutter_screenutil: ^5.9.3
  loading_animation_widget: ^1.3.0
  flutter_svg: ^2.0.17
  cupertino_icons: ^1.0.8
  pull_down_button: ^0.10.2

  # dart
  equatable: ^2.0.7
  json_annotation: ^4.9.0
  freezed_annotation: ^2.4.4
  dartz: ^0.10.1

  # device
  path_provider: ^2.1.5
  permission_handler: ^11.4.0

  # Data
  dio: ^5.8.0+1
  cached_network_image: ^3.4.1
  flutter_dotenv: ^5.2.1
  shared_preferences: ^2.5.3

  # Utilities
  easy_localization: ^3.0.7+1
  go_router: ^15.1.3

  # Firebase
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0
  flutter_local_notifications: ^19.4.0
  firebase_analytics: ^12.0.0
  firebase_app_check: ^0.4.0
  firebase_crashlytics: ^5.0.0

  # state management + DI
  get_it: ^8.0.3
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  intl: ^0.20.2
  carousel_slider: ^5.1.1
  share_plus: ^11.0.0
  flutter_html: ^3.0.0

  # Media playback
  video_player: ^2.8.6
  just_audio: ^0.9.39
  shimmer: ^3.0.0
  webview_flutter: ^4.13.0

dev_dependencies:
  flutter_launcher_icons: ^0.14.1
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # analysis
  flutter_lints: ^5.0.0
  pretty_dio_logger: ^1.3.1

  # testing
  mockito: ^5.4.4

  # generators
  json_serializable: ^6.9.3
  freezed: ^2.5.8
  build_runner: ^2.4.14

flutter_native_splash:
  color: "#ffffff"
  image: assets/logo/kurdsat_app_icon.png
  android: true
  ios: true

flutter_icons:
  android: true
  ios: true
  image_path: "assets/logo/kurdsat_app_icon.png"

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/logo/
    - assets/translations/
    - assets/images/illustrations/
    - assets/images/icons/
    - assets/icons/
    - assets/tv_logos/
    - assets/illustrations/
    - assets/setting/icons/
    - assets/setting/channels_icon/
    - assets/setting/socials/
    - .env

  fonts:
    - family: Rabar013
      fonts:
        - asset: assets/fonts/Rabar_013.ttf
    - family: Rabar015
      fonts:
        - asset: assets/fonts/Rabar_015.ttf
    - family: Titillium
      fonts:
        - asset: assets/fonts/Titillium_Regular.ttf
